<?php

use App\Http\Controllers\Admin\ActivityController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\BookingController;
use App\Http\Controllers\Admin\CommissionController;
use App\Http\Controllers\Admin\CustomizedTripController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DestinationController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\FaqGroupController;
use App\Http\Controllers\Admin\MessageController;
use App\Http\Controllers\Admin\PackageController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\TravelGuideController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;

Route::group(['middleware' => ['inertia_handler:auth', 'guest']], function () {
    Route::group(['middleware' => 'guest'], function () {
        Route::group(['as' => 'admin.'], function () {
            Route::get('login', [AuthenticatedSessionController::class, 'create'])->name('login');
            Route::post('login', [AuthenticatedSessionController::class, 'store']);
        });
    });
});

Route::group(['middleware' => ['inertia_handler:admin'/* , 'auth' */], 'as' => 'admin.'], function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Settings
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
    Route::get('/settings/home-page', [\App\Http\Controllers\Admin\SettingController::class, 'homePageSettings'])->name('settings.home-page');
    Route::put('/settings/{group}', [\App\Http\Controllers\Admin\SettingController::class, 'updateGroup'])->name('settings.updateGroup');

    // TinyMCE image upload endpoint (stateless API, no CSRF required)
    Route::post('/uploads/tinymce', [\App\Http\Controllers\Admin\UploadController::class, 'tinymce']);

    // User management routes
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
    Route::post('/users', [UserController::class, 'store'])->name('users.store');
    Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update');
    Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');

    // Destination management routes
    Route::get('/destinations', [DestinationController::class, 'index'])->name('destinations.index');
    Route::post('/destinations', [DestinationController::class, 'store'])->name('destinations.store');
    Route::put('/destinations/{destination}', [DestinationController::class, 'update'])->name('destinations.update');
    Route::delete('/destinations/{destination}', [DestinationController::class, 'destroy'])->name('destinations.destroy');

    // Activity management routes
    Route::get('/activities', [ActivityController::class, 'index'])->name('activities.index');
    Route::post('/activities', [ActivityController::class, 'store'])->name('activities.store');
    Route::put('/activities/{activity}', [ActivityController::class, 'update'])->name('activities.update');
    Route::delete('/activities/{activity}', [ActivityController::class, 'destroy'])->name('activities.destroy');

    // Package management routes
    Route::resource('packages', PackageController::class);

    // Post management routes
    Route::resource('pages', PageController::class);
    Route::resource('blogs', BlogController::class);
    Route::resource('travel-guides', TravelGuideController::class);

    Route::resource('testimonials', TestimonialController::class);
    Route::resource('travel-experience-videos', \App\Http\Controllers\Admin\TravelExperienceVideoController::class);

    // FAQ Group management routes
    Route::get('/faq-groups', [FaqGroupController::class, 'index'])->name('faq-groups.index');
    Route::post('/faq-groups', [FaqGroupController::class, 'store'])->name('faq-groups.store');
    Route::put('/faq-groups/{faqGroup}', [FaqGroupController::class, 'update'])->name('faq-groups.update');
    Route::delete('/faq-groups/{faqGroup}', [FaqGroupController::class, 'destroy'])->name('faq-groups.destroy');

    // FAQ management routes
    Route::get('/faqs', [FaqController::class, 'index'])->name('faqs.index');
    Route::post('/faqs', [FaqController::class, 'store'])->name('faqs.store');
    Route::put('/faqs/{faq}', [FaqController::class, 'update'])->name('faqs.update');
    Route::delete('/faqs/{faq}', [FaqController::class, 'destroy'])->name('faqs.destroy');

    // Message management routes
    Route::get('/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::delete('/messages/contact/{contactMessage}', [MessageController::class, 'destroyContact'])->name('messages.destroyContact');
    Route::delete('/messages/question/{askedQuestion}', [MessageController::class, 'destroyQuestion'])->name('messages.destroyQuestion');

    // Booking management routes
    Route::get('/bookings', [BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/{booking}', [BookingController::class, 'show'])->name('bookings.show');
    Route::delete('/bookings/{booking}', [BookingController::class, 'destroy'])->name('bookings.destroy');
    Route::put('/bookings/{booking}/status', [BookingController::class, 'updateStatus'])->name('bookings.updateStatus');

    // Customized Trip management routes
    Route::get('/customized-trips', [CustomizedTripController::class, 'index'])->name('customized-trips.index');
    Route::get('/customized-trips/{customizedTrip}', [CustomizedTripController::class, 'show'])->name('customized-trips.show');
    Route::put('/customized-trips/{customizedTrip}/status', [CustomizedTripController::class, 'updateStatus'])->name('customized-trips.update-status');
    Route::delete('/customized-trips/{customizedTrip}', [CustomizedTripController::class, 'destroy'])->name('customized-trips.destroy');

    // Commission management routes
    Route::get('/commissions', [CommissionController::class, 'index'])->name('commissions.index');
    Route::get('/commissions/{commission}', [CommissionController::class, 'show'])->name('commissions.show');
    Route::put('/commissions/{commission}/status', [CommissionController::class, 'updateStatus'])->name('commissions.updateStatus');
    Route::delete('/commissions/{commission}', [CommissionController::class, 'destroy'])->name('commissions.destroy');
});
