<?php

use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['inertia_handler'], 'as' => 'front.'], function () {
    Route::get('/', [App\Http\Controllers\IndexController::class, 'index'])->name('home');
    Route::get('/contact', [App\Http\Controllers\IndexController::class, 'contact'])->name('contact');
    Route::post('/contact', [App\Http\Controllers\ContactController::class, 'submit'])->name('contact.submit');
    Route::get('/customized-trip', [App\Http\Controllers\TripController::class, 'customizedTrip'])->name('customized-trip');
    Route::post('/customized-trip', [App\Http\Controllers\TripController::class, 'storeCustomizedTrip'])->name('customized-trip.submit');

    Route::get('/destinations', [App\Http\Controllers\TripController::class, 'destinations'])->name('destinations');
    Route::get('/activities/{destination}', [App\Http\Controllers\TripController::class, 'activities'])->name('activities');
    Route::get('/activities/{destination}/{category}', [App\Http\Controllers\TripController::class, 'activityCategory'])->name('activities.category');
    Route::get('/packages', [App\Http\Controllers\TripController::class, 'allPackages'])->name('packages.search');
    Route::get('/packages/{destination}/{category}', [App\Http\Controllers\TripController::class, 'packages'])->name('packages');
    Route::get('/packages/{destination}/{category}/{slug}', [App\Http\Controllers\TripController::class, 'package'])->name('packages.show');

    // Booking routes
    Route::get('/booking/{package}', [App\Http\Controllers\BookingController::class, 'create'])->name('booking.create');
    Route::post('/booking', [App\Http\Controllers\BookingController::class, 'store'])->name('booking.store');

    Route::get('/blogs', [App\Http\Controllers\BlogController::class, 'index'])->name('blogs');
    Route::get('/blog/{slug}', [App\Http\Controllers\BlogController::class, 'show'])->name('blog.show');

    // Static routes (can be replaced with dynamic pages)
    Route::get('/travel-guide', [App\Http\Controllers\IndexController::class, 'travelGuide'])->name('travel-guide');
    Route::get('/company-page', [App\Http\Controllers\IndexController::class, 'samplePage']);
    // Route::get('/about', [App\Http\Controllers\IndexController::class, 'about'])->name('about');
    // Route::get('/privacy', [App\Http\Controllers\IndexController::class, 'privacy']);
    // Route::get('/terms', [App\Http\Controllers\IndexController::class, 'terms']);
    Route::get('/faqs', [App\Http\Controllers\IndexController::class, 'faqs']);

    Route::get('/{slug}', [App\Http\Controllers\PageController::class, 'show'])->name('page');
});
