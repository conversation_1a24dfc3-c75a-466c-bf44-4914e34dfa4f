{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@inertiajs/react": "^2.0.0", "@popperjs/core": "^2.11.8", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@splidejs/react-splide": "^0.7.12", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-table": "^8.21.3", "@tinymce/tinymce-react": "^6.3.0", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.0.1", "daisyui": "^5.0.43", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.12", "globals": "^15.14.0", "html-react-parser": "^5.2.6", "laravel-vite-plugin": "^1.0", "lucide-react": "^0.539.0", "next-themes": "^0.4.6", "preline": "^3.1.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-select": "^5.10.2", "sonner": "^2.0.7", "swiper": "^11.2.8", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "vanilla-calendar-pro": "^3.0.4", "vite": "^6.0", "yup": "^1.7.0", "zod": "^4.0.16"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}