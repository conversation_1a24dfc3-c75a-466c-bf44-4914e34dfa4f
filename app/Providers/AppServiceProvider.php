<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        try {
            $emailSetting = settings_group('email');

            // set smtp configuration from settings
            config(['mail.mailers.smtp' => [
                'transport' => 'smtp',
                'host' => $emailSetting['smtp_host'],
                'port' => $emailSetting['smtp_port'],
                'username' => $emailSetting['smtp_username'],
                'password' => $emailSetting['smtp_password'],
                'encryption' => $emailSetting['smtp_encryption'],
            ]]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
