<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Activity extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'activity_id',
    ];

    public function parentActivity(): BelongsTo
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    public function subActivities(): HasMany
    {
        return $this->hasMany(Activity::class, 'activity_id');
    }

    public function destinations(): BelongsToMany
    {
        return $this->belongsToMany(Destination::class, 'activity_destination');
    }

    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 'activity_package');
    }
}
