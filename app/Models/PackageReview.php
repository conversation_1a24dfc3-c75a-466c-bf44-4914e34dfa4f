<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PackageReview extends Model
{
    protected $fillable = [
        'package_id',
        'rating',
        'comment',
        'reviewer_name',
        'reviewer_email',
        'reviewer_phone',
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
