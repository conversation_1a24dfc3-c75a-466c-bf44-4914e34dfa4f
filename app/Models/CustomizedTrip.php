<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomizedTrip extends Model
{
    use HasFactory;
    protected $fillable = [
        'full_name',
        'email',
        'phone',
        'country_id',
        'package_id',
        'travel_date',
        'trip_duration',
        'number_of_adults',
        'number_of_children',
        'estimated_budget',
        'notes',
        'status',
    ];

    protected $casts = [
        'travel_date' => 'date',
        'estimated_budget' => 'decimal:2',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
