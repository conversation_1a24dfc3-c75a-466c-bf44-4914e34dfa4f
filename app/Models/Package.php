<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'destination_id',
        'base_price',
        'location',
        'duration',
        'route_map',
        'add_to_slider',
        'is_featured',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'add_to_slider' => 'boolean',
        'is_featured' => 'boolean',
    ];

    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class);
    }

    public function attributes(): HasMany
    {
        return $this->hasMany(PackageAttribute::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(PackagePrice::class);
    }

    public function media(): HasMany
    {
        return $this->hasMany(PackageMedia::class);
    }

    public function plans(): HasMany
    {
        return $this->hasMany(PackagePlan::class);
    }

    public function costDetails(): HasMany
    {
        return $this->hasMany(PackageCostDetail::class);
    }

    public function faqs(): HasMany
    {
        return $this->hasMany(PackageFaq::class);
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(PackageReview::class);
    }

    public function activities(): BelongsToMany
    {
        return $this->belongsToMany(Activity::class, 'activity_package');
    }

    public function customizedTrips(): HasMany
    {
        return $this->hasMany(CustomizedTrip::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }
}
