<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Cache key prefix for settings
     */
    const CACHE_STORE = 'settings';
    const CACHE_TTL = 3600; // 1 hour

    /**
     * Get the settings cache store
     */
    public static function cache()
    {
        return Cache::store(self::CACHE_STORE);
    }

    /**
     * Get a setting value with automatic type casting and caching
     */
    public static function get(string $key, $default = null)
    {
        return self::cache()->remember($key, self::CACHE_TTL, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return self::castValue($setting->value, $setting->type);
        });
    }

    /**
     * Set a setting value with group and data type
     */
    public static function set(string $key, $value, string $group = 'general', string $type = 'string')
    {
        // Store the raw value (will be cast when retrieved)
        $rawValue = self::prepareValueForStorage($value, $type);

        $result = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $rawValue,
                'group' => $group,
                'type' => $type
            ]
        );

        // Clear cache after successful database operation
        self::cache()->forget($key);
        self::cache()->forget(self::groupKey($group));

        return $result;
    }

    /**
     * Get all settings for a specific group
     */
    public static function getGroup(string $group): array
    {
        $cacheKey = self::groupKey($group);

        return self::cache()->remember($cacheKey, self::CACHE_TTL, function () use ($group) {
            $settings = static::where('group', $group)->get();
            $result = [];

            foreach ($settings as $setting) {
                $result[$setting->key] = self::castValue($setting->value, $setting->type);
            }

            return $result;
        });
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        self::cache()->flush();
    }

    public static function groupKey(string $group): string
    {
        return "group.$group";
    }

    /**
     * Cast value based on type
     */
    protected static function castValue($value, string $type)
    {
        return match ($type) {
            'integer' => (int) $value,
            'float' => (float) $value,
            'boolean' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
            'array', 'json' => json_decode($value, true),
            'date' => Carbon::parse($value)->toDateString(),
            'datetime' => Carbon::parse($value),
            default => $value,
        };
    }

    /**
     * Prepare value for storage based on type
     */
    protected static function prepareValueForStorage($value, string $type): string
    {
        return match ($type) {
            'boolean' => $value ? '1' : '0',
            'array', 'json' => json_encode($value),
            'date', 'datetime' => $value instanceof Carbon ? $value->toDateTimeString() : Carbon::parse($value)->toDateTimeString(),
            default => (string) $value,
        };
    }

    /**
     * Boot method to clear cache when model is updated
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function (self $setting) {
            self::cache()->forget($setting->key);
            self::cache()->forget(self::groupKey($setting->group));
        });

        static::deleted(function (self $setting) {
            self::cache()->forget($setting->key);
            self::cache()->forget(self::groupKey($setting->group));
        });
    }

    public function getDecodedValueAttribute()
    {
        return self::castValue($this->value, $this->type);
    }

    public static function setMany(string $group, array $data): void
    {
        foreach ($data as $key => $val) {
            // Normalize value to string; arrays/objects as JSON
            $value = is_array($val) || is_object($val)
                ? json_encode($val)
                : (string) ($val === null ? '' : $val);

            static::query()->updateOrCreate(
                ['key' => $key],
                ['value' => $value, 'group' => $group]
            );
        }
    }
}
