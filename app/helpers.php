<?php

use App\Models\Setting;
use Illuminate\Database\Eloquent\Model;

if (!function_exists('setting')) {
    /**
     * Get a setting value with optional default
     */
    function setting(string $key, $default = null)
    {
        return Setting::get($key, $default);
    }
}

if (!function_exists('settings_group')) {
    /**
     * Get all settings for a specific group
     */
    function settings_group(string $group): array
    {
        return Setting::getGroup($group);
    }
}

if (!function_exists('set_setting')) {
    /**
     * Set a setting value
     */
    function set_setting(string $key, mixed $value, string $group = 'general', string $type = 'string'): Model|Setting
    {
        return Setting::set($key, $value, $group, $type);
    }
}
