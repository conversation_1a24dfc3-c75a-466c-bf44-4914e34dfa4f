<?php

namespace App\Console\Commands;

use App\Models\Setting;
use Illuminate\Console\Command;

class TestSettingsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the enhanced Setting model functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Enhanced Setting Model');
        $this->line('================================');

        // Test individual settings with type casting
        $this->info('1. Testing individual settings with type casting:');

        $siteName = Setting::get('site_name');
        $this->line("Site Name (string): {$siteName}");

        $maxBookings = Setting::get('max_bookings_per_day');
        $this->line("Max Bookings (integer): {$maxBookings} (type: " . gettype($maxBookings) . ")");

        $commissionRate = Setting::get('default_commission_rate');
        $this->line("Commission Rate (float): {$commissionRate} (type: " . gettype($commissionRate) . ")");

        $maintenanceMode = Setting::get('maintenance_mode');
        $this->line("Maintenance Mode (boolean): " . ($maintenanceMode ? 'true' : 'false') . " (type: " . gettype($maintenanceMode) . ")");

        $paymentMethods = Setting::get('payment_methods');
        $this->line("Payment Methods (array): " . implode(', ', $paymentMethods) . " (type: " . gettype($paymentMethods) . ")");

        $this->line('');

        // Test group-based retrieval
        $this->info('2. Testing group-based retrieval:');

        $contactSettings = Setting::getGroup('contact');
        $this->line("Contact Settings:");
        foreach ($contactSettings as $key => $value) {
            $this->line("  - {$key}: {$value}");
        }

        $this->line('');

        $socialSettings = Setting::getGroup('social_media');
        $this->line("Social Media Settings:");
        foreach ($socialSettings as $key => $value) {
            $this->line("  - {$key}: {$value}");
        }

        $this->line('');

        // Test caching
        $this->info('3. Testing caching performance:');

        $start = microtime(true);
        $value1 = Setting::get('site_name');
        $time1 = microtime(true) - $start;

        $start = microtime(true);
        $value2 = Setting::get('site_name');
        $time2 = microtime(true) - $start;

        $this->line("First call (database): " . number_format($time1 * 1000, 2) . "ms");
        $this->line("Second call (cache): " . number_format($time2 * 1000, 2) . "ms");
        $this->line("Cache speedup: " . number_format($time1 / $time2, 1) . "x faster");

        $this->line('');

        // Test setting new values
        $this->info('4. Testing setting new values:');

        Setting::set('test_string', 'Hello World', 'test', 'string');
        Setting::set('test_integer', 42, 'test', 'integer');
        Setting::set('test_boolean', true, 'test', 'boolean');
        Setting::set('test_array', ['apple', 'banana', 'cherry'], 'test', 'array');

        $testSettings = Setting::getGroup('test');
        $this->line("Test Settings:");
        foreach ($testSettings as $key => $value) {
            $type = gettype($value);
            $displayValue = is_array($value) ? '[' . implode(', ', $value) . ']' : (is_bool($value) ? ($value ? 'true' : 'false') : $value);
            $this->line("  - {$key}: {$displayValue} ({$type})");
        }

        $this->line('');
        $this->info('✅ All tests completed successfully!');

        // Clean up test settings
        Setting::where('group', 'test')->delete();
        Setting::clearCache();
        $this->line('🧹 Test settings cleaned up.');
    }
}
