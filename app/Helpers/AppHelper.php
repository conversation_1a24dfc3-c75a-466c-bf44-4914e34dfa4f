<?php

namespace App\Helpers;

use App\Models\Destination;
use App\Models\Post;

class AppHelper
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function getSetting(): array
    {
        return [
            'app_name' => config('app.name'),
            'general' => settings_group('general'),
            'social' => settings_group('social'),
            'contact' => settings_group('contact'),
            'footer' => settings_group('footer'),
        ];
    }

    public function getMenu(): array
    {
        return array_values([
            'home' => [
                'title' => 'Home',
                'href' => route('front.home'),
            ],
            'destinations' => [
                'title' => 'Destinations',
                'children' => Destination::all(['name', 'slug'])->map(function ($destination) {
                    return [
                        'title' => $destination->name,
                        'href' => route('front.activities', $destination->slug),
                    ];
                })->prepend([
                    'title' => 'All Destinations',
                    'href' => route('front.destinations'),
                ]),
            ],
            'travel_guides' => [
                'title' => 'Travel Guides',
                'children' => Post::where('type', 'travel_guide')
                    ->where('status', 'published')
                    ->get(['title', 'slug'])
                    ->map(function ($post) {
                        return [
                            'title' => $post->title,
                            'href' => route('front.page', $post->slug),
                        ];
                    }),
            ],
            'blogs' => [
                'title' => 'Blogs',
                'href' => route('front.blogs'),
            ],
            'contact' => [
                'title' => 'Contact Us',
                'href' => route('front.contact'),
            ],
            /*'about' => [
                'title' => 'About Us',
                'href' => route('front.page', 'about-us'),
            ]*/
        ]);
    }
}
