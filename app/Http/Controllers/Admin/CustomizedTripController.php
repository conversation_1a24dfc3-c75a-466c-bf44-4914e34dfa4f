<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CustomizedTrip;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CustomizedTripController extends Controller
{
    public function index(Request $request)
    {
        $query = CustomizedTrip::with(['package:id,name', 'country:id,name']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%")
                    ->orWhereHas('package', function ($packageQuery) use ($search) {
                        $packageQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Budget range filter
        if ($request->filled('budget_min')) {
            $query->where('estimated_budget', '>=', $request->budget_min);
        }
        if ($request->filled('budget_max')) {
            $query->where('estimated_budget', '<=', $request->budget_max);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('travel_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('travel_date', '<=', $request->date_to);
        }

        $customizedTrips = $query->latest()->paginate(10)->withQueryString();

        return Inertia::render('admin/customized-trips/index', [
            'customizedTrips' => $customizedTrips,
            'filters' => $request->only(['search', 'budget_min', 'budget_max', 'date_from', 'date_to']),
        ]);
    }

    public function show(CustomizedTrip $customizedTrip)
    {
        $customizedTrip->load(['package', 'country']);

        return response()->json([
            'customizedTrip' => $customizedTrip,
        ]);
    }

    public function updateStatus(Request $request, CustomizedTrip $customizedTrip)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled',
        ]);

        try {
            $customizedTrip->update([
                'status' => $request->status,
            ]);

            return redirect()->back()->with('success', 'Customized trip status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error updating customized trip status: '.$e->getMessage());
        }
    }

    public function destroy(CustomizedTrip $customizedTrip)
    {
        try {
            $customizedTrip->delete();

            return redirect()->back()->with('success', 'Customized trip deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting customized trip: '.$e->getMessage());
        }
    }
}
