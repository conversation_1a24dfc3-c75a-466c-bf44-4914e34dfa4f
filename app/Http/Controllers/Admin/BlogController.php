<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Blog\StoreRequest;
use App\Http\Requests\Blog\UpdateRequest;
use App\Models\BlogPost;
use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BlogController extends Controller
{
    public function index(Request $request)
    {
        $query = BlogPost::with(['destination']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhereHas('destination', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by destination if exists
        if ($request->has('destination') && $request->destination) {
            $query->where('destination_id', $request->destination);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $blogs = $query->orderBy('created_at', 'desc')
            ->paginate(perPage: 10)
            ->withQueryString();

        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/blogs/index', [
            'blogs' => $blogs,
            'destinations' => $destinations,
            'filters' => $request->only(['search', 'destination', 'status']),
            'title' => 'Blogs Management',
            'description' => 'Manage all blog posts in the system.',
        ]);
    }

    public function create()
    {
        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/blogs/create', [
            'destinations' => $destinations,
            'title' => 'Create Blog Post',
            'description' => 'Create a new blog post.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        // Handle destination_id "none" value
        if ($validated['destination_id'] === 'none') {
            $validated['destination_id'] = null;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('blogs', 'public');
            $validated['image'] = $imagePath;
        }

        BlogPost::create($validated);

        return redirect()->route('admin.blogs.index')
            ->with('success', 'Blog post created successfully.');
    }

    public function show(BlogPost $blog)
    {
        $blog->load(['destination']);

        return Inertia::render('admin/blogs/show', [
            'blog' => $blog,
            'title' => 'Blog Post Details',
            'description' => 'View blog post details.',
        ]);
    }

    public function edit(BlogPost $blog)
    {
        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/blogs/edit', [
            'blog' => $blog,
            'destinations' => $destinations,
            'title' => 'Edit Blog Post',
            'description' => 'Edit blog post details.',
        ]);
    }

    public function update(UpdateRequest $request, BlogPost $blog)
    {
        $validated = $request->validated();

        // Handle destination_id "none" value
        if ($validated['destination_id'] === 'none') {
            $validated['destination_id'] = null;
        }

        unset($validated['image']);
        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($blog->image) {
                Storage::disk('public')->delete($blog->image);
            }

            $imagePath = $request->file('image')->store('blogs', 'public');
            $validated['image'] = $imagePath;
        }

        $blog->update($validated);

        return redirect()->route('admin.blogs.index')
            ->with('success', 'Blog post updated successfully.');
    }

    public function destroy(BlogPost $blog)
    {
        // Delete associated image if exists
        if ($blog->image) {
            Storage::disk('public')->delete($blog->image);
        }

        $blog->delete();

        return redirect()->route('admin.blogs.index')
            ->with('success', 'Blog post deleted successfully.');
    }
}
