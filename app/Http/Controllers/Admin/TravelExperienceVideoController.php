<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\TravelExperienceVideo\StoreRequest;
use App\Http\Requests\TravelExperienceVideo\UpdateRequest;
use App\Models\TravelExperienceVideo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TravelExperienceVideoController extends Controller
{
    public function index(Request $request)
    {
        $query = TravelExperienceVideo::query();

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('traveler', 'like', "%{$search}%")
                    ->orWhere('country', 'like', "%{$search}%");
            });
        }

        $videos = $query->orderByDesc('created_at')->paginate(10)->withQueryString();

        return Inertia::render('admin/travel-experience-videos/index', [
            'videos' => $videos,
            'filters' => $request->only(['search']),
            'title' => 'Travel Experience Videos',
            'description' => 'Manage travel experience videos.',
        ]);
    }

    public function create()
    {
        return Inertia::render('admin/travel-experience-videos/create', [
            'title' => 'Add Travel Experience Video',
            'description' => 'Create a new travel experience video.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        if ($request->hasFile('thumbnail')) {
            $validated['thumbnail'] = $request->file('thumbnail')->store('travel-experience-videos', 'public');
        }

        if ($request->hasFile('video')) {
            $validated['video'] = $request->file('video')->store('travel-experience-videos', 'public');
        }

        TravelExperienceVideo::create($validated);

        return redirect()->route('admin.travel-experience-videos.index')->with('success', 'Travel experience video created successfully.');
    }

    public function show(TravelExperienceVideo $travel_experience_video)
    {
        return Inertia::render('admin/travel-experience-videos/show', [
            'videoItem' => $travel_experience_video,
            'title' => 'Travel Experience Video Details',
            'description' => 'View details of the travel experience video.',
        ]);
    }

    public function edit(TravelExperienceVideo $travel_experience_video)
    {
        return Inertia::render('admin/travel-experience-videos/edit', [
            'videoItem' => $travel_experience_video,
            'title' => 'Edit Travel Experience Video',
            'description' => 'Edit the travel experience video.',
        ]);
    }

    public function update(UpdateRequest $request, TravelExperienceVideo $travel_experience_video)
    {
        $validated = $request->validated();

        unset($validated['thumbnail'], $validated['video']);

        if ($request->hasFile('thumbnail')) {
            if ($travel_experience_video->thumbnail) {
                Storage::disk('public')->delete($travel_experience_video->thumbnail);
            }
            $validated['thumbnail'] = $request->file('thumbnail')->store('travel-experience-videos', 'public');
        }

        if ($request->hasFile('video')) {
            if ($travel_experience_video->video) {
                Storage::disk('public')->delete($travel_experience_video->video);
            }
            $validated['video'] = $request->file('video')->store('travel-experience-videos', 'public');
        }

        $travel_experience_video->update($validated);

        return redirect()->route('admin.travel-experience-videos.index')->with('success', 'Travel experience video updated successfully.');
    }

    public function destroy(TravelExperienceVideo $travel_experience_video)
    {
        if ($travel_experience_video->thumbnail) {
            Storage::disk('public')->delete($travel_experience_video->thumbnail);
        }
        if ($travel_experience_video->video) {
            Storage::disk('public')->delete($travel_experience_video->video);
        }

        $travel_experience_video->delete();

        return redirect()->route('admin.travel-experience-videos.index')->with('success', 'Travel experience video deleted successfully.');
    }
}
