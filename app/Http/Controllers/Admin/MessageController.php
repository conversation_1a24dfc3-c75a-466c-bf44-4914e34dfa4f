<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AskedQuestion;
use App\Models\ContactMessage;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MessageController extends Controller
{
    public function index(Request $request)
    {
        $tab = $request->get('tab', 'contact');

        // Contact Messages Query
        $contactQuery = ContactMessage::query();
        if ($request->has('contact_search') && $request->contact_search) {
            $search = $request->contact_search;
            $contactQuery->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('subject', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Asked Questions Query
        $questionsQuery = AskedQuestion::query();
        if ($request->has('questions_search') && $request->questions_search) {
            $search = $request->questions_search;
            $questionsQuery->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $contactMessages = $contactQuery->orderBy('created_at', 'desc')
            ->paginate(10, ['*'], 'contact_page')
            ->withQueryString();

        $askedQuestions = $questionsQuery->orderBy('created_at', 'desc')
            ->paginate(10, ['*'], 'questions_page')
            ->withQueryString();

        return Inertia::render('admin/messages/index', [
            'contactMessages' => $contactMessages,
            'askedQuestions' => $askedQuestions,
            'activeTab' => $tab,
            'filters' => $request->only(['contact_search', 'questions_search']),
            'title' => 'Messages Management',
            'description' => 'Manage contact messages and asked questions.',
        ]);
    }

    public function destroyContact(ContactMessage $contactMessage)
    {
        $contactMessage->delete();

        return redirect()->route('admin.messages.index', ['tab' => 'contact'])
            ->with('success', 'Contact message deleted successfully.');
    }

    public function destroyQuestion(AskedQuestion $askedQuestion)
    {
        $askedQuestion->delete();

        return redirect()->route('admin.messages.index', ['tab' => 'questions'])
            ->with('success', 'Asked question deleted successfully.');
    }
}
