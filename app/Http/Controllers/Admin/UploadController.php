<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class UploadController extends Controller
{
    /**
     * Handle TinyMCE image uploads.
     */
    public function tinymce(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'file' => ['required', 'file', 'image', 'mimes:jpg,jpeg,png,gif,webp,avif,svg', 'max:5120'], // max 5MB
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Invalid file upload',
                'errors' => $e->errors(),
            ], 422);
        }

        $file = $request->file('file');

        // Directory for TinyMCE uploads on the public disk
        $path = $file->store('uploads', 'public');

        $url = Storage::disk('public')->url($path);

        return response()->json([
            'url' => $url,
        ]);
    }
}
