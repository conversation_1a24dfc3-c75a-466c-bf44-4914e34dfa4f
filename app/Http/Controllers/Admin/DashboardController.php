<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\CustomizedTrip;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Get analytics data
        $analytics = $this->getAnalytics();

        return inertia('admin/dashboard', [
            'title' => 'Dashboard',
            'description' => 'Welcome to the admin dashboard.',
            'analytics' => $analytics,
        ]);
    }

    private function getAnalytics()
    {
        // Revenue from confirmed bookings
        $confirmedBookingsRevenue = BookingPayment::whereHas('booking', function ($query) {
            $query->where('status', 'confirmed');
        })->where('status', 'completed')->sum('amount');

        // Booking counters
        $confirmedBookingsCount = Booking::where('status', 'confirmed')->count();
        $pendingBookingsCount = Booking::where('status', 'pending')->count();

        // Customized trip counters
        $confirmedCustomizedTripsCount = CustomizedTrip::where('status', 'confirmed')->count();
        $pendingCustomizedTripsCount = CustomizedTrip::where('status', 'pending')->count();

        // Monthly revenue trend (last 6 months)
        $monthlyRevenue = BookingPayment::whereHas('booking', function ($query) {
            $query->where('status', 'confirmed');
        })
        ->where('status', 'completed')
        ->where('payment_date', '>=', now()->subMonths(6))
        ->select(
            DB::raw('YEAR(payment_date) as year'),
            DB::raw('MONTH(payment_date) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->groupBy('year', 'month')
        ->orderBy('year', 'desc')
        ->orderBy('month', 'desc')
        ->get();

        return [
            'revenue' => [
                'total_confirmed_bookings' => $confirmedBookingsRevenue,
                'monthly_trend' => $monthlyRevenue,
            ],
            'counters' => [
                'bookings' => [
                    'confirmed' => $confirmedBookingsCount,
                    'pending' => $pendingBookingsCount,
                    'total' => $confirmedBookingsCount + $pendingBookingsCount,
                ],
                'customized_trips' => [
                    'confirmed' => $confirmedCustomizedTripsCount,
                    'pending' => $pendingCustomizedTripsCount,
                    'total' => $confirmedCustomizedTripsCount + $pendingCustomizedTripsCount,
                ],
            ],
        ];
    }
}
