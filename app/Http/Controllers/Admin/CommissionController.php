<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CommissionController extends Controller
{
    public function index(Request $request)
    {
        $query = Commission::with(['booking.package:id,name', 'booking:id,first_name,last_name,package_id', 'partner:id,name']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('booking', function ($bookingQuery) use ($search) {
                    $bookingQuery->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%");
                })
                    ->orWhereHas('partner', function ($partnerQuery) use ($search) {
                        $partnerQuery->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('booking.package', function ($packageQuery) use ($search) {
                        $packageQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Partner filter
        if ($request->filled('partner_id')) {
            $query->where('partner_id', $request->partner_id);
        }

        // Amount range filter
        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }
        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $commissions = $query->latest()->paginate(10)->withQueryString();

        // Get partners for filter dropdown
        $partners = \App\Models\Partner::select('id', 'name')->get();

        return Inertia::render('admin/commissions/index', [
            'commissions' => $commissions,
            'partners' => $partners,
            'filters' => $request->only(['search', 'status', 'partner_id', 'amount_min', 'amount_max', 'date_from', 'date_to']),
            'statuses' => [
                'pending' => 'Pending',
                'paid' => 'Paid',
                'cancelled' => 'Cancelled',
            ],
        ]);
    }

    public function show(Commission $commission)
    {
        $commission->load(['booking.package', 'booking.country', 'partner']);

        return response()->json([
            'commission' => $commission,
        ]);
    }

    public function updateStatus(Request $request, Commission $commission)
    {
        $request->validate([
            'status' => 'required|in:pending,paid,cancelled',
        ]);

        try {
            $commission->update([
                'status' => $request->status,
            ]);

            return redirect()->back()->with('success', 'Commission status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error updating commission status: '.$e->getMessage());
        }
    }

    public function destroy(Commission $commission)
    {
        try {
            $commission->delete();

            return redirect()->back()->with('success', 'Commission deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting commission: '.$e->getMessage());
        }
    }
}
