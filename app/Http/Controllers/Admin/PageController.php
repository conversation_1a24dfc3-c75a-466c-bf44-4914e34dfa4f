<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Page\StoreRequest;
use App\Http\Requests\Page\UpdateRequest;
use App\Models\Destination;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PageController extends Controller
{
    public function index(Request $request)
    {
        $query = Post::with(['destination'])->where('type', 'page');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhereHas('destination', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $pages = $query->orderBy('created_at', 'desc')
            ->paginate(perPage: 10)
            ->withQueryString();

        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/pages/index', [
            'pages' => $pages,
            'destinations' => $destinations,
            'filters' => $request->only(['search', 'destination', 'status']),
            'title' => 'Pages Management',
            'description' => 'Manage all pages in the system.',
        ]);
    }

    public function create()
    {
        return Inertia::render('admin/pages/create', [
            'title' => 'Create Page',
            'description' => 'Create a new page.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('pages', 'public');
            $validated['image'] = $imagePath;
        }

        $validated['type'] = 'page';

        Post::create($validated);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully.');
    }

    public function show(Post $page)
    {
        // Ensure we're only showing pages
        if ($page->type !== 'page') {
            abort(404);
        }

        return Inertia::render('admin/pages/show', [
            'page' => $page,
            'title' => 'Page Details',
            'description' => 'View page details.',
        ]);
    }

    public function edit(Post $page)
    {
        // Ensure we're only editing pages
        if ($page->type !== 'page') {
            abort(404);
        }

        return Inertia::render('admin/pages/edit', [
            'page' => $page,
            'title' => 'Edit Page',
            'description' => 'Edit page details.',
        ]);
    }

    public function update(UpdateRequest $request, Post $page)
    {
        // Ensure we're only updating pages
        if ($page->type !== 'page') {
            abort(404);
        }

        $validated = $request->validated();

        unset($validated['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($page->image) {
                Storage::disk('public')->delete($page->image);
            }

            $imagePath = $request->file('image')->store('pages', 'public');
            $validated['image'] = $imagePath;
        }

        $page->update($validated);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully.');
    }

    public function destroy(Post $page)
    {
        // Ensure we're only deleting pages
        if ($page->type !== 'page') {
            abort(404);
        }

        // Prevent deletion of default pages
        if ($page->is_default) {
            return redirect()->route('admin.pages.index')
                ->with('error', 'Default pages cannot be deleted.');
        }

        // Delete associated image if exists
        if ($page->image) {
            Storage::disk('public')->delete($page->image);
        }

        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }
}
