<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Testimonial\StoreRequest;
use App\Http\Requests\Testimonial\UpdateRequest;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TestimonialController extends Controller
{
    public function index(Request $request)
    {
        $query = Testimonial::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by rating
        if ($request->has('rating') && $request->rating) {
            $query->where('rating', $request->rating);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $testimonials = $query->orderBy('created_at', 'desc')
            ->paginate(perPage: 10)
            ->withQueryString();

        return Inertia::render('admin/testimonials/index', [
            'testimonials' => $testimonials,
            'filters' => $request->only(['search', 'rating', 'status']),
            'title' => 'Testimonials Management',
            'description' => 'Manage all testimonials in the system.',
        ]);
    }

    public function create()
    {
        return Inertia::render('admin/testimonials/create', [
            'title' => 'Create Testimonial',
            'description' => 'Create a new testimonial.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('testimonials', 'public');
            $validated['avatar'] = $avatarPath;
        }

        Testimonial::create($validated);

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial created successfully.');
    }

    public function show(Testimonial $testimonial)
    {
        return Inertia::render('admin/testimonials/show', [
            'testimonial' => $testimonial,
            'title' => 'Testimonial Details',
            'description' => 'View testimonial details.',
        ]);
    }

    public function edit(Testimonial $testimonial)
    {
        return Inertia::render('admin/testimonials/edit', [
            'testimonial' => $testimonial,
            'title' => 'Edit Testimonial',
            'description' => 'Edit testimonial details.',
        ]);
    }

    public function update(UpdateRequest $request, Testimonial $testimonial)
    {
        $validated = $request->validated();

        unset($validated['avatar']);
        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($testimonial->avatar) {
                Storage::disk('public')->delete($testimonial->avatar);
            }

            $avatarPath = $request->file('avatar')->store('testimonials', 'public');
            $validated['avatar'] = $avatarPath;
        }

        $testimonial->update($validated);

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial updated successfully.');
    }

    public function destroy(Testimonial $testimonial)
    {
        // Delete associated avatar if exists
        if ($testimonial->avatar) {
            Storage::disk('public')->delete($testimonial->avatar);
        }

        $testimonial->delete();

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial deleted successfully.');
    }
}