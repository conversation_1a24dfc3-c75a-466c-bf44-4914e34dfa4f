<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\FaqGroup;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FaqController extends Controller
{
    public function index(Request $request)
    {
        $query = Faq::query()->with('faqGroup');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('question', 'like', "%{$search}%")
                    ->orWhere('answer', 'like', "%{$search}%")
                    ->orWhereHas('faqGroup', function ($groupQuery) use ($search) {
                        $groupQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by FAQ group
        if ($request->has('faq_group_id') && $request->faq_group_id) {
            $query->where('faq_group_id', $request->faq_group_id);
        }

        $faqs = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        $faqGroups = FaqGroup::orderBy('name')->get(['id', 'name']);

        return Inertia::render('admin/faqs/index', [
            'faqs' => $faqs,
            'faqGroups' => $faqGroups,
            'filters' => $request->only(['search', 'faq_group_id']),
            'title' => 'FAQs Management',
            'description' => 'Manage frequently asked questions in the system.',
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'faq_group_id' => 'required|exists:faq_groups,id',
        ]);

        $faq = Faq::create($validated);

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ created successfully.');
    }

    public function update(Request $request, Faq $faq)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'faq_group_id' => 'required|exists:faq_groups,id',
        ]);

        $faq->update($validated);

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ updated successfully.');
    }

    public function destroy(Faq $faq)
    {
        $faq->delete();

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ deleted successfully.');
    }
}
