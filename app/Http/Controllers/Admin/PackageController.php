<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Package\StoreRequest;
use App\Http\Requests\Package\UpdateRequest;
use App\Models\Activity;
use App\Models\Destination;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PackageController extends Controller
{
    public function index(Request $request)
    {
        $query = Package::with(['destination', 'activities']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhereHas('destination', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by destination if exists
        if ($request->has('destination') && $request->destination) {
            $query->where('destination_id', $request->destination);
        }

        // Filter by activity if exists
        if ($request->has('activity') && $request->activity) {
            $query->whereHas('activities', function ($actQuery) use ($request) {
                $actQuery->where('activities.id', $request->activity);
            });
        }

        // Filter by duration range
        if ($request->has('duration_min') && $request->duration_min) {
            $query->where('duration', '>=', $request->duration_min);
        }

        if ($request->has('duration_max') && $request->duration_max) {
            $query->where('duration', '<=', $request->duration_max);
        }

        // Filter by price range
        if ($request->has('price_min') && $request->price_min) {
            $query->where('base_price', '>=', $request->price_min);
        }

        if ($request->has('price_max') && $request->price_max) {
            $query->where('base_price', '<=', $request->price_max);
        }

        $packages = $query->orderBy('created_at', 'desc')
            ->paginate(perPage: 10)
            ->withQueryString();

        $destinations = Destination::select('id', 'name')->get();
        $activities = Activity::select('id', 'name')->with('destinations:id,name')->get();

        return Inertia::render('admin/packages/index', [
            'packages' => $packages,
            'destinations' => $destinations,
            'activities' => $activities,
            'filters' => $request->only(['search', 'destination', 'activity', 'duration_min', 'duration_max', 'price_min', 'price_max']),
            'title' => 'Packages Management',
            'description' => 'Manage all packages in the system.',
        ]);
    }

    public function create()
    {
        $destinations = Destination::select('id', 'name')->get();
        $activities = Activity::select('id', 'name')->with('destinations:id,name')->get();

        return Inertia::render('admin/packages/create', [
            'destinations' => $destinations,
            'activities' => $activities,
            'title' => 'Create Package',
            'description' => 'Create a new travel package.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('packages', 'public');
        }

        // Handle route map upload
        if ($request->hasFile('route_map')) {
            $validated['route_map'] = $request->file('route_map')->store('packages/route-maps', 'public');
        }

        $package = Package::create($validated);

        // Attach activities
        if (isset($validated['activities'])) {
            $package->activities()->attach($validated['activities']);
        }

        // Create package attributes
        if (isset($validated['attributes'])) {
            foreach ($validated['attributes'] as $index => $attribute) {
                // Handle icon upload for this attribute
                if ($request->hasFile("attributes.{$index}.icon")) {
                    $attribute['icon'] = $request->file("attributes.{$index}.icon")->store('package-attributes', 'public');
                }
                $package->attributes()->create($attribute);
            }
        }

        // Create package prices
        if (isset($validated['prices'])) {
            foreach ($validated['prices'] as $price) {
                $package->prices()->create($price);
            }
        }

        // Create package plans
        if (isset($validated['plans'])) {
            foreach ($validated['plans'] as $plan) {
                $package->plans()->create($plan);
            }
        }

        // Create package cost details
        if (isset($validated['cost_details'])) {
            foreach ($validated['cost_details'] as $costDetail) {
                $package->costDetails()->create($costDetail);
            }
        }

        // Create package FAQs
        if (isset($validated['faqs'])) {
            foreach ($validated['faqs'] as $faq) {
                $package->faqs()->create($faq);
            }
        }

        return redirect()->route('admin.packages.edit', ['package' => $package->id])
            ->with('success', 'Package created successfully.');
    }

    public function show(Package $package)
    {
        $package->load([
            'destination',
            'activities',
            'attributes',
            'prices',
            'media',
            'plans',
            'costDetails',
            'faqs',
            'reviews',
        ]);

        return Inertia::render('admin/packages/show', [
            'package' => $package,
            'title' => 'Package Details',
            'description' => 'View package details and related information.',
        ]);
    }

    public function edit(Package $package)
    {
        $package->load([
            'destination',
            'activities',
            'attributes',
            'prices',
            'media',
            'plans',
            'costDetails',
            'faqs',
        ]);

        $destinations = Destination::select('id', 'name')->get();
        $activities = Activity::select('id', 'name')->with('destinations:id,name')->get();

        return Inertia::render('admin/packages/edit', [
            'package' => $package,
            'destinations' => $destinations,
            'activities' => $activities,
            'title' => 'Edit Package',
            'description' => 'Edit package details and related information.',
        ]);
    }

    public function update(UpdateRequest $request, Package $package)
    {
        $validated = $request->validated();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($package->image) {
                Storage::disk('public')->delete($package->image);
            }
            $validated['image'] = $request->file('image')->store('packages', 'public');
        }

        // Handle route map upload
        if ($request->hasFile('route_map')) {
            // Delete old route map
            if ($package->route_map) {
                Storage::disk('public')->delete($package->route_map);
            }
            $validated['route_map'] = $request->file('route_map')->store('packages/route-maps', 'public');
        }

        $validated['add_to_slider'] = filter_var($validated['add_to_slider'], FILTER_VALIDATE_BOOLEAN);
        $validated['is_featured'] = filter_var($validated['is_featured'], FILTER_VALIDATE_BOOLEAN);

        $package->update($validated);

        // Sync activities
        $package->activities()->sync($validated['activities'] ?? []);

        // Update attributes (with icon handling)
        $this->updateAttributes($package, $validated['attributes'] ?? [], $request);

        // Update prices
        $this->updateNestedData($package, 'prices', $validated['prices'] ?? []);

        // Update plans
        $this->updateNestedData($package, 'plans', $validated['plans'] ?? []);

        // Update cost details
        $this->updateNestedData($package, 'costDetails', $validated['cost_details'] ?? []);

        // Update FAQs
        $this->updateNestedData($package, 'faqs', $validated['faqs'] ?? []);

        return redirect()->route('admin.packages.index')
            ->with('success', 'Package updated successfully.');
    }

    public function destroy(Package $package)
    {
        // Delete associated image
        if ($package->image) {
            Storage::disk('public')->delete($package->image);
        }

        // Delete associated route map
        if ($package->route_map) {
            Storage::disk('public')->delete($package->route_map);
        }

        // Delete the package (cascading will handle related data)
        $package->delete();

        return redirect()->route('admin.packages.index')
            ->with('success', 'Package deleted successfully.');
    }

    /**
     * Helper method to update attributes with icon handling
     */
    private function updateAttributes($package, $data, $request)
    {
        $relation = $package->attributes();
        $existingIds = $relation->pluck('id')->toArray();
        $submittedIds = collect($data)->pluck('id')->filter()->toArray();

        // Delete items not in the submitted data
        $idsToDelete = array_diff($existingIds, $submittedIds);
        if (!empty($idsToDelete)) {
            $relation->whereIn('id', $idsToDelete)->delete();
        }

        // Update or create items
        foreach ($data as $index => $item) {
            // Handle icon upload
            if ($request->hasFile("attributes.{$index}.icon")) {
                $item['icon'] = $request->file("attributes.{$index}.icon")->store('package-attributes', 'public');
            } else if (isset($item['existing_icon'])) {
                $item['icon'] = $item['existing_icon'];
            }

            // Remove form-specific fields
            unset($item['existing_icon']);

            if (isset($item['id']) && $item['id']) {
                // Update existing item
                $relation->where('id', $item['id'])->update(collect($item)->except('id')->toArray());
            } else {
                // Create new item
                $relation->create(collect($item)->except('id')->toArray());
            }
        }
    }

    /**
     * Helper method to update nested data (attributes, prices, plans, etc.)
     */
    private function updateNestedData($package, $relationName, $data)
    {
        $relation = $package->{$relationName}();
        $existingIds = $relation->pluck('id')->toArray();
        $submittedIds = collect($data)->pluck('id')->filter()->toArray();

        // Delete items not in the submitted data
        $idsToDelete = array_diff($existingIds, $submittedIds);
        if (!empty($idsToDelete)) {
            $relation->whereIn('id', $idsToDelete)->delete();
        }

        // Update or create items
        foreach ($data as $item) {
            if (isset($item['id']) && $item['id']) {
                // Update existing item
                $relation->where('id', $item['id'])->update(collect($item)->except('id')->toArray());
            } else {
                // Create new item
                $relation->create(collect($item)->except('id')->toArray());
            }
        }
    }
}
