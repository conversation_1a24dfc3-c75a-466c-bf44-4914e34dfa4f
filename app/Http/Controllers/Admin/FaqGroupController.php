<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FaqGroup;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FaqGroupController extends Controller
{
    public function index(Request $request)
    {
        $query = FaqGroup::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        $faqGroups = $query->withCount('faqs')
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('admin/faq-groups/index', [
            'faqGroups' => $faqGroups,
            'filters' => $request->only(['search']),
            'title' => 'FAQ Groups Management',
            'description' => 'Manage FAQ groups in the system.',
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:faq_groups',
        ]);

        $faqGroup = FaqGroup::create($validated);

        return redirect()->route('admin.faq-groups.index')
            ->with('success', 'FAQ Group created successfully.');
    }

    public function update(Request $request, FaqGroup $faqGroup)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:faq_groups,name,'.$faqGroup->id,
        ]);

        $faqGroup->update($validated);

        return redirect()->route('admin.faq-groups.index')
            ->with('success', 'FAQ Group updated successfully.');
    }

    public function destroy(FaqGroup $faqGroup)
    {
        // Check if FAQ group has associated FAQs
        if ($faqGroup->faqs()->count() > 0) {
            return redirect()->route('admin.faq-groups.index')
                ->with('error', 'Cannot delete FAQ group with associated FAQs. Delete FAQs first.');
        }

        $faqGroup->delete();

        return redirect()->route('admin.faq-groups.index')
            ->with('success', 'FAQ Group deleted successfully.');
    }
}
