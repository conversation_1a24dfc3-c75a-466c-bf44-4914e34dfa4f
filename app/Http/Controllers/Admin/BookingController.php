<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\BookingPayment;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $query = Booking::with(['package:id,name', 'country:id,name']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%")
                    ->orWhereHas('package', function ($packageQuery) use ($search) {
                        $packageQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('booking_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('booking_date', '<=', $request->date_to);
        }

        $bookings = $query->latest()->paginate(10)->withQueryString();

        // Get analytics data
        $analytics = $this->getBookingAnalytics();

        return Inertia::render('admin/bookings/index', [
            'bookings' => $bookings,
            'analytics' => $analytics,
            'filters' => $request->only(['search', 'status', 'date_from', 'date_to']),
            'statuses' => [
                'pending' => 'Pending',
                'confirmed' => 'Confirmed',
                'cancelled' => 'Cancelled',
            ],
        ]);
    }

    public function show(Booking $booking)
    {
        $booking->load(['package', 'country', 'payments', 'commissions']);

        return response()->json([
            'booking' => $booking,
        ]);
    }

    public function destroy(Booking $booking)
    {
        try {
            $booking->delete();

            return redirect()->back()->with('success', 'Booking deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting booking: '.$e->getMessage());
        }
    }

    public function updateStatus(Request $request, Booking $booking)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled',
        ]);

        try {
            $booking->update([
                'status' => $request->status,
            ]);

            return redirect()->back()->with('success', 'Booking status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error updating booking status: '.$e->getMessage());
        }
    }

    private function getBookingAnalytics()
    {
        // Total bookings count
        $totalBookings = Booking::count();
        
        // Status counts
        $pendingBookings = Booking::where('status', 'pending')->count();
        $confirmedBookings = Booking::where('status', 'confirmed')->count();
        $cancelledBookings = Booking::where('status', 'cancelled')->count();
        
        // Total revenue from confirmed bookings
        $totalRevenue = BookingPayment::whereHas('booking', function ($query) {
            $query->where('status', 'confirmed');
        })->where('status', 'completed')->sum('amount');
        
        return [
            'total_bookings' => $totalBookings,
            'pending_bookings' => $pendingBookings,
            'confirmed_bookings' => $confirmedBookings,
            'cancelled_bookings' => $cancelledBookings,
            'total_revenue' => $totalRevenue,
        ];
    }
}
