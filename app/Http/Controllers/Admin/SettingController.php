<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class SettingController extends Controller
{
    public function index()
    {
        $groups = [
            'general' => [
                'business_name', 'office_time', 'address_short', 'address_full', 'email', 'phone', 'whatsapp',
            ],
            'social' => [
                'facebook_link', 'twitter_link', 'instagram_link',
            ],
            'contact' => [
                'email_address_1', 'email_address_2', 'phone_number_1', 'phone_number_2', 'head_office_address', 'map_embed_link', 'receiving_email',
            ],
            'email' => [
                'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption',
            ],
            'payment' => [
                'enable_cash_on_arrival', 'enable_bank_transfer', 'bank_name', 'bank_account_name', 'bank_account_number', 'bank_swift_code', 'payment_instructions',
            ],
            'footer' => [
                'footer_useful_links', 'footer_company_links',
            ],
            'hero' => [
                'hero_pre_heading', 'hero_heading', 'hero_sub_heading', 'hero_button_text', 'hero_button_url', 'hero_image',
            ],
            'top_destination' => [
                'top_dest_pre_heading', 'top_dest_heading', 'top_dest_sub_heading',
            ],
            'plan_trip' => [
                'plan_trip_pre_heading', 'plan_trip_heading', 'plan_trip_short_desc', 'plan_trip_image1', 'plan_trip_image2', 'plan_trip_image3',
                'plan_trip_button_text', 'plan_trip_button_url', 'plan_trip_point1_title', 'plan_trip_point1_desc', 'plan_trip_point2_title', 'plan_trip_point2_desc',
            ],
            'best_offers' => [
                'best_offer_pre_heading', 'best_offer_heading',
            ],
            'popular_packages' => [
                'popular_package_pre_heading', 'popular_package_heading',
            ],
            'travel_experience' => [
                'travel_exp_pre_heading', 'travel_exp_heading',
            ],
            'solabans_village' => [
                'solabans_village_pre_heading', 'solabans_village_heading', 'solabans_village_short_desc', 'solabans_village_button_text', 'solabans_village_video_url',
            ],
            'testimonials' => [
                'testimonial_pre_heading', 'testimonial_heading',
            ],
            'review_brands' => [
                'review_brand_review_image', 'review_brand_images',
            ],
            'news' => [
                'news_pre_heading', 'news_heading',
            ],
        ];

        $payload = [];
        foreach (array_keys($groups) as $group) {
            $payload[$group] = Setting::getGroup($group);
        }

        return inertia('admin/settings/index', [
            'title' => 'Settings',
            'description' => 'Manage site-wide settings by group.',
            'settings' => $payload,
        ]);
    }

    public function homePageSettings()
    {
        $homePageGroups = [
            'hero' => [
                'hero_pre_heading', 'hero_heading', 'hero_sub_heading', 'hero_button_text', 'hero_button_url', 'hero_image',
            ],
            'top_destination' => [
                'top_dest_pre_heading', 'top_dest_heading', 'top_dest_sub_heading',
            ],
            'plan_trip' => [
                'plan_trip_pre_heading', 'plan_trip_heading', 'plan_trip_short_desc', 'plan_trip_image1', 'plan_trip_image2', 'plan_trip_image3',
                'plan_trip_button_text', 'plan_trip_button_url', 'plan_trip_point1_title', 'plan_trip_point1_desc', 'plan_trip_point2_title', 'plan_trip_point2_desc',
            ],
            'best_offers' => [
                'best_offer_pre_heading', 'best_offer_heading',
            ],
            'popular_packages' => [
                'popular_package_pre_heading', 'popular_package_heading',
            ],
            'travel_experience' => [
                'travel_exp_pre_heading', 'travel_exp_heading',
            ],
            'solabans_village' => [
                'solabans_village_pre_heading', 'solabans_village_heading', 'solabans_village_short_desc', 'solabans_village_button_text', 'solabans_village_video_url',
            ],
            'testimonials' => [
                'testimonial_pre_heading', 'testimonial_heading',
            ],
            'review_brands' => [
                'review_brand_review_image', 'review_brand_images',
            ],
            'news' => [
                'news_pre_heading', 'news_heading',
            ],
        ];

        $payload = [];
        foreach (array_keys($homePageGroups) as $group) {
            $payload[$group] = Setting::getGroup($group);
        }

        return inertia('admin/settings/home-page', [
            'title' => 'Home Page Settings',
            'description' => 'Manage home page content and settings by section.',
            'settings' => $payload,
        ]);
    }

    public function updateGroup(Request $request, string $group)
    {
        $rules = $this->rulesForGroup($group);
        $validated = $request->validate($rules);

        // Special handling for footer links: accept an array of objects or a newline string of "Label|URL"
        if ($group === 'footer') {
            foreach (['footer_useful_links', 'footer_company_links'] as $key) {
                if (isset($validated[$key]) && is_string($validated[$key])) {
                    $validated[$key] = $this->parseLinksText($validated[$key]);
                }
            }
        }

        Setting::setMany($group, $validated);

        return back()->with('success', 'Settings updated successfully');
    }

    protected function rulesForGroup(string $group): array
    {
        return match ($group) {
            'general' => [
                'business_name' => ['nullable', 'string', 'max:191'],
                'office_time' => ['nullable', 'string', 'max:191'],
                'address_short' => ['nullable', 'string', 'max:255'],
                'address_full' => ['nullable', 'string'],
                'email' => ['nullable', 'email'],
                'phone' => ['nullable', 'string', 'max:50'],
                'whatsapp' => ['nullable', 'string', 'max:50'],
            ],
            'social' => [
                'facebook_link' => ['nullable', 'url'],
                'twitter_link' => ['nullable', 'url'],
                'instagram_link' => ['nullable', 'url'],
            ],
            'contact' => [
                'email_address_1' => ['nullable', 'email'],
                'email_address_2' => ['nullable', 'email'],
                'phone_number_1' => ['nullable', 'string', 'max:50'],
                'phone_number_2' => ['nullable', 'string', 'max:50'],
                'head_office_address' => ['nullable', 'string'],
                'map_embed_link' => ['nullable', 'string'],
                'receiving_email' => ['nullable', 'email'],
            ],
            'email' => [
                'smtp_host' => ['nullable', 'string', 'max:191'],
                'smtp_port' => ['nullable', 'integer'],
                'smtp_username' => ['nullable', 'string', 'max:191'],
                'smtp_password' => ['nullable', 'string', 'max:191'],
                'smtp_encryption' => ['nullable', Rule::in(['tls', 'ssl'])],
            ],
            'payment' => [
                'enable_cash_on_arrival' => ['nullable', 'boolean'],
                'enable_bank_transfer' => ['nullable', 'boolean'],
                'bank_name' => ['nullable', 'string', 'max:191'],
                'bank_account_name' => ['nullable', 'string', 'max:191'],
                'bank_account_number' => ['nullable', 'string', 'max:191'],
                'bank_swift_code' => ['nullable', 'string', 'max:191'],
                'payment_instructions' => ['nullable', 'string'],
            ],
            'footer' => [
                // Accept either array of {label,url} or string to be parsed (one per line: Label|URL)
                'footer_useful_links' => ['nullable'],
                'footer_company_links' => ['nullable'],
            ],
            'hero' => [
                'hero_pre_heading' => ['nullable', 'string', 'max:255'],
                'hero_heading' => ['nullable', 'string', 'max:255'],
                'hero_sub_heading' => ['nullable', 'string'],
                'hero_button_text' => ['nullable', 'string', 'max:100'],
                'hero_button_url' => ['nullable', 'url'],
                'hero_image' => ['nullable', 'string', 'max:255'],
            ],
            'top_destination' => [
                'top_dest_pre_heading' => ['nullable', 'string', 'max:255'],
                'top_dest_heading' => ['nullable', 'string', 'max:255'],
                'top_dest_sub_heading' => ['nullable', 'string'],
            ],
            'plan_trip' => [
                'plan_trip_pre_heading' => ['nullable', 'string', 'max:255'],
                'plan_trip_heading' => ['nullable', 'string', 'max:255'],
                'plan_trip_short_desc' => ['nullable', 'string'],
                'plan_trip_image1' => ['nullable', 'string', 'max:255'],
                'plan_trip_image2' => ['nullable', 'string', 'max:255'],
                'plan_trip_image3' => ['nullable', 'string', 'max:255'],
                'plan_trip_button_text' => ['nullable', 'string', 'max:100'],
                'plan_trip_button_url' => ['nullable', 'url'],
                'plan_trip_point1_title' => ['nullable', 'string', 'max:255'],
                'plan_trip_point1_desc' => ['nullable', 'string'],
                'plan_trip_point2_title' => ['nullable', 'string', 'max:255'],
                'plan_trip_point2_desc' => ['nullable', 'string'],
            ],
            'best_offers' => [
                'best_offer_pre_heading' => ['nullable', 'string', 'max:255'],
                'best_offer_heading' => ['nullable', 'string', 'max:255'],
            ],
            'popular_packages' => [
                'popular_package_pre_heading' => ['nullable', 'string', 'max:255'],
                'popular_package_heading' => ['nullable', 'string', 'max:255'],
            ],
            'travel_experience' => [
                'travel_exp_pre_heading' => ['nullable', 'string', 'max:255'],
                'travel_exp_heading' => ['nullable', 'string', 'max:255'],
            ],
            'solabans_village' => [
                'solabans_village_pre_heading' => ['nullable', 'string', 'max:255'],
                'solabans_village_heading' => ['nullable', 'string', 'max:255'],
                'solabans_village_short_desc' => ['nullable', 'string'],
                'solabans_village_button_text' => ['nullable', 'string', 'max:100'],
                'solabans_village_video_url' => ['nullable', 'url'],
            ],
            'testimonials' => [
                'testimonial_pre_heading' => ['nullable', 'string', 'max:255'],
                'testimonial_heading' => ['nullable', 'string', 'max:255'],
            ],
            'review_brands' => [
                'review_brand_review_image' => ['nullable', 'string', 'max:255'],
                'review_brand_images' => ['nullable'],
            ],
            'news' => [
                'news_pre_heading' => ['nullable', 'string', 'max:255'],
                'news_heading' => ['nullable', 'string', 'max:255'],
            ],
            default => [],
        };
    }

    protected function parseLinksText(string $text): array
    {
        $lines = preg_split("/(\r\n|\n|\r)/", trim($text));
        $result = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if ($line === '') {
                continue;
            }
            $parts = explode('|', $line, 2);
            $label = trim($parts[0] ?? '');
            $url = trim($parts[1] ?? '');
            if ($label !== '' && $url !== '') {
                $result[] = ['label' => $label, 'url' => $url];
            }
        }

        return $result;
    }
}
