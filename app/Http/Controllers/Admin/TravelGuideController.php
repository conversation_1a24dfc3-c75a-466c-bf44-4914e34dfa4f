<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\TravelGuide\StoreRequest;
use App\Http\Requests\TravelGuide\UpdateRequest;
use App\Models\Destination;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TravelGuideController extends Controller
{
    public function index(Request $request)
    {
        $query = Post::with(['destination'])->where('type', 'travel_guide');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhereHas('destination', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by destination if exists
        if ($request->has('destination') && $request->destination) {
            $query->where('destination_id', $request->destination);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $travelGuides = $query->orderBy('created_at', 'desc')
            ->paginate(perPage: 10)
            ->withQueryString();

        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/travel-guides/index', [
            'travelGuides' => $travelGuides,
            'destinations' => $destinations,
            'filters' => $request->only(['search', 'destination', 'status']),
            'title' => 'Travel Guides Management',
            'description' => 'Manage all travel guides in the system.',
        ]);
    }

    public function create()
    {
        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/travel-guides/create', [
            'destinations' => $destinations,
            'title' => 'Create Travel Guide',
            'description' => 'Create a new travel guide.',
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        // Handle destination_id "none" value
        if ($validated['destination_id'] === 'none') {
            $validated['destination_id'] = null;
        }

        $validated['type'] = 'travel_guide';

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('travel-guides', 'public');
            $validated['image'] = $imagePath;
        }

        $travelGuide = Post::create($validated);

        return redirect()->route('admin.travel-guides.index')
            ->with('success', 'Travel guide created successfully.');
    }

    public function show(Post $travelGuide)
    {
        // Ensure we're only showing travel guides
        if ($travelGuide->type !== 'travel_guide') {
            abort(404);
        }

        $travelGuide->load(['destination']);

        return Inertia::render('admin/travel-guides/show', [
            'travelGuide' => $travelGuide,
            'title' => 'Travel Guide Details',
            'description' => 'View travel guide details.',
        ]);
    }

    public function edit(Post $travelGuide)
    {
        // Ensure we're only editing travel guides
        if ($travelGuide->type !== 'travel_guide') {
            abort(404);
        }

        $destinations = Destination::select('id', 'name')->get();

        return Inertia::render('admin/travel-guides/edit', [
            'travelGuide' => $travelGuide,
            'destinations' => $destinations,
            'title' => 'Edit Travel Guide',
            'description' => 'Edit travel guide details.',
        ]);
    }

    public function update(UpdateRequest $request, Post $travelGuide)
    {
        // Ensure we're only updating travel guides
        if ($travelGuide->type !== 'travel_guide') {
            abort(404);
        }

        $validated = $request->validated();

        // Handle destination_id "none" value
        if ($validated['destination_id'] === 'none') {
            $validated['destination_id'] = null;
        }

        unset($validated['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($travelGuide->image) {
                Storage::disk('public')->delete($travelGuide->image);
            }

            $imagePath = $request->file('image')->store('travel-guides', 'public');
            $validated['image'] = $imagePath;
        }

        $travelGuide->update($validated);

        return redirect()->route('admin.travel-guides.index')
            ->with('success', 'Travel guide updated successfully.');
    }

    public function destroy(Post $travelGuide)
    {
        // Ensure we're only deleting travel guides
        if ($travelGuide->type !== 'travel_guide') {
            abort(404);
        }

        // Delete associated image if exists
        if ($travelGuide->image) {
            Storage::disk('public')->delete($travelGuide->image);
        }

        $travelGuide->delete();

        return redirect()->route('admin.travel-guides.index')
            ->with('success', 'Travel guide deleted successfully.');
    }
}
