<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ActivityController extends Controller
{
    public function index(Request $request)
    {
        $query = Activity::query()->with(['parentActivity', 'destinations']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhereHas('destinations', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by destination
        if ($request->has('destination_id') && $request->destination_id) {
            $query->whereHas('destinations', function ($destQuery) use ($request) {
                $destQuery->where('destinations.id', $request->destination_id);
            });
        }

        // Filter by parent activity (show only main activities or sub-activities)
        if ($request->has('activity_type')) {
            if ($request->activity_type === 'main') {
                $query->whereNull('activity_id');
            } elseif ($request->activity_type === 'sub') {
                $query->whereNotNull('activity_id');
            }
        }

        $activities = $query->withCount(['subActivities', 'packages'])
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        $destinations = Destination::orderBy('name')->get(['id', 'name']);
        $parentActivities = Activity::whereNull('activity_id')
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('admin/activities/index', [
            'activities' => $activities,
            'destinations' => $destinations,
            'parentActivities' => $parentActivities,
            'filters' => $request->only(['search', 'destination_id', 'activity_type']),
            'title' => 'Activities Management',
            'description' => 'Manage all activities and sub-activities in the system.',
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:activities,slug',
            'description' => 'nullable|string',
            'destination_ids' => 'required|array',
            'destination_ids.*' => 'required|exists:destinations,id',
            'activity_id' => 'nullable|exists:activities,id',
            'image' => 'nullable|image|max:2048', // 2MB max
        ]);

        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('activities', 'public');
        }

        // Remove destination_ids from main data as it's for the pivot table
        $destinationIds = $validated['destination_ids'] ?? [];
        unset($validated['destination_ids']);

        $activity = Activity::create($validated);

        // Attach the activity to multiple destinations if provided
        if (! empty($destinationIds)) {
            $activity->destinations()->attach($destinationIds);
        }

        return redirect()->route('admin.activities.index')
            ->with('success', 'Activity created successfully.');
    }

    public function update(Request $request, Activity $activity)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:activities,slug,'.$activity->id,
            'description' => 'nullable|string',
            'destination_id' => 'required|exists:destinations,id',
            'destination_ids' => 'nullable|array',
            'destination_ids.*' => 'exists:destinations,id',
            'activity_id' => 'nullable|exists:activities,id',
            'image' => 'nullable|image|max:2048', // 2MB max
        ]);

        // Prevent self-referencing
        if (isset($validated['activity_id']) && $validated['activity_id'] == $activity->id) {
            return redirect()->route('admin.activities.index')
                ->with('error', 'An activity cannot be its own parent.');
        }

        // Prevent circular references
        if (isset($validated['activity_id']) && $this->wouldCreateCircularReference($activity, $validated['activity_id'])) {
            return redirect()->route('admin.activities.index')
                ->with('error', 'This would create a circular reference.');
        }

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($activity->image) {
                Storage::disk('public')->delete($activity->image);
            }
            $validated['image'] = $request->file('image')->store('activities', 'public');
        }

        // Remove destination_ids from main data as it's for the pivot table
        $destinationIds = $validated['destination_ids'] ?? [];
        unset($validated['destination_ids']);

        $activity->update($validated);

        // Sync the activity with multiple destinations if provided
        if (! empty($destinationIds)) {
            $activity->destinations()->sync($destinationIds);
        }

        return redirect()->route('admin.activities.index')
            ->with('success', 'Activity updated successfully.');
    }

    public function destroy(Activity $activity)
    {
        // Check if activity has sub-activities
        if ($activity->subActivities()->count() > 0) {
            return redirect()->route('admin.activities.index')
                ->with('error', 'Cannot delete activity with sub-activities. Delete sub-activities first.');
        }

        // Check if activity has associated packages
        if ($activity->packages()->count() > 0) {
            return redirect()->route('admin.activities.index')
                ->with('error', 'Cannot delete activity with associated packages.');
        }

        // Delete image if exists
        if ($activity->image) {
            Storage::disk('public')->delete($activity->image);
        }

        $activity->delete();

        return redirect()->route('admin.activities.index')
            ->with('success', 'Activity deleted successfully.');
    }

    /**
     * Check if setting a parent would create a circular reference
     */
    private function wouldCreateCircularReference(Activity $activity, $parentId)
    {
        if (! $parentId) {
            return false;
        }

        $currentParent = Activity::find($parentId);
        while ($currentParent) {
            if ($currentParent->id === $activity->id) {
                return true;
            }
            $currentParent = $currentParent->parentActivity;
        }

        return false;
    }
}
