<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;

class BlogController extends Controller
{
    public function index()
    {
        $blogs = BlogPost::with(['destination'])
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(9)
            ->through(function ($blog) {
                return [
                    'id' => $blog->id,
                    'title' => $blog->title,
                    'content' => $blog->content,
                    'image' => $blog->image ? '/storage/'.$blog->image : '/assets/img-landscape.png',
                    'url' => '/blog/'.$blog->id,
                    'destination' => $blog->destination?->name,
                    'created_at' => $blog->created_at,
                ];
            });

        return inertia('blogs', [
            'title' => 'Blogs',
            'blogs' => $blogs,
        ]);
    }

    public function show($slug)
    {
        $blog = BlogPost::published()
            ->where('id', $slug)
            ->with(['destination'])
            ->firstOrFail();

        return inertia('blogs-details', [
            'title' => $blog->title,
            'blog' => $blog,
        ]);
    }
}
