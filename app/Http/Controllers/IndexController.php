<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\Destination;
use App\Models\FaqGroup;
use App\Models\Package;
use App\Models\Setting;
use App\Models\Testimonial;

class IndexController extends Controller
{
    public function index()
    {
        // Fetch featured packages for home page
        $featuredPackages = Package::with(['destination', 'media'])
            ->where('is_featured', true)
            ->limit(8)
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'description' => $package->description,
                    'image' => $package->image,
                    'destination' => $package->destination->name ?? '',
                    'base_price' => $package->base_price,
                    'duration' => $package->duration,
                    'location' => $package->location,
                ];
            });

        // Fetch top destinations
        $topDestinations = Destination::withCount('packages')
            ->limit(6)
            ->get()
            ->map(function ($destination) {
                return [
                    'id' => $destination->id,
                    'name' => $destination->name,
                    'description' => $destination->description,
                    'image' => $destination->image,
                    'packages_count' => $destination->packages_count,
                    'slug' => strtolower(str_replace(' ', '-', $destination->name)),
                ];
            });

        // Fetch testimonials
        $testimonials = Testimonial::limit(6)
            ->get()
            ->map(function ($testimonial) {
                return [
                    'id' => $testimonial->id,
                    'name' => $testimonial->name,
                    'content' => $testimonial->content,
                    'rating' => $testimonial->rating ?? 5,
                    'image' => $testimonial->image,
                ];
            });

        // Fetch latest blog posts/news
        $latestPosts = BlogPost::published()
            ->latest()
            ->limit(3)
            ->get()
            ->map(function ($post) {
                return [
                    'id' => $post->id,
                    'title' => $post->title,
                    'slug' => $post->slug,
                    'content' => substr(strip_tags($post->content), 0, 150).'...',
                    'image' => $post->image,
                    'created_at' => $post->created_at->format('M d, Y'),
                ];
            });

        // Fetch home page settings
        $homePageSettings = Setting::whereIn('group', [
            'hero', 'top_destination', 'plan_trip', 'best_offers',
            'popular_packages', 'travel_experience', 'solabans_village',
            'testimonials', 'review_brands', 'news',
        ])->get()->groupBy('group')->map(function ($settings) {
            return $settings->pluck('value', 'key');
        });

        return inertia('home', [
            'featuredPackages' => $featuredPackages,
            'topDestinations' => $topDestinations,
            'testimonials' => $testimonials,
            'latestPosts' => $latestPosts,
            'homePageSettings' => $homePageSettings,
        ]);
    }

    public function contact()
    {
        return inertia('contact', [
            'title' => 'Contact Us',
        ]);
    }

    public function about()
    {
        return inertia('sample-page', [
            'title' => 'About Us',
        ]);
    }

    public function samplePage()
    {
        return inertia('sample-page', [
            'title' => 'Sample Page',
        ]);
    }

    public function travelGuide()
    {
        return inertia('sample-page', [
            'title' => 'Travel Guide',
        ]);
    }

    public function privacy()
    {
        return inertia('sample-page', [
            'title' => 'Privacy Policy',
        ]);
    }

    public function terms()
    {
        return inertia('sample-page', [
            'title' => 'Terms and Conditions',
        ]);
    }

    public function faqs()
    {
        $faqGroups = FaqGroup::with('faqs')
            ->whereHas('faqs')
            ->orderBy('name')
            ->get()
            ->map(function ($group) {
                return [
                    'id' => $group->id,
                    'title' => $group->name,
                    'items' => $group->faqs->map(function ($faq) {
                        return [
                            'title' => $faq->question,
                            'content' => $faq->answer,
                        ];
                    })->toArray(),
                ];
            })
            ->toArray();

        return inertia('faqs', [
            'title' => 'FAQs',
            'faqs' => $faqGroups,
        ]);
    }
}
