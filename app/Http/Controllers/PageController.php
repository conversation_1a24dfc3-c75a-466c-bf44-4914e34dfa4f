<?php

namespace App\Http\Controllers;

use App\Models\Post;

class PageController extends Controller
{
    public function show($slug)
    {
        try {
            $page = Post::query()
                // ->whereIn('type', ['page', 'travel_guide'])
                ->where('status', 'published')
                ->where('slug', $slug)
                ->with(['destination'])
                ->firstOrFail();

            return inertia('page', [
                'title' => $page->title,
                'page' => [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'content' => $page->content,
                    'image' => $page->image ? '/storage/'.$page->image : null,
                    'destination' => $page->destination?->name,
                    'created_at' => $page->created_at,
                    'updated_at' => $page->updated_at,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching page: '.$e->getMessage());

            return inertia('page', [
                'title' => 'Page Not Found',
                'page' => null,
                'error' => 'The requested page could not be found.',
            ]);
        }
    }

    public function index()
    {
        try {
            $pages = Post::where('type', 'page')
                ->where('status', 'published')
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($page) {
                    return [
                        'id' => $page->id,
                        'title' => $page->title,
                        'slug' => $page->slug,
                        'content' => substr(strip_tags($page->content), 0, 200).'...',
                        'image' => $page->image ? '/storage/'.$page->image : null,
                        'destination' => $page->destination?->name,
                        'created_at' => $page->created_at->format('M d, Y'),
                    ];
                });

            return inertia('pages', [
                'title' => 'Pages',
                'pages' => $pages,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching pages: '.$e->getMessage());

            return inertia('pages', [
                'title' => 'Pages',
                'pages' => [],
                'error' => 'Unable to load pages at this time.',
            ]);
        }
    }
}
