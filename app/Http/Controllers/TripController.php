<?php

namespace App\Http\Controllers;

use App\Mail\CustomizedTripSubmitted;
use App\Models\Activity;
use App\Models\Country;
use App\Models\CustomizedTrip;
use App\Models\Destination;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;

class TripController extends Controller
{
    public function destinations()
    {
        try {
            $destinations = Destination::withCount('packages')
                ->get()
                ->map(function ($destination) {
                    return [
                        'id' => $destination->id,
                        'name' => $destination->name,
                        'description' => $destination->description,
                        'image' => $destination->image,
                        'packages_count' => $destination->packages_count,
                        'slug' => strtolower(str_replace(' ', '-', $destination->name)),
                    ];
                });

            return inertia('destinations', [
                'title' => 'Destinations',
                'destinations' => $destinations,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching destinations: '.$e->getMessage());

            return inertia('destinations', [
                'title' => 'Destinations',
                'destinations' => [],
                'error' => 'Unable to load destinations. Please try again later.',
            ]);
        }
    }

    public function activities(string $destination)
    {
        try {
            // Find destination by slug
            $destinationModel = Destination::where('slug', $destination)->firstOrFail();

            if (!$destinationModel) {
                abort(404, 'Destination not found');
            }

            // Get activities for this destination
            $activities = Activity::whereHas('destinations', function ($query) use ($destinationModel) {
                $query->where('destinations.id', $destinationModel->id);
            })
                ->withCount(['packages' => function ($query) use ($destinationModel) {
                    $query->where('destination_id', $destinationModel->id);
                }])
                ->get()
                ->map(function ($activity) {
                    return [
                        'id' => $activity->id,
                        'name' => $activity->name,
                        'description' => $activity->description,
                        'image' => $activity->image,
                        'packages_count' => $activity->packages_count,
                        'slug' => strtolower(str_replace(' ', '-', $activity->name)),
                    ];
                });

            return Inertia::render('activities', [
                'title' => $destinationModel->name,
                'destination' => $destination,
                'destinationData' => [
                    'id' => $destinationModel->id,
                    'name' => $destinationModel->name,
                    'description' => $destinationModel->description,
                    'image' => $destinationModel->image,
                ],
                'activities' => $activities,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching activities for destination '.$destination.': '.$e->getMessage());

            return Inertia::render('activities', [
                'title' => 'Activities',
                'destination' => $destination,
                'destinationData' => [],
                'activities' => [],
                'error' => 'Unable to load activities. Please try again later.',
            ]);
        }
    }

    public function activityCategory(string $destination, string $category)
    {
        return Inertia::render('activity-categories', [
            'title' => 'Trekking In Nepal',
            'category' => $category,
            'destination' => $destination,
        ]);
    }

    public function packages(string $destination, string $category)
    {
        // Find destination
        $destinationModel = Destination::where('name', 'like', '%'.ucfirst($destination).'%')
            ->orWhere('id', $destination)
            ->first();

        if (!$destinationModel) {
            abort(404, 'Destination not found');
        }

        // Find activity/category
        $activityModel = Activity::where('name', 'like', '%'.str_replace('-', ' ', $category).'%')
            ->orWhere('id', $category)
            ->first();

        if (!$activityModel) {
            abort(404, 'Activity category not found');
        }

        // Get packages for this destination and activity
        $packages = Package::with(['destination', 'media', 'reviews'])
            ->where('destination_id', $destinationModel->id)
            ->whereHas('activities', function ($query) use ($activityModel) {
                $query->where('activities.id', $activityModel->id);
            })
            ->paginate(12)
            ->through(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'description' => $package->description,
                    'image' => $package->image,
                    'destination' => $package->destination->name,
                    'activities' => $package->activities->map->only(['id', 'name']),
                    'base_price' => $package->base_price,
                    'duration' => $package->duration,
                    'location' => $package->location,
                    'slug' => strtolower(str_replace(' ', '-', $package->name)),
                    'reviews_count' => $package->reviews->count(),
                    'average_rating' => $package->reviews->avg('rating') ?? 0,
                ];
            });

        return Inertia::render('packages', [
            'title' => $activityModel->name.' in '.$destinationModel->name,
            'category' => $category,
            'destination' => $destination,
            'destinationData' => [
                'id' => $destinationModel->id,
                'name' => $destinationModel->name,
                'description' => $destinationModel->description,
            ],
            'activityData' => [
                'id' => $activityModel->id,
                'name' => $activityModel->name,
                'description' => $activityModel->description,
            ],
            'packages' => $packages,
        ]);
    }

    public function allPackages(Request $request)
    {
        $query = Package::with(['destination', 'activities', 'media', 'reviews']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhereHas('destination', function ($destQuery) use ($search) {
                        $destQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by destination
        if ($request->has('destination') && $request->destination && $request->destination !== 'all') {
            if (is_numeric($request->destination)) {
                $query->where('destination_id', $request->destination);
            } else {
                $query->whereHas('destination', function ($destQuery) use ($request) {
                    $destQuery->where('name', 'like', '%' . $request->destination . '%');
                });
            }
        }

        // Filter by activity type
        if ($request->has('type') && $request->type && $request->type !== 'all') {
            $query->whereHas('activities', function ($actQuery) use ($request) {
                $actQuery->where('name', 'like', '%' . $request->type . '%');
            });
        }

        // Filter by duration
        if ($request->has('duration') && $request->duration && $request->duration !== 'all') {
            $durationValue = (int) filter_var($request->duration, FILTER_SANITIZE_NUMBER_INT);
            if ($durationValue > 0) {
                $query->where('duration', '<=', $durationValue + 2)
                      ->where('duration', '>=', $durationValue - 2);
            }
        }

        // Get packages with pagination
        $packages = $query->paginate(12)
            ->through(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'description' => $package->description,
                    'image' => $package->image,
                    'destination' => $package->destination->name,
                    'activities' => $package->activities->map->only(['id', 'name']),
                    'base_price' => $package->base_price,
                    'duration' => $package->duration,
                    'location' => $package->location,
                    'slug' => strtolower(str_replace(' ', '-', $package->name)),
                    'reviews_count' => $package->reviews->count(),
                    'average_rating' => $package->reviews->avg('rating') ?? 0,
                ];
            });

        // Get all destinations and activities for filters
        $destinations = Destination::select('id', 'name')->get();
        $activities = Activity::select('id', 'name')->get();

        return Inertia::render('packages-search', [
            'title' => 'Search Packages',
            'packages' => $packages,
            'destinations' => $destinations,
            'activities' => $activities,
            'filters' => [
                'search' => $request->search,
                'destination' => $request->destination,
                'type' => $request->type,
                'duration' => $request->duration,
            ],
        ]);
    }

    public function package(string $destination, string $category, string $slug)
    {
        // Find the package by slug or ID
        $package = Package::with([
            'destination',
            'activities',
            'attributes',
            'media',
            'plans',
            'costDetails',
            'faqs',
            'reviews',
            'prices',
        ])
            ->where(function ($query) use ($slug) {
                $query->where('name', 'like', '%'.str_replace('-', ' ', $slug).'%')
                    ->orWhere('id', $slug);
            })
            ->first();

        if (!$package) {
            abort(404, 'Package not found');
        }

        // Format package data
        $packageData = [
            'id' => $package->id,
            'name' => $package->name,
            'description' => $package->description,
            'image' => $package->image,
            'destination' => $package->destination->name,
            'base_price' => $package->base_price,
            'duration' => $package->duration,
            'location' => $package->location,
            'route_map' => $package->route_map,
            'activities' => $package->activities->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'name' => $activity->name,
                    'description' => $activity->description,
                ];
            }),
            'attributes' => $package->attributes->map(function ($attribute) {
                return [
                    'id' => $attribute->id,
                    'name' => $attribute->name,
                    'value' => $attribute->value,
                    'icon' => $attribute->icon,
                ];
            }),
            'media' => $package->media->map(function ($media) {
                return [
                    'id' => $media->id,
                    'type' => $media->type,
                    'url' => $media->url,
                    'caption' => $media->caption,
                ];
            }),
            'plans' => $package->plans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'day' => $plan->day_number,
                    'title' => $plan->plan_name,
                    'description' => $plan->description,
                ];
            }),
            'cost_details' => $package->costDetails->map(function ($cost) {
                return [
                    'id' => $cost->id,
                    'type' => $cost->cost_type,
                    'description' => $cost->description,
                ];
            }),
            'faqs' => $package->faqs->map(function ($faq) {
                return [
                    'id' => $faq->id,
                    'question' => $faq->question,
                    'answer' => $faq->answer,
                ];
            }),
            'reviews' => $package->reviews->map(function ($review) {
                return [
                    'id' => $review->id,
                    'name' => $review->name,
                    'rating' => $review->rating,
                    'comment' => $review->comment,
                    'created_at' => $review->created_at->format('M d, Y'),
                ];
            }),
            'prices' => $package->prices->map(function ($price) {
                return [
                    'id' => $price->id,
                    'group_size' => is_array($price->condition) ? implode(', ', $price->condition) : $price->condition,
                    'price' => $price->price,
                ];
            }),
            'reviews_count' => $package->reviews->count(),
            'average_rating' => $package->reviews->avg('rating') ?? 0,
        ];

        return Inertia::render('package', [
            'title' => $package->name,
            'category' => $category,
            'destination' => $destination,
            'slug' => $slug,
            'package' => $packageData,
        ]);
    }

    public function customizedTrip()
    {
        $countries = Country::select('id', 'name')->orderBy('name')->get();

        // Temporary fix: Use all() instead of get() to avoid the status issue
        $packages = Package::all(['id', 'name'])->sortBy('name')->values();

        return Inertia::render('customized-trip', [
            'title' => 'Customized Trip',
            'countries' => $countries,
            'packages' => $packages,
        ]);
    }

    public function storeCustomizedTrip(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:50',
            'country_id' => 'required|exists:countries,id',
            'package_id' => 'nullable|exists:packages,id',
            'travel_date' => 'nullable|date|after:today',
            'trip_duration' => 'nullable|integer|min:1|max:365',
            'number_of_adults' => 'required|integer|min:1|max:50',
            'number_of_children' => 'required|integer|min:0|max:50',
            'estimated_budget' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:5000',
        ]);

        // Set default status
        $validated['status'] = 'pending';

        // Create the customized trip
        $customizedTrip = CustomizedTrip::create($validated);

        // Send email notification to admin
        $recipient = function_exists('setting')
            ? (setting('receiving_email') ?: setting('email') ?: config('mail.from.address'))
            : config('mail.from.address');

        try {
            Mail::to($recipient)
                ->send(new CustomizedTripSubmitted($customizedTrip));
        } catch (\Throwable $e) {
            // Log error but don't break the UX
            report($e);
        }

        return redirect()->route('front.customized-trip')
            ->with('success', 'Your customized trip request has been submitted successfully. We\'ll get back to you shortly.');
    }
}
