<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\BookingPayment;
use App\Models\Country;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class BookingController extends Controller
{
    public function create(Package $package)
    {
        $countries = Country::orderBy('name')->get();

        // Get payment settings
        $paymentSettings = [
            'enable_cash_on_arrival' => setting('enable_cash_on_arrival', '1'),
            'enable_bank_transfer' => setting('enable_bank_transfer', '1'),
            'bank_name' => setting('bank_name', ''),
            'bank_account_name' => setting('bank_account_name', ''),
            'bank_account_number' => setting('bank_account_number', ''),
            'bank_swift_code' => setting('bank_swift_code', ''),
            'payment_instructions' => setting('payment_instructions', ''),
        ];

        return Inertia::render('booking', [
            'title' => 'Book '.$package->name,
            'package' => $package->load('destination'),
            'countries' => $countries,
            'paymentSettings' => $paymentSettings,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'package_id' => 'required|exists:packages,id',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'whatsapp' => 'nullable|string|max:255',
            'country_id' => 'required|exists:countries,id',
            'extra_notes' => 'nullable|string',
            'payment_method' => 'required|in:cash,bank_transfer',
            'payment_amount' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Create booking
            $booking = Booking::create([
                'package_id' => $validated['package_id'],
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'whatsapp' => $validated['whatsapp'],
                'country_id' => $validated['country_id'],
                'booking_date' => today(),
                'extra_notes' => $validated['extra_notes'],
                'status' => 'pending',
            ]);

            // Create payment record
            BookingPayment::create([
                'booking_id' => $booking->id,
                'payment_method' => $validated['payment_method'],
                'amount' => $validated['payment_amount'],
                'payment_date' => now(),
                'status' => $validated['payment_method'] === 'cash' ? 'pending' : 'pending',
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Booking submitted successfully! We will contact you soon to confirm your booking.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }
}
