<?php

namespace App\Http\Controllers;

use App\Mail\ContactFormSubmitted;
use App\Models\ContactMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    public function submit(Request $request)
    {
        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:50'],
            'email' => ['required', 'email', 'max:255'],
            'message' => ['required', 'string', 'max:5000'],
        ]);

        // Store the contact message (optional but useful for admin UI)
        $contactMessage = ContactMessage::create([
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'subject' => 'New Contact Form Submission',
            'message' => $validated['message'],
        ]);

        // Determine recipient email from settings with sensible fallback
        $recipient = function_exists('setting')
            ? (setting('receiving_email') ?: setting('email') ?: config('mail.from.address'))
            : config('mail.from.address');

        try {
            Mail::to($recipient)
                ->send(new ContactFormSubmitted($contactMessage));
        } catch (\Throwable $e) {
            // We don't want to break the UX; log error and continue
            report($e);
        }

        return redirect()->route('front.contact')
            ->with('success', 'Your message has been sent successfully. We\'ll get back to you shortly.');
    }
}
