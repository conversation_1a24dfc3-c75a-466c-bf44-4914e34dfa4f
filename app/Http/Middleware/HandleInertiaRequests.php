<?php

namespace App\Http\Middleware;

use App\Helpers\AppHelper;
use Closure;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Symfony\Component\HttpFoundation\Response;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'app';
    protected string $_layout = 'default';

    public function handle(Request $request, Closure $next, $layout = null): Response
    {
        // If a layout is provided, set it as the root view.
        if ($layout) {
            $this->_layout = $layout;
        }

        // Set the root view for Inertia.
        return parent::handle($request, $next);
    }


    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $ziggyGroup = $this->_layout !== 'default' ? $this->_layout : null;

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'auth' => [
                'user' => $request->user(),
            ],
            'ziggy' => fn(): array => [
                ...(new Ziggy($ziggyGroup))->toArray(),
                'location' => $request->url(),
            ],
            'layout' => $this->_layout,
            'settings' => app(AppHelper::class)->getSetting(),
            'menu' => app(AppHelper::class)->getMenu()
        ];
    }
}
