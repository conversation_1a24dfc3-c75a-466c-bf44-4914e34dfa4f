<?php

namespace App\Http\Requests\Package;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:packages,slug',
            'description' => 'required|string',
            'destination_id' => 'required|exists:destinations,id',
            'base_price' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'duration' => 'required|integer|min:1',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'route_map' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'add_to_slider' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'activities' => 'nullable|array',
            'activities.*' => 'exists:activities,id',
            // Package attributes
            'attributes' => 'nullable|array',
            'attributes.*.name' => 'required|string|max:255',
            'attributes.*.value' => 'required|string|max:255',
            'attributes.*.icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:1024',
            // Package prices
            'prices' => 'nullable|array',
            'prices.*.price' => 'required|numeric|min:0',
            'prices.*.condition' => 'required|string|max:255',
            // Package plans
            'plans' => 'nullable|array',
            'plans.*.day_number' => 'required|integer|min:1',
            'plans.*.plan_name' => 'required|string|max:255',
            'plans.*.description' => 'required|string',
            // Package cost details
            'cost_details' => 'nullable|array',
            'cost_details.*.description' => 'required|string',
            'cost_details.*.cost_type' => 'required|in:included,excluded',
            // Package FAQs
            'faqs' => 'nullable|array',
            'faqs.*.question' => 'required|string|max:500',
            'faqs.*.answer' => 'required|string',
        ];
    }
}
