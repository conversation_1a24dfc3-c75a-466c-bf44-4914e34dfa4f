<?php

namespace App\Mail;

use App\Models\CustomizedTrip;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CustomizedTripSubmitted extends Mailable
{
    use Queueable, SerializesModels;

    public $customizedTrip;

    /**
     * Create a new message instance.
     */
    public function __construct(CustomizedTrip $customizedTrip)
    {
        $this->customizedTrip = $customizedTrip;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Customized Trip Request - ' . $this->customizedTrip->full_name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.customized-trip-submitted',
            with: [
                'customizedTrip' => $this->customizedTrip,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}