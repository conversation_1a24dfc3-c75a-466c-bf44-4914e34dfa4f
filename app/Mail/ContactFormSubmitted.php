<?php

namespace App\Mail;

use App\Models\ContactMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ContactFormSubmitted extends Mailable
{
    use Queueable, SerializesModels;

    public ContactMessage $contactMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(ContactMessage $contactMessage)
    {
        $this->contactMessage = $contactMessage;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        $fullName = trim($this->contactMessage->first_name.' '.$this->contactMessage->last_name);

        return $this
            ->subject('New Contact Message from '.$fullName)
            ->replyTo($this->contactMessage->email, $fullName)
            ->view('emails.contact.new_contact_message', [
                'contact' => $this->contactMessage,
                'appName' => config('app.name'),
                'submittedAt' => $this->contactMessage->created_at,
            ]);
    }
}
