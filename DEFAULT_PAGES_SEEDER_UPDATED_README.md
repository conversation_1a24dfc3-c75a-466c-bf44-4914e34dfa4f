# Default Pages Seeder - Updated with Delete Protection

## Overview

The `DefaultPagesSeeder` creates essential pages for the Everest Sherpa Adventure website. These pages provide important information for visitors and establish the foundation for the site's content structure.

## New Features ✨

- **Default Page Protection**: Default pages cannot be deleted from the admin panel
- **Automatic Flagging**: All default pages are marked with `is_default = true`
- **Update Support**: Re-running the seeder updates existing default pages
- **Comprehensive Testing**: Full test coverage for all functionality

## Database Schema Changes

A new `is_default` boolean column has been added to the `posts` table to identify default pages that should not be deleted.

```sql
-- Migration: add_is_default_to_posts_table
ALTER TABLE posts ADD COLUMN is_default BOOLEAN DEFAULT FALSE AFTER destination_id;
```

## Created Pages

### 1. About Us

- **Purpose**: Introduces the company, its story, and team
- **Content**: Company history, services, team information, and commitments
- **Protected**: ✅ Cannot be deleted from admin panel

### 2. Privacy Policy

- **Purpose**: Legal document outlining data collection and usage policies
- **Content**: Information collection, usage, sharing, security, and user rights
- **Compliance**: Essential for GDPR and privacy regulation compliance
- **Protected**: ✅ Cannot be deleted from admin panel

### 3. Terms and Conditions

- **Purpose**: Legal agreement between the company and customers
- **Content**: Booking policies, cancellation terms, liability, and dispute resolution
- **Protected**: ✅ Cannot be deleted from admin panel

### 4. Travel Guide

- **Purpose**: Helpful information for travelers visiting Nepal
- **Content**: Best times to visit, popular routes, packing guides, cultural etiquette
- **Protected**: ✅ Cannot be deleted from admin panel

### 5. Sample Page

- **Purpose**: Template/example page for content management
- **Content**: Demonstrates features and how to use the CMS
- **Protected**: ✅ Cannot be deleted from admin panel

## Usage

### Initial Setup

```bash
# Run migration to add is_default column
php artisan migrate

# Run the seeder to create/update default pages
php artisan db:seed --class=DefaultPagesSeeder
```

### Updating Default Pages

```bash
# Re-run seeder to update existing default pages with new content
php artisan db:seed --class=DefaultPagesSeeder
```

## Protection Features

### Delete Protection in Admin Panel

- Default pages (with `is_default = true`) **cannot be deleted**
- Attempting to delete shows error: _"Default pages cannot be deleted."_
- Non-default pages can still be deleted normally
- Default pages can still be **edited** through the admin panel

### Code Implementation

```php
// In PageController@destroy
if ($page->is_default) {
    return redirect()->route('admin.pages.index')
        ->with('error', 'Default pages cannot be deleted.');
}
```

## Model Updates

### Post Model Enhancements

```php
// New fillable field
'is_default'

// New cast
protected function casts(): array
{
    return [
        'is_default' => 'boolean',
    ];
}

// Helper method
public function isDefault(): bool
{
    return $this->is_default;
}
```

## Testing

### Comprehensive Test Coverage

```bash
# Run all default pages tests
php artisan test tests/Feature/DefaultPagesSeederTest.php
```

**Tests verify:**

- ✅ All default pages are created with `is_default = true`
- ✅ No duplicate pages when seeder runs multiple times
- ✅ All pages have rich HTML content
- ✅ Default pages cannot be deleted (returns error)
- ✅ Non-default pages can still be deleted
- ✅ Proper database structure is maintained

## Database Queries

### Find All Default Pages

```sql
SELECT id, title, type, is_default FROM posts
WHERE type = 'page' AND is_default = true;
```

### Check Default Page Protection

```sql
-- All default pages should have is_default = 1
SELECT title, is_default FROM posts
WHERE type = 'page' AND title IN (
    'About Us', 'Privacy Policy', 'Terms and Conditions',
    'Travel Guide', 'Sample Page'
);
```

## File Changes Summary

### New Files

- `database/migrations/2025_08_21_022828_add_is_default_to_posts_table.php`

### Updated Files

- `app/Models/Post.php` - Added `is_default` field, casts, and HasFactory trait
- `app/Http/Controllers/Admin/PageController.php` - Added delete protection logic
- `database/seeders/DefaultPagesSeeder.php` - Added `is_default = true` for all pages
- `database/factories/PostFactory.php` - Added `is_default` field and `defaultPage()` state
- `tests/Feature/DefaultPagesSeederTest.php` - Added comprehensive tests

## Security Benefits

- **Prevents Accidental Deletion**: Critical pages cannot be accidentally removed
- **Maintains Site Integrity**: Essential pages like Privacy Policy always exist
- **Administrative Safety**: Reduces risk of breaking site functionality
- **Data Consistency**: Database constraints ensure `is_default` field integrity

## Admin Interface Impact

- Default pages show in the pages list normally
- Delete button/action will show error for default pages
- Edit functionality remains fully available
- No visual changes to the admin interface (pages can be identified by `is_default` field)

## Future Enhancements

Consider adding visual indicators in the admin interface to show which pages are protected (e.g., a lock icon or "Protected" badge).

---

**Note**: Default pages are essential for legal compliance and site functionality. They can be edited but should not be deleted to maintain site integrity.
