<?php

namespace Tests\Unit;

use App\Models\Destination;
use App\Models\Package;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PackageFieldsTest extends TestCase
{
    use RefreshDatabase;

    public function test_package_can_be_created_with_new_boolean_fields(): void
    {
        $destination = Destination::factory()->create();

        $package = Package::factory()->create([
            'destination_id' => $destination->id,
            'add_to_slider' => true,
            'is_featured' => true,
        ]);

        $this->assertInstanceOf(Package::class, $package);
        $this->assertTrue($package->add_to_slider);
        $this->assertTrue($package->is_featured);
        $this->assertIsBool($package->add_to_slider);
        $this->assertIsBool($package->is_featured);
    }

    public function test_package_boolean_fields_default_to_false(): void
    {
        $destination = Destination::factory()->create();

        $package = Package::create([
            'name' => 'Test Package',
            'description' => 'Test Description',
            'destination_id' => $destination->id,
            'base_price' => 999.99,
            'location' => 'Test Location',
            'duration' => 7,
            'add_to_slider' => null, // Explicitly test null conversion
            'is_featured' => null,
        ]);

        // Refresh from database to get default values
        $package->refresh();

        $this->assertFalse($package->add_to_slider);
        $this->assertFalse($package->is_featured);
    }

    public function test_package_boolean_fields_are_fillable(): void
    {
        $fillable = (new Package())->getFillable();

        $this->assertContains('add_to_slider', $fillable);
        $this->assertContains('is_featured', $fillable);
    }

    public function test_package_boolean_fields_are_cast_correctly(): void
    {
        $destination = Destination::factory()->create();

        $package = Package::factory()->create([
            'destination_id' => $destination->id,
            'add_to_slider' => '1',
            'is_featured' => '0',
        ]);

        // The casts should convert string values to boolean
        $this->assertIsBool($package->add_to_slider);
        $this->assertIsBool($package->is_featured);
        $this->assertTrue($package->add_to_slider);
        $this->assertFalse($package->is_featured);
    }
}
