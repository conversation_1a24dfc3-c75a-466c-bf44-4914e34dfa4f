<?php

namespace Tests\Feature;

use App\Models\Post;
use App\Models\User;
use Database\Seeders\DefaultPagesSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DefaultPageDeleteButtonTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        $this->admin = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_admin_pages_index_returns_is_default_field(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pages.index'));

        $response->assertStatus(200);

        // Check that the response includes pages with is_default field
        $pages = $response->getOriginalContent()->getData()['page']['props']['pages']['data'];

        foreach ($pages as $page) {
            $this->assertArrayHasKey('is_default', $page);
            if (in_array($page['title'], ['About Us', 'Privacy Policy', 'Terms and Conditions', 'Travel Guide', 'Sample Page'])) {
                $this->assertTrue($page['is_default'], "Page '{$page['title']}' should have is_default = true");
            }
        }
    }

    public function test_admin_pages_show_returns_is_default_field(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $defaultPage = Post::where('type', 'page')->where('is_default', true)->first();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pages.show', $defaultPage));

        $response->assertStatus(200);

        // Check that the response includes the is_default field
        $pageData = $response->getOriginalContent()->getData()['page']['props']['page'];
        $this->assertArrayHasKey('is_default', $pageData);
        $this->assertTrue($pageData['is_default']);
    }

    public function test_non_default_pages_have_is_default_false(): void
    {
        // Create a non-default page
        $nonDefaultPage = Post::factory()->page()->create([
            'is_default' => false,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pages.show', $nonDefaultPage));

        $response->assertStatus(200);

        $pageData = $response->getOriginalContent()->getData()['page']['props']['page'];
        $this->assertArrayHasKey('is_default', $pageData);
        $this->assertFalse($pageData['is_default']);
    }

    public function test_backend_protection_still_works(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $defaultPage = Post::where('type', 'page')->where('is_default', true)->first();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.pages.destroy', $defaultPage));

        $response->assertRedirect(route('admin.pages.index'));
        $response->assertSessionHas('error', 'Default pages cannot be deleted.');

        // Assert the page still exists
        $this->assertDatabaseHas('posts', [
            'id' => $defaultPage->id,
            'title' => $defaultPage->title,
        ]);
    }
}
