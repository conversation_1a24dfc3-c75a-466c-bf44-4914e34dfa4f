<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SettingsCacheTest extends TestCase
{
    use RefreshDatabase;

    public function test_settings_use_separate_cache_store()
    {
        // Set a setting
        Setting::set('test_key', 'test_value', 'test_group');

        // Get the value to trigger caching
        $this->assertEquals('test_value', Setting::get('test_key'));

        // Verify it's cached in the settings store
        $this->assertTrue(Cache::store('settings')->has('test_key'));

        // Verify it's NOT in the default cache store
        $this->assertFalse(Cache::has('test_key'));
    }

    public function test_settings_cache_isolation()
    {
        // Set something in default cache
        Cache::put('test_key', 'default_cache_value', 60);

        // Set a setting with same key
        Setting::set('test_key', 'settings_cache_value');

        // Verify they don't interfere with each other
        $this->assertEquals('default_cache_value', Cache::get('test_key'));
        $this->assertEquals('settings_cache_value', Setting::get('test_key'));
    }

    public function test_settings_cache_clearing_only_affects_settings()
    {
        // Set something in default cache
        Cache::put('default_key', 'default_value', 60);

        // Set a setting
        Setting::set('setting_key', 'setting_value');

        // Clear settings cache
        Setting::clearCache();

        // Verify default cache is unaffected
        $this->assertEquals('default_value', Cache::get('default_key'));

        // Verify settings cache is cleared
        $this->assertFalse(Cache::store('settings')->has('setting_key'));
    }

    public function test_group_cache_uses_settings_store()
    {
        Setting::set('key1', 'value1', 'test_group');
        Setting::set('key2', 'value2', 'test_group');

        // Get group to trigger caching
        $group = Setting::getGroup('test_group');

        // Verify group cache is in settings store
        $this->assertTrue(Cache::store('settings')->has('group.test_group'));

        // Verify it's not in default cache
        $this->assertFalse(Cache::has('group.test_group'));

        $this->assertEquals([
            'key1' => 'value1',
            'key2' => 'value2'
        ], $group);
    }
}
