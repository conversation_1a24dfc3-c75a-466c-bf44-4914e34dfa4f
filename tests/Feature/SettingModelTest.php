<?php

namespace Tests\Feature;

use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Carbon\Carbon;

class SettingModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush(); // Clear cache before each test
    }

    public function test_can_set_and_get_string_setting()
    {
        Setting::set('site_name', 'Everest Travel', 'general', 'string');
        
        $value = Setting::get('site_name');
        
        $this->assertEquals('Everest Travel', $value);
        $this->assertIsString($value);
    }

    public function test_can_set_and_get_integer_setting()
    {
        Setting::set('max_bookings', 100, 'booking', 'integer');
        
        $value = Setting::get('max_bookings');
        
        $this->assertEquals(100, $value);
        $this->assertIsInt($value);
    }

    public function test_can_set_and_get_float_setting()
    {
        Setting::set('commission_rate', 15.5, 'payment', 'float');
        
        $value = Setting::get('commission_rate');
        
        $this->assertEquals(15.5, $value);
        $this->assertIsFloat($value);
    }

    public function test_can_set_and_get_boolean_setting()
    {
        Setting::set('maintenance_mode', true, 'system', 'boolean');
        
        $value = Setting::get('maintenance_mode');
        
        $this->assertTrue($value);
        $this->assertIsBool($value);
    }

    public function test_can_set_and_get_array_setting()
    {
        $socialMedia = ['facebook', 'twitter', 'instagram'];
        Setting::set('social_platforms', $socialMedia, 'social', 'array');
        
        $value = Setting::get('social_platforms');
        
        $this->assertEquals($socialMedia, $value);
        $this->assertIsArray($value);
    }

    public function test_can_set_and_get_json_setting()
    {
        $config = ['theme' => 'dark', 'language' => 'en'];
        Setting::set('user_preferences', $config, 'ui', 'json');
        
        $value = Setting::get('user_preferences');
        
        $this->assertEquals($config, $value);
        $this->assertIsArray($value);
    }

    public function test_can_set_and_get_date_setting()
    {
        $date = '2025-08-02';
        Setting::set('launch_date', $date, 'general', 'date');
        
        $value = Setting::get('launch_date');
        
        $this->assertEquals($date, $value);
        $this->assertIsString($value);
    }

    public function test_can_set_and_get_datetime_setting()
    {
        $datetime = Carbon::now();
        Setting::set('last_backup', $datetime, 'system', 'datetime');
        
        $value = Setting::get('last_backup');
        
        $this->assertInstanceOf(Carbon::class, $value);
        $this->assertEquals($datetime->toDateTimeString(), $value->toDateTimeString());
    }

    public function test_can_get_settings_by_group()
    {
        Setting::set('site_name', 'Everest Travel', 'general', 'string');
        Setting::set('site_description', 'Best travel agency', 'general', 'string');
        Setting::set('facebook_url', 'https://facebook.com/everest', 'social', 'string');
        
        $generalSettings = Setting::getGroup('general');
        
        $this->assertCount(2, $generalSettings);
        $this->assertEquals('Everest Travel', $generalSettings['site_name']);
        $this->assertEquals('Best travel agency', $generalSettings['site_description']);
        $this->assertArrayNotHasKey('facebook_url', $generalSettings);
    }

    public function test_returns_default_value_when_setting_not_found()
    {
        $value = Setting::get('non_existent_key', 'default_value');
        
        $this->assertEquals('default_value', $value);
    }

    public function test_setting_is_cached()
    {
        Setting::set('cached_setting', 'test_value', 'general', 'string');
        
        // First call should hit the database
        $value1 = Setting::get('cached_setting');
        
        // Second call should hit the cache
        $value2 = Setting::get('cached_setting');
        
        $this->assertEquals($value1, $value2);
        $this->assertEquals('test_value', $value2);
    }

    public function test_cache_is_cleared_when_setting_is_updated()
    {
        Setting::set('updatable_setting', 'original_value', 'general', 'string');
        
        // Get the original value (should be cached)
        $originalValue = Setting::get('updatable_setting');
        $this->assertEquals('original_value', $originalValue);
        
        // Update the setting
        Setting::set('updatable_setting', 'updated_value', 'general', 'string');
        
        // Get the updated value (cache should be cleared)
        $updatedValue = Setting::get('updatable_setting');
        $this->assertEquals('updated_value', $updatedValue);
    }

    public function test_backward_compatibility_with_old_method_signature()
    {
        // Test that the old method signature still works (group defaults to 'general', type to 'string')
        Setting::set('old_style_setting', 'test_value');
        
        $value = Setting::get('old_style_setting');
        
        $this->assertEquals('test_value', $value);
        
        // Verify it was stored with default group and type
        $setting = Setting::where('key', 'old_style_setting')->first();
        $this->assertEquals('general', $setting->group);
        $this->assertEquals('string', $setting->type);
    }
}
