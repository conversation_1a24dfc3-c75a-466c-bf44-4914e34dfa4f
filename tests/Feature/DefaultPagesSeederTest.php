<?php

namespace Tests\Feature;

use App\Models\Post;
use Database\Seeders\DefaultPagesSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DefaultPagesSeederTest extends TestCase
{
    use RefreshDatabase;

    public function test_default_pages_seeder_creates_expected_pages(): void
    {
        // Ensure no pages exist initially
        $this->assertEquals(0, Post::where('type', 'page')->count());

        // Run the seeder
        $this->seed(DefaultPagesSeeder::class);

        // Assert all expected pages were created
        $expectedPages = [
            'About Us',
            'Privacy Policy',
            'Terms and Conditions',
            'Travel Guide',
            'Sample Page',
        ];

        foreach ($expectedPages as $pageTitle) {
            $this->assertDatabaseHas('posts', [
                'title' => $pageTitle,
                'type' => 'page',
                'status' => 'published',
                'is_default' => true,
            ]);
        }

        // Assert the correct number of pages were created
        $this->assertEquals(5, Post::where('type', 'page')->count());
    }

    public function test_seeder_does_not_create_duplicate_pages(): void
    {
        // Run the seeder twice
        $this->seed(DefaultPagesSeeder::class);
        $this->seed(DefaultPagesSeeder::class);

        // Should still only have 5 pages (no duplicates)
        $this->assertEquals(5, Post::where('type', 'page')->count());
    }

    public function test_created_pages_have_content(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $pages = Post::where('type', 'page')->get();

        foreach ($pages as $page) {
            $this->assertNotEmpty($page->content);
            $this->assertStringContainsString('<', $page->content); // Should contain HTML
        }
    }

    public function test_default_pages_are_marked_as_default(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $defaultPages = Post::where('type', 'page')->where('is_default', true)->count();
        $this->assertEquals(5, $defaultPages);
    }

    public function test_default_pages_cannot_be_deleted(): void
    {
        $this->seed(DefaultPagesSeeder::class);

        $defaultPage = Post::where('type', 'page')->where('is_default', true)->first();

        $response = $this->delete(route('admin.pages.destroy', $defaultPage));

        $response->assertRedirect(route('admin.pages.index'));
        $response->assertSessionHas('error', 'Default pages cannot be deleted.');

        // Assert the page still exists
        $this->assertDatabaseHas('posts', [
            'id' => $defaultPage->id,
            'title' => $defaultPage->title,
        ]);
    }

    public function test_non_default_pages_can_be_deleted(): void
    {
        // Create a non-default page
        $nonDefaultPage = Post::factory()->page()->create([
            'is_default' => false,
        ]);

        $response = $this->delete(route('admin.pages.destroy', $nonDefaultPage));

        $response->assertRedirect(route('admin.pages.index'));
        $response->assertSessionHas('success', 'Page deleted successfully.');

        // Assert the page was deleted
        $this->assertDatabaseMissing('posts', [
            'id' => $nonDefaultPage->id,
        ]);
    }
}
