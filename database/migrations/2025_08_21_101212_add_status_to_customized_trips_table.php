<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customized_trips', function (Blueprint $table) {
            $table->enum('status', ['confirmed', 'pending', 'cancelled'])->default('pending')->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customized_trips', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
