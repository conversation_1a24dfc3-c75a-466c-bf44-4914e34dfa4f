<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Add group column to replace the current type functionality
            $table->string('group')->default('general')->after('value');

            // Change type column to support data types instead of categories
            $table->dropColumn('type');
        });

        // Add the new type column with data type enum
        Schema::table('settings', function (Blueprint $table) {
            $table->string('type')->default('string')->after('group'); //'string', 'integer', 'float', 'boolean', 'array', 'json', 'date', 'datetime'
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Remove the group column
            $table->dropColumn('group');

            // Remove the new type column
            $table->dropColumn('type');
        });

        // Restore the original type column
        Schema::table('settings', function (Blueprint $table) {
            $table->enum('type', ['general', 'social_media', 'seo'])->default('general')->after('value');
        });
    }
};
