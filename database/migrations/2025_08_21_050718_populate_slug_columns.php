<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Populate slugs for destinations
        $destinations = DB::table('destinations')->whereNull('slug')->orWhere('slug', '')->get();
        foreach ($destinations as $destination) {
            $slug = Str::slug($destination->name);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (DB::table('destinations')->where('slug', $slug)->where('id', '!=', $destination->id)->exists()) {
                $slug = $originalSlug.'-'.$counter;
                $counter++;
            }

            DB::table('destinations')->where('id', $destination->id)->update(['slug' => $slug]);
        }

        // Populate slugs for activities
        $activities = DB::table('activities')->whereNull('slug')->orWhere('slug', '')->get();
        foreach ($activities as $activity) {
            $slug = Str::slug($activity->name);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (DB::table('activities')->where('slug', $slug)->where('id', '!=', $activity->id)->exists()) {
                $slug = $originalSlug.'-'.$counter;
                $counter++;
            }

            DB::table('activities')->where('id', $activity->id)->update(['slug' => $slug]);
        }

        // Populate slugs for packages
        $packages = DB::table('packages')->whereNull('slug')->orWhere('slug', '')->get();
        foreach ($packages as $package) {
            $slug = Str::slug($package->name);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (DB::table('packages')->where('slug', $slug)->where('id', '!=', $package->id)->exists()) {
                $slug = $originalSlug.'-'.$counter;
                $counter++;
            }

            DB::table('packages')->where('id', $package->id)->update(['slug' => $slug]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear all slugs
        DB::table('destinations')->update(['slug' => null]);
        DB::table('activities')->update(['slug' => null]);
        DB::table('packages')->update(['slug' => null]);
    }
};
