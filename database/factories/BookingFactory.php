<?php

namespace Database\Factories;

use App\Models\Booking;
use App\Models\Package;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookingFactory extends Factory
{
    protected $model = Booking::class;

    public function definition(): array
    {
        $statuses = ['pending', 'confirmed', 'cancelled'];

        return [
            'package_id' => Package::inRandomOrder()->first()?->id ?? Package::factory(),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'whatsapp' => $this->faker->optional(0.7)->phoneNumber(),
            'country_id' => Country::inRandomOrder()->first()?->id ?? Country::factory(),
            'booking_date' => $this->faker->dateTimeBetween('+1 week', '+6 months'),
            'extra_notes' => $this->faker->optional(0.4)->paragraph(),
            'status' => $this->faker->randomElement($statuses),
            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
        ];
    }

    public function pending()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    public function confirmed()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
        ]);
    }

    public function cancelled()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }
}
