<?php

namespace Database\Factories;

use App\Models\Destination;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BlogPost>
 */
class BlogPostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = fake()->sentence(6, true);
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'content' => fake()->paragraphs(5, true),
            'image' => null,
            'status' => fake()->randomElement(['published', 'draft']),
            'destination_id' => fake()->boolean(70) ? Destination::inRandomOrder()->first()?->id : null,
            'published_at' => fake()->optional(0.8)->dateTimeBetween('-6 months', 'now'),
        ];
    }

    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => fake()->dateTimeBetween('-6 months', 'now'),
        ]);
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }

    public function withDestination(): static
    {
        return $this->state(fn (array $attributes) => [
            'destination_id' => Destination::inRandomOrder()->first()?->id ?? Destination::factory(),
        ]);
    }

    public function withoutDestination(): static
    {
        return $this->state(fn (array $attributes) => [
            'destination_id' => null,
        ]);
    }
}
