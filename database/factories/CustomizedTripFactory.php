<?php

namespace Database\Factories;

use App\Models\CustomizedTrip;
use App\Models\Package;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomizedTripFactory extends Factory
{
    protected $model = CustomizedTrip::class;

    public function definition(): array
    {
        $numberOfAdults = $this->faker->numberBetween(1, 8);
        $numberOfChildren = $this->faker->numberBetween(0, 4);
        $tripDuration = $this->faker->numberBetween(5, 30);

        // Calculate estimated budget based on duration and number of people
        $basePrice = $this->faker->numberBetween(100, 300); // per person per day
        $totalPeople = $numberOfAdults + $numberOfChildren;
        $estimatedBudget = $basePrice * $totalPeople * $tripDuration;

        $notes = [
            'Looking for adventure trekking with experienced guides. Prefer organic meals during the trek.',
            'Family trip with elderly parents. Need comfortable accommodations and easy trekking routes.',
            'Photography focused trip. Interested in sunrise views from mountain peaks.',
            'Budget-conscious travelers. Looking for basic but clean accommodations.',
            'Luxury travel experience. Want the best hotels and premium services.',
            'Cultural immersion trip. Interested in visiting local villages and experiencing traditions.',
            'Solo female traveler. Safety is the primary concern.',
            'Honeymoon trip. Looking for romantic destinations and private accommodations.',
            'Adventure sports enthusiasts. Interested in rock climbing, paragliding, and river rafting.',
            'Group of friends celebrating graduation. Looking for fun activities and good food.',
            'Corporate team building trip. Need activities that promote teamwork.',
            'Spiritual journey. Interested in meditation retreats and monastery visits.',
            'Wildlife and nature lovers. Want to see rare birds and animals in their natural habitat.',
            'First time visiting Nepal. Need comprehensive guidance and support.',
            'Experienced trekkers looking for challenging routes and off-beaten paths.',
        ];

        return [
            'full_name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'country_id' => Country::inRandomOrder()->first()?->id ?? Country::factory(),
            'package_id' => $this->faker->optional(0.6)->randomElement(Package::pluck('id')->toArray() ?: [null]),
            'travel_date' => $this->faker->dateTimeBetween('+2 weeks', '+8 months'),
            'trip_duration' => $tripDuration,
            'number_of_adults' => $numberOfAdults,
            'number_of_children' => $numberOfChildren,
            'estimated_budget' => $estimatedBudget,
            'notes' => $this->faker->optional(0.8)->randomElement($notes),
            'created_at' => $this->faker->dateTimeBetween('-2 months', 'now'),
        ];
    }

    public function withPackage()
    {
        return $this->state(fn (array $attributes) => [
            'package_id' => Package::factory(),
        ]);
    }

    public function withoutPackage()
    {
        return $this->state(fn (array $attributes) => [
            'package_id' => null,
        ]);
    }

    public function luxury()
    {
        return $this->state(function (array $attributes) {
            $numberOfAdults = $attributes['number_of_adults'];
            $numberOfChildren = $attributes['number_of_children'];
            $tripDuration = $attributes['trip_duration'];
            $totalPeople = $numberOfAdults + $numberOfChildren;

            return [
                'estimated_budget' => $this->faker->numberBetween(400, 800) * $totalPeople * $tripDuration,
                'notes' => 'Luxury travel experience. Want the best hotels, private guides, and premium services throughout the trip.',
            ];
        });
    }

    public function budget()
    {
        return $this->state(function (array $attributes) {
            $numberOfAdults = $attributes['number_of_adults'];
            $numberOfChildren = $attributes['number_of_children'];
            $tripDuration = $attributes['trip_duration'];
            $totalPeople = $numberOfAdults + $numberOfChildren;

            return [
                'estimated_budget' => $this->faker->numberBetween(50, 150) * $totalPeople * $tripDuration,
                'notes' => 'Budget-conscious travelers. Looking for basic but clean accommodations and local transportation.',
            ];
        });
    }
}
