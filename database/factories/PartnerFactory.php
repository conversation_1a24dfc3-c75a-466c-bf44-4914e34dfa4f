<?php

namespace Database\Factories;

use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

class PartnerFactory extends Factory
{
    protected $model = Partner::class;

    public function definition(): array
    {
        $partnerTypes = ['donation', 'business'];

        $partnerNames = [
            'Himalayan Adventures',
            'Everest Trek Company',
            'Nepal Mountain Guides',
            'Sherpa Expeditions',
            'Annapurna Travels',
            'Mountain Spirit Adventures',
            'High Altitude Trekking',
            'Kathmandu Travel Hub',
            'Peak Adventures Nepal',
            'Mountain Trails Company',
            'Alpine Journey Nepal',
            'Sacred Valley Tours',
            'Himalayan Pathways',
            'Mountain Echo Travels',
            'Everest Gateway',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($partnerNames),
            'type' => $this->faker->randomElement($partnerTypes),
            'description' => $this->faker->paragraph(2),
            'logo' => null,
            'commission_rate' => $this->faker->randomFloat(2, 5, 25), // 5% to 25%
            'balance' => $this->faker->randomFloat(2, 0, 10000),
        ];
    }

    public function business()
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'business',
            'commission_rate' => $this->faker->randomFloat(2, 10, 20),
        ]);
    }

    public function donation()
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'donation',
            'commission_rate' => $this->faker->randomFloat(2, 0, 5),
        ]);
    }
}
