<?php

namespace Database\Factories;

use App\Models\Destination;
use Illuminate\Database\Eloquent\Factories\Factory;

class DestinationFactory extends Factory
{
    protected $model = Destination::class;

    public function definition(): array
    {
        $destinations = [
            'Everest Region',
            'Annapurna Region',
            'Langtang Region',
            'Manaslu Region',
            'Dolpo Region',
            'Mustang Region',
            'Kanchenjunga Region',
            'Makalu Region',
            'Dhaulagiri Region',
            'Rolwaling Region',
        ];

        $descriptions = [
            'Home to the world\'s highest peak, offering spectacular mountain views and Sherpa culture.',
            'Famous for its diverse landscapes, from subtropical forests to high alpine terrain.',
            'Known for its beautiful valleys, sacred lakes, and proximity to Kathmandu.',
            'Remote and pristine region offering authentic mountain culture and stunning scenery.',
            'Hidden and mysterious region with unique Bon culture and dramatic landscapes.',
            'Ancient trans-Himalayan trade route with unique desert-like landscapes.',
            'Eastern region offering pristine wilderness and diverse flora and fauna.',
            'Challenging and remote region for experienced trekkers seeking adventure.',
            'Dramatic and challenging terrain with the world\'s seventh highest peak.',
            'Sacred valley with pristine glacial lakes and challenging high passes.',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($destinations),
            'description' => $this->faker->randomElement($descriptions),
            'image' => null, // We'll handle images separately if needed
        ];
    }
}
