<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

class CountryFactory extends Factory
{
    protected $model = Country::class;

    public function definition(): array
    {
        $countries = [
            ['name' => 'United States', 'code' => 'US'],
            ['name' => 'Canada', 'code' => 'CA'],
            ['name' => 'United Kingdom', 'code' => 'GB'],
            ['name' => 'Australia', 'code' => 'AU'],
            ['name' => 'Germany', 'code' => 'DE'],
            ['name' => 'France', 'code' => 'FR'],
            ['name' => 'Japan', 'code' => 'JP'],
            ['name' => 'India', 'code' => 'IN'],
            ['name' => 'China', 'code' => 'CN'],
            ['name' => 'Brazil', 'code' => 'BR'],
            ['name' => 'Mexico', 'code' => 'MX'],
            ['name' => 'South Korea', 'code' => 'KR'],
            ['name' => 'Italy', 'code' => 'IT'],
            ['name' => 'Spain', 'code' => 'ES'],
            ['name' => 'Netherlands', 'code' => 'NL'],
            ['name' => 'Switzerland', 'code' => 'CH'],
            ['name' => 'New Zealand', 'code' => 'NZ'],
            ['name' => 'Singapore', 'code' => 'SG'],
            ['name' => 'Thailand', 'code' => 'TH'],
            ['name' => 'Malaysia', 'code' => 'MY'],
        ];

        $country = $this->faker->unique()->randomElement($countries);

        return [
            'name' => $country['name'],
            'code' => $country['code'],
            'dial_code' => '+' . $this->faker->numberBetween(1, 999),
        ];
    }
}
