<?php

namespace Database\Factories;

use App\Models\Commission;
use App\Models\Booking;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

class CommissionFactory extends Factory
{
    protected $model = Commission::class;

    public function definition(): array
    {
        $statuses = ['pending', 'paid', 'cancelled'];

        return [
            'booking_id' => Booking::inRandomOrder()->first()?->id ?? Booking::factory(),
            'partner_id' => Partner::inRandomOrder()->first()?->id ?? Partner::factory(),
            'amount' => $this->faker->randomFloat(2, 50, 1000),
            'status' => $this->faker->randomElement($statuses),
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ];
    }

    public function pending()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    public function paid()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
        ]);
    }

    public function cancelled()
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    public function highValue()
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->randomFloat(2, 500, 2000),
        ]);
    }

    public function lowValue()
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->randomFloat(2, 25, 200),
        ]);
    }
}
