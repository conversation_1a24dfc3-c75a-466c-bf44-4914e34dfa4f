<?php

namespace Database\Factories;

use App\Models\ContactMessage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContactMessage>
 */
class ContactMessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ContactMessage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subjects = [
            'Inquiry about Nepal trekking packages',
            'Question about Everest Base Camp trek',
            'Booking inquiry for Annapurna Circuit',
            'Request for custom itinerary',
            'Information about travel dates',
            'Group booking inquiry',
            'Solo traveler package inquiry',
            'Equipment rental information',
            'Travel insurance questions',
            'Visa and permit assistance',
            'Accommodation preferences',
            'Transportation arrangements',
            'Weather and best time to visit',
            'Difficulty level questions',
            'Training recommendations',
            'Budget and pricing inquiry',
            'Cancellation policy questions',
            'Photography tour inquiry',
            'Cultural experience packages',
            'Food and dietary requirements'
        ];

        $messageTemplates = [
            "Hello, I'm interested in your trekking packages to Nepal. Could you please provide more information about the available options and pricing?",
            "Hi there! I'm planning a trip to Nepal and would love to know more about your guided tours. What would you recommend for a first-time visitor?",
            "I'm looking for a challenging trek in the Himalayas. Can you suggest something suitable for experienced hikers?",
            "Could you help me plan a custom itinerary for my family? We're traveling with children and need something less demanding.",
            "I have specific dates in mind for my trip. Are you available from [date] to [date]? What packages would work best?",
            "We're a group of 8 friends interested in trekking together. Do you offer group discounts and what would be included?",
            "I'm a solo female traveler. Do you have packages that would be suitable and safe for solo adventures?",
            "What kind of equipment do I need to bring? Do you provide rental services for trekking gear?",
            "I need information about travel insurance. What do you recommend for trekking in Nepal?",
            "Can you assist with visa applications and trekking permits? What's the process like?",
            "What type of accommodation is included in your packages? Are there different comfort levels available?",
            "How do we get to the starting point of the trek? Is transportation included in the package?",
            "When is the best time to visit Nepal for trekking? I want to avoid the monsoon season.",
            "I'm concerned about the difficulty level. Can you recommend something for intermediate hikers?",
            "What kind of physical preparation do you recommend before attempting these treks?",
            "Could you provide a detailed breakdown of costs? I'm trying to plan my budget.",
            "What's your cancellation policy? I want to understand the terms before booking.",
            "Do you offer photography-focused tours? I'm interested in capturing the landscapes.",
            "I'm interested in experiencing local culture. Do your packages include cultural activities?",
            "I have dietary restrictions (vegetarian/vegan). Can you accommodate special food requirements?"
        ];

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->optional(0.8)->phoneNumber(),
            'subject' => $this->faker->randomElement($subjects),
            'message' => $this->faker->randomElement($messageTemplates),
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Indicate that the contact message is recent.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the contact message is old.
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-2 years', '-6 months'),
        ]);
    }
}
