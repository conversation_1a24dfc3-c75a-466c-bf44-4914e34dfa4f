<?php

namespace Database\Factories;

use App\Models\Package;
use App\Models\Destination;
use Illuminate\Database\Eloquent\Factories\Factory;

class PackageFactory extends Factory
{
    protected $model = Package::class;

    public function definition(): array
    {
        $packageNames = [
            'Everest Base Camp Trek',
            'Annapurna Circuit Trek',
            'Manaslu Circuit Trek',
            'Langtang Valley Trek',
            'Gokyo Lakes Trek',
            'Upper Mustang Trek',
            'Makalu Base Camp Trek',
            'Dhaulagiri Circuit Trek',
            'Kanchenjunga Base Camp Trek',
            'Three Passes Trek',
            'Annapurna Base Camp Trek',
            'Gosaikunda Lake Trek',
            'Poon Hill Trek',
            'Mardi Himal Trek',
            'Tsum Valley Trek',
        ];

        $name = $this->faker->unique()->randomElement($packageNames);
        $difficulty = $this->faker->randomElement(['Easy', 'Moderate', 'Challenging', 'Difficult']);
        $duration = $this->faker->numberBetween(5, 30);

        return [
            'name' => $name,
            'description' => $this->faker->paragraph(3),
            'image' => null,
            'destination_id' => Destination::inRandomOrder()->first()?->id ?? Destination::factory(),
            'base_price' => $this->faker->numberBetween(800, 5000),
            'location' => $this->faker->city(),
            'duration' => $duration,
            'route_map' => null,
            'add_to_slider' => $this->faker->boolean(20), // 20% chance of being true
            'is_featured' => $this->faker->boolean(30), // 30% chance of being true
        ];
    }
}
