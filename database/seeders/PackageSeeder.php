<?php

namespace Database\Seeders;

use App\Models\Activity;
use App\Models\Destination;
use App\Models\Package;
use Illuminate\Database\Seeder;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample destinations if they don't exist
        $destinations = [
            ['name' => 'Everest', 'description' => 'Home to the world\'s highest peak'],
            ['name' => 'Annapurna', 'description' => 'Popular trekking destination'],
            ['name' => 'Langtang', 'description' => 'Close to Kathmandu valley'],
        ];

        foreach ($destinations as $destData) {
            $destination = Destination::firstOrCreate(
                ['name' => $destData['name']],
                $destData
            );

            // Create sample activities for each destination
            $activities = [
                'Everest' => [
                    'Everest Base Camp Trek',
                    'Gokyo Lakes Trek',
                    'Three Passes Trek',
                ],
                'Annapurna' => [
                    'Annapurna Circuit Trek',
                    'Annapurna Base Camp Trek',
                    'Poon Hill Trek',
                ],
                'Langtang' => [
                    'Langtang Valley Trek',
                    'Gosaikunda Trek',
                    'Tamang Heritage Trail',
                ],
            ];

            foreach ($activities[$destination->name] as $activityName) {
                $_activity = Activity::firstOrCreate(
                    ['name' => $activityName],
                    ['description' => 'Amazing '.$activityName.' experience']
                );

                $_activity->destinations()->attach([$destination->id]);
            }
        }

        // Create sample packages
        $packages = [
            [
                'name' => 'Everest Base Camp Adventure',
                'description' => 'Experience the ultimate trekking adventure to the base of the world\'s highest mountain. This challenging trek takes you through Sherpa villages, ancient monasteries, and breathtaking mountain scenery.',
                'destination_id' => Destination::where('name', 'Everest')->first()->id,
                'base_price' => 2500.00,
                'location' => 'Solukhumbu, Nepal',
                'duration' => 14,
                'route_map' => null,
            ],
            [
                'name' => 'Annapurna Circuit Classic',
                'description' => 'One of the world\'s most popular trekking routes, offering diverse landscapes from subtropical forests to high alpine terrain. Cross the challenging Thorong La Pass at 5,416m.',
                'destination_id' => Destination::where('name', 'Annapurna')->first()->id,
                'base_price' => 1800.00,
                'location' => 'Annapurna, Nepal',
                'duration' => 16,
                'route_map' => null,
            ],
            [
                'name' => 'Langtang Valley Explorer',
                'description' => 'Discover the beautiful Langtang Valley, known as the "Valley of Glaciers". This trek offers stunning mountain views, rich culture, and diverse flora and fauna.',
                'destination_id' => Destination::where('name', 'Langtang')->first()->id,
                'base_price' => 1200.00,
                'location' => 'Langtang, Nepal',
                'duration' => 10,
                'route_map' => null,
            ],
        ];

        foreach ($packages as $packageData) {
            $package = Package::firstOrCreate(
                ['name' => $packageData['name']],
                $packageData
            );

            // Add sample activities to package
            $destinationActivities = Activity::whereRelation('destinations', 'destinations.id', $package->destination_id)->take(2)->get();
            $package->activities()->sync($destinationActivities->pluck('id')->toArray());

            // Add sample attributes
            $sampleAttributes = [
                ['name' => 'Maximum Altitude', 'value' => '5000m+'],
                ['name' => 'Best Time', 'value' => 'March-May, September-November'],
                ['name' => 'Difficulty Level', 'value' => 'Moderate to Challenging'],
                ['name' => 'Group Size', 'value' => '2-12 people'],
            ];

            foreach ($sampleAttributes as $attr) {
                $package->attributes()->firstOrCreate(
                    ['name' => $attr['name']],
                    $attr
                );
            }

            // Add sample price tiers
            $samplePrices = [
                [
                    'price' => $package->base_price,
                    'condition' => 'Standard package with basic services',
                ],
                [
                    'price' => $package->base_price * 1.3,
                    'condition' => 'Premium package with upgraded services',
                ],
            ];

            foreach ($samplePrices as $price) {
                $package->prices()->firstOrCreate(
                    ['condition' => $price['condition']],
                    $price
                );
            }

            // Add sample itinerary plans
            $samplePlans = [
                [
                    'day_number' => 1,
                    'plan_name' => 'Arrival and Preparation',
                    'description' => 'Arrive in Kathmandu, meet your guide, and prepare for the trek.',
                ],
                [
                    'day_number' => 2,
                    'plan_name' => 'Begin the Adventure',
                    'description' => 'Fly or drive to the starting point and begin trekking.',
                ],
            ];

            foreach ($samplePlans as $plan) {
                $package->plans()->firstOrCreate(
                    ['day_number' => $plan['day_number']],
                    $plan
                );
            }

            // Add sample cost details
            $sampleCosts = [
                ['description' => 'Domestic flights and accommodation in Kathmandu included', 'cost_type' => 'included'],
                ['description' => 'Professional guide and porter services included', 'cost_type' => 'included'],
                ['description' => 'International flights not included', 'cost_type' => 'excluded'],
                ['description' => 'Personal expenses and travel insurance not included', 'cost_type' => 'excluded'],
            ];

            foreach ($sampleCosts as $cost) {
                $package->costDetails()->firstOrCreate(
                    ['description' => $cost['description']],
                    $cost
                );
            }

            // Add sample FAQs
            $sampleFaqs = [
                [
                    'question' => 'What is the best time to do this trek?',
                    'answer' => 'The best time is during the spring (March-May) and autumn (September-November) seasons when the weather is clear and stable.',
                ],
                [
                    'question' => 'Do I need previous trekking experience?',
                    'answer' => 'While previous experience is helpful, it\'s not mandatory. Good physical fitness and determination are most important.',
                ],
            ];

            foreach ($sampleFaqs as $faq) {
                $package->faqs()->firstOrCreate(
                    ['question' => $faq['question']],
                    $faq
                );
            }
        }
    }
}
