<?php

namespace Database\Seeders;

use App\Models\Booking;
use Illuminate\Database\Seeder;

class BookingSeeder extends Seeder
{
    public function run(): void
    {
        // Create bookings with different statuses
        Booking::factory()->count(25)->pending()->create();
        Booking::factory()->count(18)->confirmed()->create();
        Booking::factory()->count(5)->cancelled()->create();

        $this->command->info('Booking seeder completed: 48 bookings created.');
    }
}
