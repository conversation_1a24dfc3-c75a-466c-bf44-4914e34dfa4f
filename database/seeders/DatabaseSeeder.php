<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a test admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);

        // Create some regular users
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'type' => 'user',
        ]);

        User::factory()->create([
            'name' => 'Partner User',
            'email' => '<EMAIL>',
            'type' => 'partner',
        ]);

        // Create additional test users
        User::factory(2)->create();

        // Seed countries first as foundational data
        $this->call(CountrySeeder::class);

        // Seed messages (contact messages and asked questions)
        $this->call(MessageSeeder::class);

        // Seed default pages
        $this->call(DefaultPagesSeeder::class);

        // Seed settings
        $this->call(SettingsSeeder::class);

        $this->call(PackageSeeder::class);
        $this->call(BookingAndTripSeeder::class);
        $this->call(BlogPostSeeder::class);
    }
}
