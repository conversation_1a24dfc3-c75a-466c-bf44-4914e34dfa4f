<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class BookingAndTripSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Starting Booking and Trip Seeding...');

        $this->call([
            BookingSeeder::class,
            CustomizedTripSeeder::class,
        ]);

        $this->command->info('Booking and Trip seeding completed successfully!');
        $this->command->info('Total: 48 Bookings + 55 Customized Trips = 103 total records');
    }
}
