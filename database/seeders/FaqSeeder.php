<?php

namespace Database\Seeders;

use App\Models\Faq;
use App\Models\FaqGroup;
use Illuminate\Database\Seeder;

class FaqSeeder extends Seeder
{
    public function run(): void
    {
        // Create FAQ Groups
        $bookingGroup = FaqGroup::create(['name' => 'Booking Process']);
        $tourGroup = FaqGroup::create(['name' => 'Tour Details']);
        $cancellationGroup = FaqGroup::create(['name' => 'Cancellation and Refunds']);

        // Create FAQs for Booking Process
        Faq::create([
            'faq_group_id' => $bookingGroup->id,
            'question' => 'How do I book a tour?',
            'answer' => '<p>To book a tour, simply visit our website, select your desired tour, and follow the booking instructions. You can also contact us directly for assistance.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $bookingGroup->id,
            'question' => 'What payment methods do you accept?',
            'answer' => '<p>We accept major credit cards, bank transfers, and PayPal for your convenience.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $bookingGroup->id,
            'question' => 'Do I need to pay a deposit?',
            'answer' => '<p>Yes, we require a 30% deposit to secure your booking. The remaining balance is due 30 days before departure.</p>'
        ]);

        // Create FAQs for Tour Details
        Faq::create([
            'faq_group_id' => $tourGroup->id,
            'question' => 'What is included in the tour package?',
            'answer' => '<p>Our tour packages typically include accommodation, meals, transportation, and guided activities. Specific inclusions vary by tour.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $tourGroup->id,
            'question' => 'What should I bring on the tour?',
            'answer' => '<p>We recommend bringing comfortable walking shoes, weather-appropriate clothing, sunscreen, and a camera. A detailed packing list will be provided upon booking.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $tourGroup->id,
            'question' => 'Are meals included in the tour?',
            'answer' => '<p>Most of our tours include breakfast and dinner. Lunch arrangements vary by tour and will be specified in your itinerary.</p>'
        ]);

        // Create FAQs for Cancellation and Refunds
        Faq::create([
            'faq_group_id' => $cancellationGroup->id,
            'question' => 'What is your cancellation policy?',
            'answer' => '<p>Cancellations made 30 days before departure receive a full refund. Cancellations within 30 days may incur fees depending on the tour type.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $cancellationGroup->id,
            'question' => 'Can I get a refund if I cancel due to illness?',
            'answer' => '<p>We understand that unexpected situations arise. Please contact us immediately if you need to cancel due to illness, and we will work with you on a case-by-case basis.</p>'
        ]);

        Faq::create([
            'faq_group_id' => $cancellationGroup->id,
            'question' => 'What happens if the tour is cancelled by the company?',
            'answer' => '<p>In the rare event that we need to cancel a tour, you will receive a full refund or the option to reschedule to a different date.</p>'
        ]);
    }
}