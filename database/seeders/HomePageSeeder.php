<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class HomePageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedHomePageSettings();
    }

    /**
     * Seed home page settings with default content
     */
    private function seedHomePageSettings(): void
    {
        // Hero Section
        Setting::setMany('hero', [
            'hero_heading' => 'Discover the Himalayas with Expert Guides',
            'hero_sub_heading' => 'Experience breathtaking adventures in Nepal with our professional trekking and tour services. From Everest Base Camp to Annapurna Circuit, create memories that last a lifetime.',
            'hero_background_image' => '/assets/hero-bg.png',
            'hero_button_text' => 'Start Your Adventure',
            'hero_button_url' => '/packages',
            'hero_video_url' => 'https://www.youtube.com/watch?v=example',
        ]);

        // Top Destination Section
        Setting::setMany('top_destination', [
            'top_destination_pre_heading' => 'Popular Destinations',
            'top_destination_heading' => 'Explore Nepal\'s Most Beautiful Places',
            'top_destination_sub_heading' => 'From towering peaks to ancient temples, discover the diverse landscapes and rich culture of Nepal through our carefully curated destinations.',
            'top_destination_button_text' => 'View All Destinations',
            'top_destination_button_url' => '/destinations',
        ]);

        // Plan Your Trip Section
        Setting::setMany('plan_trip', [
            'plan_trip_pre_heading' => 'Plan Your Journey',
            'plan_trip_heading' => 'How to Plan Your Perfect Adventure',
            'plan_trip_sub_heading' => 'Follow our simple steps to create an unforgettable experience tailored to your preferences and adventure level.',
            'plan_trip_background_image_1' => '/assets/plan-trip-1.png',
            'plan_trip_background_image_2' => '/assets/plan-trip-2.png',
            'plan_trip_background_image_3' => '/assets/plan-trip-3.png',
        ]);

        // Best Offers Section
        Setting::setMany('best_offers', [
            'best_offers_pre_heading' => 'Special Deals',
            'best_offers_heading' => 'Best Offers for Your Next Adventure',
            'best_offers_sub_heading' => 'Take advantage of our exclusive packages and seasonal discounts. Book now and save on your dream Himalayan adventure.',
            'best_offers_button_text' => 'View All Offers',
            'best_offers_button_url' => '/packages?filter=offers',
        ]);

        // Popular Packages Section
        Setting::setMany('popular_packages', [
            'popular_packages_pre_heading' => 'Featured Tours',
            'popular_packages_heading' => 'Most Popular Adventure Packages',
            'popular_packages_sub_heading' => 'Discover our most loved trekking and tour packages, carefully designed to showcase the best of Nepal\'s natural beauty and cultural heritage.',
            'popular_packages_button_text' => 'Browse All Packages',
            'popular_packages_button_url' => '/packages',
        ]);

        // Travel Experience Section
        Setting::setMany('travel_experience', [
            'travel_experience_pre_heading' => 'Why Choose Us',
            'travel_experience_heading' => 'Unmatched Travel Experience',
            'travel_experience_sub_heading' => 'With years of expertise and local knowledge, we provide safe, authentic, and memorable adventures in the heart of the Himalayas.',
            'travel_experience_background_image' => '/assets/img-landscape.png',
            'travel_experience_button_text' => 'Learn More',
            'travel_experience_button_url' => '/about',
        ]);

        // Solabans Village Section
        Setting::setMany('solabans_village', [
            'solabans_village_pre_heading' => 'Cultural Heritage',
            'solabans_village_heading' => 'Experience Authentic Village Life',
            'solabans_village_sub_heading' => 'Immerse yourself in the rich traditions and warm hospitality of local communities. Discover ancient customs and breathtaking mountain views.',
            'solabans_village_button_text' => 'Explore Villages',
            'solabans_village_button_url' => '/cultural-tours',
        ]);

        // Testimonials Section
        Setting::setMany('testimonials', [
            'testimonials_pre_heading' => 'What Our Clients Say',
            'testimonials_heading' => 'Stories from Fellow Adventurers',
            'testimonials_sub_heading' => 'Read authentic reviews from travelers who have experienced the magic of Nepal with our expert guides and personalized service.',
        ]);

        // Review and Brands Section
        Setting::setMany('review_brands', [
            'review_brands_heading' => 'Trusted by Thousands of Travelers',
            'review_brands_sub_heading' => 'Join the community of satisfied adventurers who have chosen us for their Himalayan journey. See why we\'re rated as one of Nepal\'s top adventure companies.',
        ]);

        // News Section
        Setting::setMany('news', [
            'news_pre_heading' => 'Latest Updates',
            'news_heading' => 'Adventure News & Travel Tips',
            'news_sub_heading' => 'Stay informed with the latest news from the mountains, travel tips, and insights to help you prepare for your next adventure in Nepal.',
        ]);
    }
}
