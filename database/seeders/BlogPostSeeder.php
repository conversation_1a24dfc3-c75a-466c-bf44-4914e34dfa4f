<?php

namespace Database\Seeders;

use App\Models\BlogPost;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BlogPostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 15 published blog posts
        BlogPost::factory()
            ->count(15)
            ->published()
            ->create();

        // Create 5 draft blog posts
        BlogPost::factory()
            ->count(5)
            ->draft()
            ->create();

        // Create 8 blog posts with destinations
        BlogPost::factory()
            ->count(8)
            ->published()
            ->withDestination()
            ->create();

        // Create 3 blog posts without destinations
        BlogPost::factory()
            ->count(3)
            ->published()
            ->withoutDestination()
            ->create();
    }
}
