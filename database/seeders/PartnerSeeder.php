<?php

namespace Database\Seeders;

use App\Models\Partner;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    public function run(): void
    {
        // Create different types of partners
        Partner::factory()->count(8)->business()->create();
        Partner::factory()->count(4)->donation()->create();
        Partner::factory()->count(3)->create(); // Random types

        $this->command->info('Partner seeder completed: 15 partners created.');
    }
}
