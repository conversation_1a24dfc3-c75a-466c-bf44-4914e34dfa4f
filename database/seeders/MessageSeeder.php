<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Message Seeding...');

        // Seed Contact Messages
        $this->call(ContactMessageSeeder::class);

        // Seed Asked Questions
        $this->call(AskedQuestionSeeder::class);

        $this->command->info('Message seeding completed successfully!');
        $this->command->info('Total: 55 Contact Messages + 55 Asked Questions = 110 total messages');
    }
}
