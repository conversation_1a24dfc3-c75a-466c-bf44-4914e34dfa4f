<?php

namespace Database\Seeders;

use App\Models\CustomizedTrip;
use Illuminate\Database\Seeder;

class CustomizedTripSeeder extends Seeder
{
    public function run(): void
    {
        // Create regular customized trips
        CustomizedTrip::factory()->count(25)->create();

        // Create some luxury trips
        CustomizedTrip::factory()->count(8)->luxury()->create();

        // Create some budget trips
        CustomizedTrip::factory()->count(12)->budget()->create();

        // Create trips without base packages (fully custom)
        CustomizedTrip::factory()->count(10)->withoutPackage()->create();

        $this->command->info('CustomizedTrip seeder completed: 55 customized trips created.');
    }
}
