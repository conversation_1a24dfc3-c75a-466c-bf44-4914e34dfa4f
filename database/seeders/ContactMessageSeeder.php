<?php

namespace Database\Seeders;

use App\Models\ContactMessage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactMessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 50 contact messages with a mix of recent and older ones
        ContactMessage::factory()
            ->count(30)
            ->create();

        // Create 15 recent contact messages (within last week)
        ContactMessage::factory()
            ->count(15)
            ->recent()
            ->create();

        // Create 10 older contact messages (older than 6 months)
        ContactMessage::factory()
            ->count(10)
            ->old()
            ->create();

        $this->command->info('ContactMessage seeder completed: 55 contact messages created.');
    }
}
