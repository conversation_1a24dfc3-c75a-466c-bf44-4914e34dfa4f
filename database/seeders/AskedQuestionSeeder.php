<?php

namespace Database\Seeders;

use App\Models\AskedQuestion;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AskedQuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 40 regular asked questions
        AskedQuestion::factory()
            ->count(25)
            ->create();

        // Create 12 recent asked questions (within last week)
        AskedQuestion::factory()
            ->count(12)
            ->recent()
            ->create();

        // Create 8 older asked questions
        AskedQuestion::factory()
            ->count(8)
            ->old()
            ->create();

        // Create 10 short questions
        AskedQuestion::factory()
            ->count(10)
            ->shortQuestion()
            ->create();

        $this->command->info('AskedQuestion seeder completed: 55 asked questions created.');
    }
}
