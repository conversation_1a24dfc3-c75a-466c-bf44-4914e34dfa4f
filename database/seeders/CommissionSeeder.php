<?php

namespace Database\Seeders;

use App\Models\Commission;
use Illuminate\Database\Seeder;

class CommissionSeeder extends Seeder
{
    public function run(): void
    {
        // Create commissions with different statuses and amounts
        Commission::factory()->count(30)->pending()->create();
        Commission::factory()->count(25)->paid()->create();
        Commission::factory()->count(8)->cancelled()->create();

        // Create some high-value commissions
        Commission::factory()->count(12)->highValue()->create();

        // Create some low-value commissions
        Commission::factory()->count(10)->lowValue()->create();

        $this->command->info('Commission seeder completed: 85 commissions created.');
    }
}
