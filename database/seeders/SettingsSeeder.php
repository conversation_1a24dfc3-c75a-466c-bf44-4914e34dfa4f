<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed settings according to settings_values.md
        // General
        Setting::setMany('general', [
            'business_name' => 'Everest Sherpa Adventure',
            'office_time' => 'Sun-Fri 10:00 - 18:00',
            'address_short' => 'Boudha 5, Kathmandu, Nepal',
            'address_full' => 'Thamel, Kathmandu, Nepal',
            'email' => '<EMAIL>',
            'phone' => '+977-1-1234567',
            'whatsapp' => '+977-9800000000',
        ]);

        // Social
        Setting::setMany('social', [
            'facebook_link' => 'https://facebook.com/esherpa',
            'twitter_link' => 'https://twitter.com/esherpa',
            'instagram_link' => 'https://instagram.com/esherpa',
        ]);

        // Contact
        Setting::setMany('contact', [
            'email_address_1' => '<EMAIL>',
            'email_address_2' => '<EMAIL>',
            'phone_number_1' => '+977-1-1234567',
            'phone_number_2' => '+977-1-7654321',
            'head_office_address' => 'Thamel, Kathmandu, Nepal',
            'map_embed_link' => '',
            'receiving_email' => '<EMAIL>',
        ]);

        // Email SMTP
        Setting::setMany('email', [
            'smtp_host' => 'sandbox.smtp.mailtrap.io',
            'smtp_port' => '2525',
            'smtp_username' => 'bb748ac717e647',
            'smtp_password' => 'dd68ec43095c32',
            'smtp_encryption' => 'tls',
        ]);

        // Footer
        Setting::setMany('footer', [
            'footer_useful_links' => [
                ['label' => 'About Us', 'url' => '/about'],
                ['label' => 'Contact', 'url' => '/contact'],
            ],
            'footer_company_links' => [
                ['label' => 'Terms', 'url' => '/terms'],
                ['label' => 'Privacy', 'url' => '/privacy'],
            ],
        ]);

        // Run Home Page Settings Seeder
        $this->call(HomePageSeeder::class);
    }
}
