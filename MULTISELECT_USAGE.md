# MultiSelect Component Usage

The `MultiSelect` component is a reusable multi-select dropdown component built on top of `react-select` that integrates seamlessly with the admin design system.

## Basic Usage

```jsx
import { MultiSelect } from '@admin/components/ui/multi-select.jsx';

function ExampleForm() {
    const [selectedValues, setSelectedValues] = useState([]);

    const options = [
        { value: 1, label: 'Option 1' },
        { value: 2, label: 'Option 2' },
        { value: 3, label: 'Option 3' },
    ];

    return (
        <MultiSelect
            options={options}
            value={selectedValues}
            onChange={setSelectedValues}
            placeholder="Select options..."
        />
    );
}
```

## Props

| Prop               | Type                                 | Default                    | Description                       |
| ------------------ | ------------------------------------ | -------------------------- | --------------------------------- |
| `options`          | `Array<{value: any, label: string}>` | `[]`                       | Array of options to display       |
| `value`            | `Array<{value: any, label: string}>` | `[]`                       | Currently selected values         |
| `onChange`         | `function`                           | -                          | Callback when selection changes   |
| `placeholder`      | `string`                             | `"Select options..."`      | Placeholder text                  |
| `isDisabled`       | `boolean`                            | `false`                    | Whether the input is disabled     |
| `isClearable`      | `boolean`                            | `true`                     | Whether the input can be cleared  |
| `isSearchable`     | `boolean`                            | `true`                     | Whether the input is searchable   |
| `noOptionsMessage` | `function`                           | `() => 'No options found'` | Message when no options available |
| `size`             | `'sm' \| 'md' \| 'lg'`               | `'md'`                     | Size variant                      |
| `className`        | `string`                             | -                          | Additional CSS classes            |

## Size Variants

```jsx
// Small
<MultiSelect size="sm" options={options} value={value} onChange={onChange} />

// Medium (default)
<MultiSelect options={options} value={value} onChange={onChange} />

// Large
<MultiSelect size="lg" options={options} value={value} onChange={onChange} />
```

## With Form Field

```jsx
<div className="space-y-2">
    <Label htmlFor="activities">Activities</Label>
    <MultiSelect
        options={activities.map((activity) => ({
            value: activity.id,
            label: activity.name,
        }))}
        value={selectedActivities}
        onChange={setSelectedActivities}
        placeholder="Select activities..."
        noOptionsMessage={() => 'No activities found'}
    />
    {errors.activities && (
        <p className="text-sm text-red-600">{errors.activities}</p>
    )}
</div>
```

## Filtering Options

```jsx
const filteredOptions = activities
    .filter((activity) => activity.destination_id === selectedDestination)
    .map((activity) => ({
        value: activity.id,
        label: activity.name,
    }));

<MultiSelect
    options={filteredOptions}
    value={selectedActivities}
    onChange={setSelectedActivities}
    placeholder="Select activities for this destination..."
/>;
```

## Features

- **Design System Integration**: Matches your existing UI components
- **Size Variants**: Small, medium, and large sizes
- **Search Functionality**: Built-in search/filter capability
- **Keyboard Navigation**: Full keyboard accessibility
- **Custom Styling**: Integrates with CSS custom properties
- **Loading States**: Support for async loading
- **Error States**: Proper error styling integration
- **Focus Management**: Proper focus ring and states
- **Dark Mode**: Automatic dark mode support

## Styling

The component automatically inherits your design system colors and styling:

- Uses CSS custom properties from your theme
- Matches border radius and spacing
- Integrates with focus ring system
- Supports hover and active states
- Works with dark mode

The component is built to be consistent with other form components in your admin panel.
