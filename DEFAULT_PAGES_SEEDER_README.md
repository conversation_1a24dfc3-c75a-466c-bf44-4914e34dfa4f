# Default Pages Seeder

## Overview

The `DefaultPagesSeeder` creates essential pages for the Everest Sherpa Adventure website. These pages provide important information for visitors and establish the foundation for the site's content structure.

## Created Pages

### 1. About Us

- **Purpose**: Introduces the company, its story, and team
- **Content**: Company history, services, team information, and commitments
- **Features**: Professional content about Everest Sherpa Adventure's expertise and values

### 2. Privacy Policy

- **Purpose**: Legal document outlining data collection and usage policies
- **Content**: Information collection, usage, sharing, security, and user rights
- **Compliance**: Essential for GDPR and privacy regulation compliance

### 3. Terms and Conditions

- **Purpose**: Legal agreement between the company and customers
- **Content**: Booking policies, cancellation terms, liability, and dispute resolution
- **Importance**: Protects the business and sets clear expectations

### 4. Travel Guide

- **Purpose**: Helpful information for travelers visiting Nepal
- **Content**: Best times to visit, popular routes, packing guides, cultural etiquette
- **Value**: Establishes expertise and provides value to potential customers

### 5. Sample Page

- **Purpose**: Template/example page for content management
- **Content**: Demonstrates features and how to use the CMS
- **Usage**: Can be customized for any purpose or used as a starting template

## Usage

### Running the Seeder

```bash
# Run just the default pages seeder
php artisan db:seed --class=DefaultPagesSeeder

# Or run all seeders (includes default pages)
php artisan db:seed
```

### Features

- **Duplicate Prevention**: Uses `firstOrCreate()` to prevent duplicate pages
- **Rich Content**: All pages include properly formatted HTML content
- **Published Status**: All pages are created with 'published' status
- **Type Classification**: All pages are properly tagged as 'page' type

### Testing

The seeder includes comprehensive tests that verify:

- All expected pages are created
- No duplicate pages are created when run multiple times
- All pages have content
- Proper database structure is maintained

```bash
# Run the tests
php artisan test tests/Feature/DefaultPagesSeederTest.php
```

## Integration

The seeder is automatically included in the main `DatabaseSeeder` class and will run as part of the standard database seeding process.

## Customization

To modify the content of any page:

1. Edit the respective content method in `DefaultPagesSeeder.php`
2. Re-run the seeder to update existing pages
3. Or manually edit through the admin interface after seeding

## File Structure

- **Seeder**: `database/seeders/DefaultPagesSeeder.php`
- **Factory**: `database/factories/PostFactory.php` (created for flexibility)
- **Tests**: `tests/Feature/DefaultPagesSeederTest.php`
- **Model**: `app/Models/Post.php` (existing)
