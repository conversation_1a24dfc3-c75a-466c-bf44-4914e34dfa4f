# User CRUD System for Admin Panel

This document describes the User CRUD (Create, Read, Update, Delete) system implemented for the admin panel.

## Features

### ✅ Complete CRUD Operations

- **Create**: Add new users via modal form
- **Read**: View all users with pagination and search
- **Update**: Edit existing users via modal form
- **Delete**: Remove users with confirmation dialog

### ✅ Modal-based Forms

- **Add User Modal**: Clean form for creating new users
- **Edit User Modal**: Pre-populated form for updating users
- **Confirmation Dialog**: Safety prompt before user deletion

### ✅ Advanced Functionality

- **Search & Filter**: Search by name/email, filter by user type
- **Pagination**: Efficient data loading with page navigation
- **Responsive Design**: Works on all screen sizes
- **Toast Notifications**: Success/error feedback
- **Form Validation**: Client and server-side validation

## File Structure

### Backend (Laravel)

```
app/Http/Controllers/Admin/
├── UserController.php          # Main CRUD controller

routes/
├── admin.php                   # Admin routes configuration

config/
├── ziggy.php                   # Route configuration for frontend
```

### Frontend (React/Inertia.js)

```
resources/js/admin/
├── pages/users/
│   └── index.jsx               # Main users management page
├── config/
│   └── menu.config.jsx         # Navigation menu configuration
├── layouts/
│   └── LayoutTemplate.jsx      # Updated with Toaster component
└── components/ui/              # Shared UI components used
    ├── dialog.jsx
    ├── alert-dialog.jsx
    ├── data-grid.jsx
    ├── pagination.jsx
    └── sonner.jsx
```

## API Routes

| Method | Route                 | Description                            |
| ------ | --------------------- | -------------------------------------- |
| GET    | `/admin/users`        | List users with pagination and filters |
| POST   | `/admin/users`        | Create new user                        |
| PUT    | `/admin/users/{user}` | Update existing user                   |
| DELETE | `/admin/users/{user}` | Delete user                            |

## Usage

### Accessing the Users Management

1. Login to admin panel
2. Navigate to "Manage Users" → "All Users" in the sidebar
3. The users index page will display with all features

### Adding a New User

1. Click "Add User" button
2. Fill in the modal form:
    - Name (required)
    - Email (required, must be unique)
    - Password (required, min 8 characters)
    - Confirm Password (required)
    - Type (optional)
3. Click "Create User"

### Editing a User

1. Click the edit icon (pencil) next to any user
2. Modify fields in the modal form
3. Leave password fields empty to keep current password
4. Click "Update User"

### Deleting a User

1. Click the delete icon (trash) next to any user
2. Confirm deletion in the alert dialog
3. Note: Users cannot delete their own account

### Search and Filter

- Use the search box to find users by name or email
- Use the type filter dropdown to filter by user type
- Click "Search" to apply filters
- Click "Clear" to remove all filters

## Database Schema

The system uses the existing users table with these fields:

- `id` - Primary key
- `name` - User's full name
- `email` - Email address (unique)
- `password` - Hashed password
- `type` - User type (admin, user, partner, etc.)
- `email_verified_at` - Email verification timestamp
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

## Validation Rules

### Create User

- Name: Required, string, max 255 characters
- Email: Required, valid email, max 255 characters, unique
- Password: Required, string, min 8 characters, confirmed
- Type: Optional, string, max 255 characters

### Update User

- Name: Required, string, max 255 characters
- Email: Required, valid email, max 255 characters, unique (except current user)
- Password: Optional, string, min 8 characters, confirmed
- Type: Optional, string, max 255 characters

## Security Features

- **Authentication Required**: All routes require admin authentication
- **Self-Deletion Prevention**: Users cannot delete their own account
- **Password Hashing**: All passwords are automatically hashed
- **CSRF Protection**: All forms include CSRF token protection
- **Input Validation**: Both client and server-side validation

## Navigation Integration

The users management has been integrated into the admin sidebar navigation:

- Menu: "Manage Users" → "All Users"
- Route: `/admin/users`

## Toast Notifications

The system provides user feedback through toast notifications:

- Success messages for successful operations
- Error messages for failed operations
- Automatic dismissal after a few seconds

## Test Data

The system includes seeded test data:

- Admin user: <EMAIL>
- Regular user: <EMAIL>
- Partner user: <EMAIL>
- 10 additional randomly generated users

## Dependencies

### Backend

- Laravel Framework
- Inertia.js Laravel Adapter

### Frontend

- React
- Inertia.js React Adapter
- Lucide React (icons)
- Sonner (toast notifications)
- Custom UI components (shadcn/ui style)

## Future Enhancements

Potential improvements that could be added:

- Bulk operations (select multiple users)
- User role/permission management
- Email verification management
- User activity logging
- Export functionality
- Advanced filtering options
- User profile image upload
