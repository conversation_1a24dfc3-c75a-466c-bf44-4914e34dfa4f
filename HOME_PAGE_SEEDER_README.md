# Home Page Settings Seeder

This document explains the home page settings seeder that populates default content for all home page sections.

## Overview

The home page settings seeder is a separate `HomePageSeeder.php` class that creates default content for 10 different home page sections:

1. **Hero Section** - Main banner with heading, subheading, and call-to-action
2. **Top Destination** - Featured destinations section
3. **Plan Your Trip** - Trip planning section with images
4. **Best Offers** - Special deals and promotions
5. **Popular Packages** - Featured travel packages
6. **Travel Experience** - Experience showcase with background image
7. **Solabans Village** - Cultural heritage section
8. **Testimonials** - Customer reviews section
9. **Review and Brands** - Trust indicators and partnerships
10. **News** - Latest updates and articles

## Running the Seeder

### Run Only Home Page Seeder
```bash
php artisan db:seed --class=HomePageSeeder
```

### Run Settings Seeder (includes home page settings)
```bash
php artisan db:seed --class=SettingsSeeder
```

### Run All Seeders (includes home page settings)
```bash
php artisan db:seed
```

## Default Content

The seeder creates realistic default content for each section:

- **Headings**: Professional and engaging titles
- **Descriptions**: Compelling copy that reflects the adventure travel theme
- **Images**: References to existing assets in the `/assets/` directory
- **URLs**: Logical internal links to relevant pages
- **Button Text**: Clear call-to-action labels

## Database Structure

All settings are stored in the `settings` table with:
- `group`: The section name (e.g., 'hero', 'top_destination')
- `key`: The specific field name (e.g., 'hero_heading', 'hero_sub_heading')
- `value`: The content value
- `type`: Data type (string, text, url)

## Verification

To verify the seeder worked correctly:

```bash
# Check total home page settings count (should be 70)
php artisan tinker --execute="echo App\Models\Setting::whereIn('group', ['hero', 'top_destination', 'plan_trip', 'best_offers', 'popular_packages', 'travel_experience', 'solabans_village', 'testimonials', 'review_brands', 'news'])->count();"

# Check specific section (e.g., hero section should have 6 settings)
php artisan tinker --execute="echo App\Models\Setting::where('group', 'hero')->count();"
```

## Admin Interface

After running the seeder, you can:

1. Visit `/admin/settings/home-page` to view and edit all sections
2. Each section is organized in collapsible cards for easy management
3. All fields are pre-populated with the seeded default content
4. Changes are saved per section using the appropriate form submission

## Customization

To modify the default content:

1. Edit the `seedHomePageSettings()` method in `database/seeders/HomePageSeeder.php`
2. Update the values for any specific field
3. Re-run the seeder to update existing settings
4. Or manually edit through the admin interface

## Integration

The `HomePageSeeder` is called by the `SettingsSeeder`, which is automatically included when running the main `DatabaseSeeder` class, ensuring all new installations have proper default content for the home page.

## Seeder Structure

- **`HomePageSeeder.php`**: Standalone seeder for home page settings only
- **`SettingsSeeder.php`**: General settings seeder that calls `HomePageSeeder`
- **`DatabaseSeeder.php`**: Main seeder that calls `SettingsSeeder`

This modular approach allows you to:
- Run home page settings independently
- Maintain separation of concerns
- Easily modify home page content without affecting other settings