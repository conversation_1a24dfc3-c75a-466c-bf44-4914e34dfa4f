<?php

/**
 * Setting Model Usage Examples
 * 
 * This file demonstrates how to use the enhanced Setting model with:
 * - Group-based organization
 * - Data type casting
 * - Caching system
 */

use App\Models\Setting;
use Carbon\Carbon;

// ============================================================================
// BASIC USAGE
// ============================================================================

// Set a simple string setting
Setting::set('site_name', 'Everest Travel Agency', 'general', 'string');

// Get a setting (automatically cached and type-cast)
$siteName = Setting::get('site_name'); // Returns: "Everest Travel Agency" (string)

// Get with default value
$theme = Setting::get('site_theme', 'light'); // Returns: "light" if not set

// ============================================================================
// DATA TYPE EXAMPLES
// ============================================================================

// String settings
Setting::set('company_email', '<EMAIL>', 'contact', 'string');
Setting::set('company_phone', '+977-1-4567890', 'contact', 'string');

// Integer settings
Setting::set('max_bookings_per_day', 50, 'booking', 'integer');
Setting::set('session_timeout', 3600, 'system', 'integer');

// Float settings
Setting::set('default_commission_rate', 12.5, 'payment', 'float');
Setting::set('currency_conversion_rate', 132.45, 'payment', 'float');

// Boolean settings
Setting::set('maintenance_mode', false, 'system', 'boolean');
Setting::set('email_notifications', true, 'notification', 'boolean');

// Array settings
Setting::set('supported_languages', ['en', 'ne', 'hi'], 'localization', 'array');
Setting::set('payment_methods', ['credit_card', 'paypal', 'bank_transfer'], 'payment', 'array');

// JSON settings (for complex data structures)
Setting::set('seo_meta', [
    'title' => 'Best Travel Agency in Nepal',
    'description' => 'Explore Nepal with our expert guides',
    'keywords' => ['nepal', 'travel', 'trekking', 'everest']
], 'seo', 'json');

// Date settings
Setting::set('business_start_date', '2020-01-15', 'general', 'date');

// DateTime settings
Setting::set('last_backup', Carbon::now(), 'system', 'datetime');
Setting::set('next_maintenance', Carbon::now()->addDays(7), 'system', 'datetime');

// ============================================================================
// RETRIEVING SETTINGS WITH AUTOMATIC TYPE CASTING
// ============================================================================

// These will be automatically cast to their proper types:
$maxBookings = Setting::get('max_bookings_per_day'); // Returns: 50 (integer)
$commissionRate = Setting::get('default_commission_rate'); // Returns: 12.5 (float)
$maintenanceMode = Setting::get('maintenance_mode'); // Returns: false (boolean)
$languages = Setting::get('supported_languages'); // Returns: ['en', 'ne', 'hi'] (array)
$seoMeta = Setting::get('seo_meta'); // Returns: associative array
$lastBackup = Setting::get('last_backup'); // Returns: Carbon instance

// ============================================================================
// GROUP-BASED RETRIEVAL
// ============================================================================

// Get all settings for a specific group
$contactSettings = Setting::getGroup('contact');
// Returns: [
//     'company_email' => '<EMAIL>',
//     'company_phone' => '+977-1-4567890'
// ]

$systemSettings = Setting::getGroup('system');
// Returns: [
//     'session_timeout' => 3600,
//     'maintenance_mode' => false,
//     'last_backup' => Carbon instance,
//     'next_maintenance' => Carbon instance
// ]

// ============================================================================
// PRACTICAL USAGE EXAMPLES
// ============================================================================

// Example 1: Application Configuration
Setting::set('app_name', 'Everest Travel', 'general', 'string');
Setting::set('app_version', '1.0.0', 'general', 'string');
Setting::set('debug_mode', false, 'system', 'boolean');
Setting::set('cache_ttl', 3600, 'system', 'integer');

// Example 2: Social Media Links
Setting::set('facebook_url', 'https://facebook.com/everest-travel', 'social_media', 'string');
Setting::set('instagram_url', 'https://instagram.com/everest_travel', 'social_media', 'string');
Setting::set('twitter_url', 'https://twitter.com/everest_travel', 'social_media', 'string');

// Example 3: Business Rules
Setting::set('min_booking_advance_days', 7, 'booking_rules', 'integer');
Setting::set('cancellation_fee_percentage', 25.0, 'booking_rules', 'float');
Setting::set('allow_same_day_booking', false, 'booking_rules', 'boolean');

// Example 4: Email Configuration
Setting::set('smtp_settings', [
    'host' => 'smtp.gmail.com',
    'port' => 587,
    'encryption' => 'tls',
    'username' => '<EMAIL>'
], 'email', 'json');

// ============================================================================
// CACHING BEHAVIOR
// ============================================================================

// First call hits the database and caches the result
$siteName = Setting::get('site_name');

// Subsequent calls use the cached value (much faster)
$siteName = Setting::get('site_name'); // From cache

// When you update a setting, cache is automatically cleared
Setting::set('site_name', 'New Everest Travel Agency', 'general', 'string');

// Next call will hit the database again and cache the new value
$newSiteName = Setting::get('site_name'); // "New Everest Travel Agency"

// ============================================================================
// CACHE MANAGEMENT
// ============================================================================

// Clear all settings cache (use sparingly)
Setting::clearCache();

// ============================================================================
// BACKWARD COMPATIBILITY
// ============================================================================

// Old method signature still works (defaults: group='general', type='string')
Setting::set('legacy_setting', 'some value');
$legacyValue = Setting::get('legacy_setting'); // Works as before

// ============================================================================
// USAGE IN CONTROLLERS
// ============================================================================

/*
class SettingsController extends Controller
{
    public function getAppSettings()
    {
        return response()->json([
            'general' => Setting::getGroup('general'),
            'contact' => Setting::getGroup('contact'),
            'social_media' => Setting::getGroup('social_media'),
        ]);
    }
    
    public function updateSetting(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string',
            'value' => 'required',
            'group' => 'required|string',
            'type' => 'required|in:string,integer,float,boolean,array,json,date,datetime'
        ]);
        
        Setting::set(
            $validated['key'],
            $validated['value'],
            $validated['group'],
            $validated['type']
        );
        
        return response()->json(['message' => 'Setting updated successfully']);
    }
}
*/

// ============================================================================
// USAGE IN VIEWS/BLADE TEMPLATES
// ============================================================================

/*
// In a Blade template:
<h1>{{ Setting::get('site_name', 'Default Site Name') }}</h1>
<p>Contact us: {{ Setting::get('company_email') }}</p>

@if(Setting::get('maintenance_mode'))
    <div class="alert alert-warning">
        Site is under maintenance
    </div>
@endif
*/
