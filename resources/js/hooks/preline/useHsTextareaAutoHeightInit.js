import HSTextareaAutoHeight from 'preline/src/plugins/textarea-auto-height';
import { useEffect } from 'react';

export default function useHsTextareaAutoHeightInit() {
    useEffect(() => {
        let ignore = false;

        setTimeout(() => {
            if (!ignore) {
                HSTextareaAutoHeight.autoInit();
            }
        }, 50);

        return () => {
            ignore = true;
        };
    }, []);
}
