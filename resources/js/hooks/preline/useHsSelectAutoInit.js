import HSSelect from 'preline/src/plugins/select/index.js';
import { useEffect } from 'react';

export default function useHsSelectAutoInit() {
    useEffect(() => {
        let ignore = false;

        setTimeout(() => {
            if (!ignore) {
                HSSelect.autoInit();
            }
        }, 50);

        return () => {
            ignore = true;
        };
    }, []);
    if (typeof window !== 'undefined' && window.Preline) {
        window.Preline.init();
    }
}
