import { router } from '@inertiajs/react';

export function useNavigate() {
    return (to, options = {}) => {
        const {
            replace = false,
            preserveState = false,
            preserveScroll = false,
            data = {},
        } = options;

        router.visit(to, {
            replace,
            preserveState,
            preserveScroll,
            data,
        });
    };
}

export function useLocation() {
    const { origin, href } = window.location;
    const url = new URL(href, origin);
    const { pathname, search } = url;

    return {
        pathname,
        search,
    };
}

export function useSearchParams() {
    return [new URLSearchParams(window.location.search)];
}
