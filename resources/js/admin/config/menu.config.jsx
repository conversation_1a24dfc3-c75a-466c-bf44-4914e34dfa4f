import {
    BookMarked,
    Calendar<PERSON>heck,
    CircleQuestionMark,
    ClipboardCheck,
    DollarSign,
    Layers,
    LayoutGrid,
    MapPin,
    MessageCircle,
    MessageSquareQuote,
    Newspaper,
    Package,
    Settings,
    SquareActivity,
    UserCircle,
} from 'lucide-react';

export const MENU_SIDEBAR = [
    {
        title: 'Dashboards',
        icon: LayoutGrid,
        path: route('admin.dashboard'),
    },
    {
        title: 'Bookings',
        icon: CalendarCheck,
        path: route('admin.bookings.index'),
    },
    {
        title: 'Customized Trips',
        icon: ClipboardCheck,
        path: route('admin.customized-trips.index'),
    },
    {
        title: 'Manage Packages',
        icon: Package,
        path: route('admin.packages.index'),
    },
    {
        title: 'Manage Activities',
        icon: SquareActivity,
        path: route('admin.activities.index'),
    },
    {
        title: 'Manage Destinations',
        icon: MapPin,
        path: route('admin.destinations.index'),
    },
    {
        title: 'Manage Messages',
        icon: MessageCircle,
        path: route('admin.messages.index'),
    },
    {
        title: 'Manage Pages',
        icon: Layers,
        path: route('admin.pages.index'),
    },
    {
        title: 'Manage Blogs',
        icon: Newspaper,
        path: route('admin.blogs.index'),
    },
    {
        title: 'Manage Travel Guides',
        icon: BookMarked,
        path: route('admin.travel-guides.index'),
    },
    {
        title: 'Manage FAQs',
        icon: CircleQuestionMark,
        children: [
            { title: 'All FAQs', path: route('admin.faqs.index') },
            { title: 'FAQ Groups', path: route('admin.faq-groups.index') },
        ],
    },
    {
        title: 'Manage Testimonials',
        icon: MessageSquareQuote,
        path: route('admin.testimonials.index'),
    },
    {
        title: 'Travel Experience Videos',
        icon: Newspaper,
        path: route('admin.travel-experience-videos.index'),
    },
    {
        title: 'Manage Users',
        icon: UserCircle,
        path: route('admin.users.index'),
    },
    {
        title: 'Commissions',
        icon: DollarSign,
        path: route('admin.commissions.index'),
    },
    {
        title: 'Settings',
        icon: Settings,
        children: [
            { title: 'General Settings', path: route('admin.settings.index') },
            { title: 'Home Page Settings', path: route('admin.settings.home-page') },
        ],
    },
];

export const MENU_MEGA = [{ title: 'Home', path: '/' }];

export const MENU_MEGA_MOBILE = [{ title: 'Home', path: '/' }];
