import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>r<PERSON><PERSON>nt,
    MenubarItem,
    MenubarMenu,
    MenubarSub,
    MenubarSubContent,
    MenubarSubTrigger,
    MenubarTrigger,
} from '@admin/components/ui/menubar';
import { useMenu } from '@admin/hooks/use-menu';
import { cn } from '@admin/lib/utils';
import { Link, useLocation } from '@inertiajs/react';
import { ChevronDown } from 'lucide-react';

const NavbarMenu = ({ items }) => {
    const { pathname } = useLocation();
    const { isActive, hasActiveChild } = useMenu(pathname);

    const buildMenu = (items) => {
        return items.map((item, index) => {
            if (item.children) {
                return (
                    <MenubarMenu key={index}>
                        <MenubarTrigger
                            className={cn(
                                'text-secondary-foreground flex items-center gap-1.5 px-3 py-3.5 text-sm',
                                'bg-transparent! rounded-none border-b-2 border-transparent',
                                'hover:text-primary hover:bg-transparent',
                                'focus:text-primary focus:bg-transparent',
                                'data-[state=open]:text-primary data-[state=open]:bg-transparent',
                                'data-[here=true]:text-primary data-[here=true]:border-primary',
                            )}
                            data-active={isActive(item.path) || undefined}
                            data-here={
                                hasActiveChild(item.children) || undefined
                            }
                        >
                            {item.title}
                            <ChevronDown className="ms-auto size-3.5" />
                        </MenubarTrigger>
                        <MenubarContent className="min-w-[175px]">
                            {buildSubMenu(item.children)}
                        </MenubarContent>
                    </MenubarMenu>
                );
            } else {
                return (
                    <MenubarMenu key={index}>
                        <MenubarTrigger
                            asChild
                            className={cn(
                                'text-secondary-foreground flex items-center px-2 px-3 py-3.5 text-sm',
                                'bg-transparent! rounded-none border-b-2 border-transparent',
                                'hover:text-primary hover:bg-transparent',
                                'focus:text-primary focus:bg-transparent',
                                'data-[active=true]:text-primary data-[active=true]:border-primary',
                            )}
                        >
                            <Link
                                to={item.path || ''}
                                data-active={isActive(item.path) || undefined}
                                data-here={
                                    hasActiveChild(item.children) || undefined
                                }
                            >
                                {item.title}
                            </Link>
                        </MenubarTrigger>
                    </MenubarMenu>
                );
            }
        });
    };

    const buildSubMenu = (items) => {
        return items.map((item, index) => {
            if (item.children) {
                return (
                    <MenubarSub key={index}>
                        <MenubarSubTrigger
                            data-active={isActive(item.path) || undefined}
                            data-here={
                                hasActiveChild(item.children) || undefined
                            }
                        >
                            <span>{item.title}</span>
                        </MenubarSubTrigger>
                        <MenubarSubContent className="min-w-[175px]">
                            {buildSubMenu(item.children)}
                        </MenubarSubContent>
                    </MenubarSub>
                );
            } else {
                return (
                    <MenubarItem
                        key={index}
                        asChild
                        data-active={isActive(item.path) || undefined}
                        data-here={hasActiveChild(item.children) || undefined}
                    >
                        <Link href={item.path || ''}>{item.title}</Link>
                    </MenubarItem>
                );
            }
        });
    };

    return (
        <div className="grid">
            <div className="kt-scrollable-x-auto">
                <Menubar className="flex h-auto items-stretch gap-3 border-none bg-transparent p-0">
                    {buildMenu(items)}
                </Menubar>
            </div>
        </div>
    );
};

export { NavbarMenu };
