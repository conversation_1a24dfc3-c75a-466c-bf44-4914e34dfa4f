import { cn } from '@admin/lib/utils';

const ScrollspyMenu = ({ items }) => {
    const buildAnchor = (item, index, indent = false) => {
        return (
            <div
                key={index}
                data-scrollspy-anchor={item.target}
                className={cn(
                    'text-accent-foreground hover:text-primary data-[active=true]:bg-accent data-[active=true]:text-primary flex cursor-pointer items-center rounded-lg border border-transparent py-1.5 pe-2.5 ps-2.5 data-[active=true]:font-medium',
                    indent ? 'gap-3.5' : 'gap-1.5',
                )}
            >
                <span className="[[data-active=true]>&]:before:bg-primary relative start-px flex w-1.5 before:absolute before:top-0 before:size-1.5 before:-translate-x-2/4 before:-translate-y-2/4 before:rounded-full rtl:-start-[5px]"></span>
                {item.title}
            </div>
        );
    };

    const buildSubAnchors = (items) => {
        return items.map((item, index) => {
            return buildAnchor(item, index, true);
        });
    };

    const renderChildren = (items) => {
        return items.map((item, index) => {
            if (item.children) {
                return (
                    <div key={index} className="flex flex-col">
                        <div className="text-mono py-2.5 pe-2.5 ps-6 text-sm font-semibold">
                            {item.title}
                        </div>
                        <div className="flex flex-col">
                            {buildSubAnchors(item.children)}
                        </div>
                    </div>
                );
            } else {
                return buildAnchor(item, index);
            }
        });
    };

    return (
        <div className="before:border-border relative flex grow flex-col text-sm before:absolute before:bottom-0 before:start-[11px] before:top-0 before:border-s">
            {renderChildren(items)}
        </div>
    );
};

export { ScrollspyMenu };
