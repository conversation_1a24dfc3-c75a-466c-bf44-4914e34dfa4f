import { Button } from '@admin/components/ui/button';
import { ScrollArea } from '@admin/components/ui/scroll-area';
import {
    Sheet,
    She<PERSON>B<PERSON>,
    She<PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>ooter,
    She<PERSON><PERSON>eader,
    She<PERSON><PERSON><PERSON>le,
    Sheet<PERSON>rigger,
} from '@admin/components/ui/sheet';

export function NotificationsSheet({ trigger }) {
    return (
        <Sheet>
            <SheetTrigger asChild>{trigger}</SheetTrigger>
            <SheetContent className="[&_[data-slot=sheet-close]]:top-4.5 inset-5 start-auto h-auto gap-0 rounded-lg p-0 sm:w-[500px] sm:max-w-none [&_[data-slot=sheet-close]]:end-5">
                <SheetHeader className="mb-0">
                    <SheetTitle className="p-3">Notifications</SheetTitle>
                </SheetHeader>
                <SheetBody className="grow p-0">
                    <ScrollArea className="h-[calc(100vh-10.5rem)]">
                        <div className="flex grow gap-2.5 px-5">
                            Notification content goes here
                        </div>
                    </ScrollArea>
                </SheetBody>
                <SheetFooter className="border-border grid grid-cols-2 gap-2.5 border-t p-5">
                    <Button variant="outline">Archive all</Button>
                    <Button variant="outline">Mark all as read</Button>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    );
}
