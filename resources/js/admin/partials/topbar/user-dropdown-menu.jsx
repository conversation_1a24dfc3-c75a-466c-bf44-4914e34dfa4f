import { useUser } from '@/hooks/props';
import { Button } from '@admin/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@admin/components/ui/dropdown-menu';
import { toAbsoluteUrl } from '@admin/lib/helpers';
import { Link, router } from '@inertiajs/react';
import { Settings, UserCircle } from 'lucide-react';

export function UserDropdownMenu({ trigger }) {
    const { user } = useUser();
    const logout = () => {
        router.post('/logout');
    };

    // Use display data from currentUser
    const displayName =
        user?.fullname ||
        (user?.first_name && user?.last_name
            ? `${user.first_name} ${user.last_name}`
            : user?.username || 'User');

    const displayEmail = user?.email || '';
    const displayAvatar =
        user?.pic || toAbsoluteUrl('/assets/images/profile.png');

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
            <DropdownMenuContent className="w-64" side="bottom" align="end">
                {/* Header */}
                <div className="flex items-center justify-between p-3">
                    <div className="flex items-center gap-2">
                        <img
                            className="size-9 rounded-full border-2 border-green-500"
                            src={displayAvatar}
                            alt="User avatar"
                        />

                        <div className="flex flex-col">
                            <Link
                                href="/profile"
                                className="text-mono hover:text-primary text-sm font-semibold"
                            >
                                {displayName}
                            </Link>
                            <a
                                href={`mailto:${displayEmail}`}
                                className="text-muted-foreground hover:text-primary text-xs"
                            >
                                {displayEmail}
                            </a>
                        </div>
                    </div>
                </div>

                <DropdownMenuSeparator />

                <DropdownMenuItem asChild>
                    <Link
                        href="/admin/profile"
                        className="flex items-center gap-2"
                    >
                        <UserCircle />
                        My Profile
                    </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                    <Link
                        href="/admin/settings"
                        className="flex items-center gap-2"
                    >
                        <Settings />
                        Settings
                    </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                {/* Footer */}
                <div className="mt-1 p-2">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={logout}
                    >
                        Logout
                    </Button>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
