import { Button } from '@admin/components/ui/button';
import { Card, CardContent } from '@admin/components/ui/card';
import { cn } from '@admin/lib/utils';
import { Link } from '@inertiajs/react';

export function CreateTeam({ className, image, title, subTitle, engage }) {
    return (
        <Card className={cn('', className && className)}>
            <CardContent className="flex flex-col place-content-center gap-5">
                <div className="flex justify-center">{image}</div>
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-3 text-center">
                        <h2 className="text-mono text-xl font-semibold">
                            {title}
                        </h2>
                        <p className="text-secondary-foreground text-sm font-medium">
                            {subTitle}
                        </p>
                    </div>
                    <div className="flex justify-center">
                        <Button variant={engage.btnColor}>
                            <Link href={engage.path}>{engage.label}</Link>
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
