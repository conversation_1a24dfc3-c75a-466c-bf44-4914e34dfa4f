import { But<PERSON> } from '@admin/components/ui/button';
import { Card, CardContent } from '@admin/components/ui/card';
import { Link } from '@inertiajs/react';

export function Starter({ image, title, subTitle, engage }) {
    return (
        <Card>
            <CardContent className="py-7.5 flex flex-col items-center gap-2.5">
                <div className="p-7.5 flex justify-center py-9">{image}</div>
                <div className="lg:gap-7.5 flex flex-col gap-5">
                    <div className="flex flex-col gap-3 text-center">
                        <h2 className="text-mono text-xl font-semibold">
                            {title}
                        </h2>
                        <p className="text-foreground text-sm">{subTitle}</p>
                    </div>
                    <div className="mb-5 flex justify-center">
                        <Button size="md" className={engage.btnColor}>
                            <Link href={engage.path}>{engage.label}</Link>
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
