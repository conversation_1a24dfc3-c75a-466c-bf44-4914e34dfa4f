import {
    Accordion,
    AccordionContent,
    Accordion<PERSON><PERSON>,
    AccordionTrigger,
} from '@admin/components/ui/accordion';
import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card';

export function Faq() {
    const items = [
        {
            title: 'How is pricing determined for each plan ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision. Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision. Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
        {
            title: 'What payment methods are accepted for subscriptions ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
        {
            title: 'Are there any hidden fees in the pricing ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
        {
            title: 'Is there a discount for annual subscriptions ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
        {
            title: 'Do you offer refunds on subscription cancellations ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
        {
            title: 'Can I add extra features to my current plan ?',
            text: "Metronic embraces flexible licensing options that empower you to choose the perfect fit for your project's needs and budget. Understanding the factors influencing each plan's pricing helps you make an informed decision",
        },
    ];

    const generateItems = () => {
        return (
            <Accordion type="single" collapsible>
                {items.map((item, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                        <AccordionTrigger>{item.title}</AccordionTrigger>
                        <AccordionContent>{item.text}</AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        );
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>FAQ</CardTitle>
            </CardHeader>
            <CardContent className="py-3">{generateItems()}</CardContent>
        </Card>
    );
}
