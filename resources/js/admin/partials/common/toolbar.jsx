import { MENU_SIDEBAR } from '@admin/config/menu.config';
import { useMenu } from '@admin/hooks/use-menu';
import { useLocation } from 'react-router';

const Toolbar = ({ children }) => {
    return (
        <div className="pb-7.5 flex flex-wrap items-center justify-between gap-5 lg:items-end">
            {children}
        </div>
    );
};

const ToolbarActions = ({ children }) => {
    return <div className="flex items-center gap-2.5">{children}</div>;
};

const ToolbarPageTitle = ({ text }) => {
    const { pathname } = useLocation();
    const { getCurrentItem } = useMenu(pathname);
    const item = getCurrentItem(MENU_SIDEBAR);

    return (
        <h1 className="text-mono text-xl font-medium leading-none">
            {text ?? item?.title}
        </h1>
    );
};

const ToolbarDescription = ({ children }) => {
    return (
        <div className="text-secondary-foreground flex items-center gap-2 text-sm font-normal">
            {children}
        </div>
    );
};

const ToolbarHeading = ({ children }) => {
    return <div className="flex flex-col justify-center gap-2">{children}</div>;
};

export {
    Toolbar,
    ToolbarActions,
    ToolbarDescription,
    ToolbarHeading,
    ToolbarPageTitle,
};
