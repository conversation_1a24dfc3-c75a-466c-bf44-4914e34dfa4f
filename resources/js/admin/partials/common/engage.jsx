import { But<PERSON> } from '@admin/components/ui/button';
import { Card, CardContent, CardFooter } from '@admin/components/ui/card';
import { Link } from '@inertiajs/react';

export function Engage({ title, description, image, more }) {
    return (
        <Card>
            <CardContent className="py-7.5 lg:pr-12.5 px-10">
                <div className="flex flex-wrap items-center gap-6 md:flex-nowrap md:gap-10">
                    <div className="flex flex-col items-start gap-3">
                        <h2 className="text-mono text-xl font-medium">
                            {title}
                        </h2>
                        <p className="text-foreground leading-5.5 mb-2.5 text-sm">
                            {description}
                        </p>
                    </div>
                    {image}
                </div>
            </CardContent>
            <CardFooter className="justify-center">
                <Button mode="link" underlined="dashed" asChild>
                    <Link href={more.url}>{more.title}</Link>
                </Button>
            </CardFooter>
        </Card>
    );
}
