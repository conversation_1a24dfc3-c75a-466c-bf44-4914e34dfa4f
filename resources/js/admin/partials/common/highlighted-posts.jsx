import { But<PERSON> } from '@admin/components/ui/button';
import { Card, CardContent } from '@admin/components/ui/card';
import { Link } from '@inertiajs/react';
import { Fragment } from 'react';
import { HexagonBadge } from './hexagon-badge';

export function HighlightedPosts({ posts }) {
    const renderItem = (post, index) => {
        return (
            <Fragment key={index}>
                <div className="flex flex-col items-start gap-2.5">
                    <div className="mb-2.5">
                        <HexagonBadge
                            stroke="stroke-orange-200 dark:stroke-orange-950"
                            fill="fill-orange-50 dark:fill-orange-950/30"
                            size="size-[50px]"
                            badge={
                                <post.icon
                                    size={28}
                                    className="ps-px text-xl text-orange-400"
                                />
                            }
                        />
                    </div>
                    <Link
                        to={`${post.path}`}
                        className="text-mono hover:text-primary text-base font-semibold"
                    >
                        {post.title}
                    </Link>
                    <p className="text-secondary-foreground text-sm">
                        {post.summary}
                    </p>
                    <Button mode="link" underlined="dashed" asChild>
                        <Link href={`${post.path}`}>Learn more</Link>
                    </Button>
                </div>
                <span className="not-last:block not-last:border-b border-b-border hidden"></span>
            </Fragment>
        );
    };

    return (
        <Card>
            <CardContent className="lg:gap-7.5 flex flex-col gap-5 py-10">
                {posts.map((post, index) => {
                    return renderItem(post, index);
                })}
            </CardContent>
        </Card>
    );
}
