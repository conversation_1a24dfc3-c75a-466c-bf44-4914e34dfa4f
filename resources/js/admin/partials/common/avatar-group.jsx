import {
    Avatar,
    AvatarFallback,
    AvatarImage,
} from '@admin/components/ui/avatar';
import { toAbsoluteUrl } from '@admin/lib/helpers';
import { cn } from '@admin/lib/utils';

function AvatarGroup({ size, group, more, className }) {
    const avatarSize = size ? size : 'size-7';

    const renderItem = (each, index) => {
        return (
            <Avatar key={index} className={cn(avatarSize)}>
                {each.filename || each.path ? (
                    <AvatarImage
                        src={toAbsoluteUrl(
                            each.path || `/media/avatars/${each.filename}`,
                        )}
                        alt="image"
                        className="border-1 border-background hover:z-10"
                    />
                ) : null}
                {each.fallback ? (
                    <AvatarFallback
                        className={cn(
                            'border-1 border-background relative text-[11px] hover:z-10',
                            size,
                            each.variant,
                        )}
                    >
                        {each.fallback}
                    </AvatarFallback>
                ) : null}
            </Avatar>
        );
    };

    return (
        <div className={cn('flex -space-x-2', className)}>
            {group.map((each, index) => renderItem(each, index))}
            {more && (
                <span
                    className={cn(
                        'border-1 border-background relative flex shrink-0 cursor-default items-center justify-center rounded-full text-[11px] font-semibold leading-none hover:z-10',
                        avatarSize,
                        more.variant,
                    )}
                >
                    +{more.number || more.label}
                </span>
            )}
        </div>
    );
}

export { AvatarGroup };
