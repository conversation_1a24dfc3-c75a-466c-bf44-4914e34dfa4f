import { AuthContext } from '@admin/auth/context/auth-context';
import { useLocation } from '@hooks/navigation.js';
import { router, usePage } from '@inertiajs/react';
import { useState } from 'react';

// Define the Auth Provider
export function AuthProvider({ children }) {
    const [loading, setLoading] = useState(true);
    const [currentUser, setCurrentUser] = useState();
    const [isAdmin, setIsAdmin] = useState(false);
    const [auth, setAuth] = useState(null);
    const page = usePage();
    const { pathname } = useLocation();

    const login = async (email, password, remember) => {
        const data = { email, password, remember };
        router.post(route('admin.login'), data, {
            onFinish(response) {
                setLoading(false);
            },
            onError(error) {
                console.error('Login error:', error);
                setLoading(false);
                throw error;
            },
            onStart() {
                setLoading(true);
            },
        });
    };

    const requestPasswordReset = async (email) => {
        // await SupabaseAdapter.requestPasswordReset(email);
    };

    const resetPassword = async (password, password_confirmation) => {
        // await SupabaseAdapter.resetPassword(password, password_confirmation);
    };

    const updateProfile = async (userData) => {
        // return await SupabaseAdapter.updateUserProfile(userData);
    };

    const logout = () => {
        console.log('Logging out...');
        router.post(route('admin.logout'), {
            onSuccess: () => {
                setCurrentUser(null);
                setAuth(null);
                setIsAdmin(false);
            },
        });
    };

    return (
        <AuthContext.Provider
            value={{
                loading,
                setLoading,
                auth,
                user: currentUser,
                setUser: setCurrentUser,
                login,
                requestPasswordReset,
                resetPassword,
                updateProfile,
                logout,
                isAdmin,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
}
