'use client';

import { TooltipProvider } from '@admin/components/ui/tooltip';
import { ThemeProvider as NextThemesProvider } from 'next-themes';

export function ThemeProvider({ children }) {
    return (
        <NextThemesProvider
            attribute="class"
            defaultTheme="system"
            storageKey="vite-theme"
            enableSystem
            disableTransitionOnChange
            enableColorScheme
        >
            <TooltipProvider delayDuration={0}>{children}</TooltipProvider>
        </NextThemesProvider>
    );
}
