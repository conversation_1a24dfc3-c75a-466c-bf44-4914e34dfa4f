import { MENU_SIDEBAR } from '@admin/config/menu.config';
import { useMenu } from '@admin/hooks/use-menu';
import { cn } from '@admin/lib/utils';
import { useLocation } from '@hooks/navigation';
import { ChevronRight } from 'lucide-react';
import { Fragment } from 'react';

export function Breadcrumb() {
    const { pathname } = useLocation();
    const { getBreadcrumb, isActive } = useMenu(pathname);
    const items = getBreadcrumb(MENU_SIDEBAR);

    if (items.length === 0) {
        return null;
    }

    return (
        <div className="gap-1.25 mb-2.5 flex items-center text-xs font-medium lg:mb-0 lg:text-sm">
            {items.map((item, index) => {
                const last = index === items.length - 1;
                const active = item.path ? isActive(item.path) : false;

                return (
                    <Fragment key={`root-${index}`}>
                        <span
                            className={cn(
                                active
                                    ? 'text-mono'
                                    : 'text-secondary-foreground',
                            )}
                            key={`item-${index}`}
                        >
                            {item.title}
                        </span>
                        {!last && (
                            <ChevronRight
                                className="text-muted-foreground size-3.5"
                                key={`separator-${index}`}
                            />
                        )}
                    </Fragment>
                );
            })}
        </div>
    );
}
