import {
    NavigationMenu,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
} from '@admin/components/ui/navigation-menu';
import { MENU_MEGA } from '@admin/config/menu.config';
import { useMenu } from '@admin/hooks/use-menu';
import { cn } from '@admin/lib/utils';
import { useLocation } from '@hooks/navigation';
import { Link } from '@inertiajs/react';

export function MegaMenu() {
    const { pathname } = useLocation();
    const { isActive, hasActiveChild } = useMenu(pathname);
    const homeItem = MENU_MEGA[0];
    const publicProfilesItem = MENU_MEGA[1];
    const myAccountItem = MENU_MEGA[2];
    const networkItem = MENU_MEGA[3];
    const authItem = MENU_MEGA[4];
    const storeItem = MENU_MEGA[5];

    const linkClass = `
    text-sm text-secondary-foreground font-medium
    hover:text-primary hover:bg-transparent
    focus:text-primary focus:bg-transparent
    data-[active=true]:text-primary data-[active=true]:bg-transparent
    data-[state=open]:text-primary data-[state=open]:bg-transparent
  `;

    return (
        <NavigationMenu>
            <NavigationMenuList className="gap-0">
                {/* Home Item */}
                <NavigationMenuItem>
                    <NavigationMenuLink asChild>
                        <Link
                            href={homeItem.path || '/'}
                            className={cn(linkClass)}
                            data-active={isActive(homeItem.path) || undefined}
                            target="_blank"
                        >
                            {homeItem.title}
                        </Link>
                    </NavigationMenuLink>
                </NavigationMenuItem>
            </NavigationMenuList>
        </NavigationMenu>
    );
}
