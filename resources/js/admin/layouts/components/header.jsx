import { Container } from '@admin/components/common/container';
import { But<PERSON> } from '@admin/components/ui/button';
import {
    She<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    She<PERSON><PERSON>eader,
    SheetTrigger,
} from '@admin/components/ui/sheet';
import { useIsMobile } from '@admin/hooks/use-mobile';
import { useScrollPosition } from '@admin/hooks/use-scroll-position';
import { cn } from '@admin/lib/utils';
import { NotificationsSheet } from '@admin/partials/topbar/notifications-sheet';
import { UserDropdownMenu } from '@admin/partials/topbar/user-dropdown-menu';
import { useLocation } from '@hooks/navigation';
import { Link } from '@inertiajs/react';
import { Bell, Menu, SquareChevronRight } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Breadcrumb } from './breadcrumb';
import { MegaMenu } from './mega-menu';
import { MegaMenuMobile } from './mega-menu-mobile';
import { SidebarMenu } from './sidebar-menu';

export function Header() {
    const [isSidebarSheetOpen, setIsSidebarSheetOpen] = useState(false);
    const [isMegaMenuSheetOpen, setIsMegaMenuSheetOpen] = useState(false);

    const { pathname } = useLocation();
    const mobileMode = useIsMobile();

    const scrollPosition = useScrollPosition();
    const headerSticky = scrollPosition > 0;

    // Close sheet when route changes
    useEffect(() => {
        setIsSidebarSheetOpen(false);
        setIsMegaMenuSheetOpen(false);
    }, [pathname]);

    return (
        <header
            className={cn(
                'header bg-background fixed end-0 start-0 top-0 z-10 flex shrink-0 items-stretch border-b border-transparent pe-[var(--removed-body-scroll-bar-size,0px)]',
                headerSticky && 'border-border border-b',
            )}
        >
            <Container className="flex items-stretch justify-between lg:gap-4">
                {/* HeaderLogo */}
                <div className="flex items-center gap-1 gap-2.5 lg:hidden">
                    <Link href="/" className="shrink-0">
                        <img
                            src={'/logo.png'}
                            className="h-[25px] w-full"
                            alt="Logo"
                        />
                    </Link>
                    <div className="flex items-center">
                        {mobileMode && (
                            <Sheet
                                open={isSidebarSheetOpen}
                                onOpenChange={setIsSidebarSheetOpen}
                            >
                                <SheetTrigger asChild>
                                    <Button variant="ghost" mode="icon">
                                        <Menu className="text-muted-foreground/70" />
                                    </Button>
                                </SheetTrigger>
                                <SheetContent
                                    className="w-[275px] gap-0 p-0"
                                    side="left"
                                    close={false}
                                >
                                    <SheetHeader className="space-y-0 p-0" />
                                    <SheetBody className="overflow-y-auto p-0">
                                        <SidebarMenu />
                                    </SheetBody>
                                </SheetContent>
                            </Sheet>
                        )}
                        {mobileMode && (
                            <Sheet
                                open={isMegaMenuSheetOpen}
                                onOpenChange={setIsMegaMenuSheetOpen}
                            >
                                <SheetTrigger asChild>
                                    <Button variant="ghost" mode="icon">
                                        <SquareChevronRight className="text-muted-foreground/70" />
                                    </Button>
                                </SheetTrigger>
                                <SheetContent
                                    className="w-[275px] gap-0 p-0"
                                    side="left"
                                    close={false}
                                >
                                    <SheetHeader className="space-y-0 p-0" />
                                    <SheetBody className="overflow-y-auto p-0">
                                        <MegaMenuMobile />
                                    </SheetBody>
                                </SheetContent>
                            </Sheet>
                        )}
                    </div>
                </div>
                {/* Main Content (MegaMenu or Breadcrumbs) */}

                <Breadcrumb />

                {!mobileMode && <MegaMenu />}

                {/* HeaderTopbar */}
                <div className="flex items-center gap-3">
                    <>
                        <NotificationsSheet
                            trigger={
                                <Button
                                    variant="ghost"
                                    mode="icon"
                                    shape="circle"
                                    className="hover:bg-primary/10 hover:[&_svg]:text-primary size-9"
                                >
                                    <Bell className="size-4.5!" />
                                </Button>
                            }
                        />

                        <UserDropdownMenu
                            trigger={
                                <img
                                    className="size-9 shrink-0 cursor-pointer rounded-full border-2 border-green-500"
                                    src={'/assets/images/profile.png'}
                                    alt="User Avatar"
                                />
                            }
                        />
                    </>
                </div>
            </Container>
        </header>
    );
}
