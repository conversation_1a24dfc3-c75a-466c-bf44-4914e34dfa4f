import { cn } from '@admin/lib/utils';
import { useSettings } from '@admin/providers/settings-provider';
import { useLocation } from '@hooks/navigation';
import { SidebarHeader } from './sidebar-header';
import { SidebarMenu } from './sidebar-menu';

export function Sidebar() {
    const { settings } = useSettings();
    const { pathname } = useLocation();

    return (
        <div
            className={cn(
                'sidebar bg-background lg:border-border shrink-0 flex-col items-stretch lg:fixed lg:bottom-0 lg:top-0 lg:z-20 lg:flex lg:border-e',
                (settings.layouts.demo1.sidebarTheme === 'dark' ||
                    pathname.includes('dark-sidebar')) &&
                    'dark',
            )}
        >
            <SidebarHeader />
            <div className="overflow-hidden">
                <div className="w-(--sidebar-default-width)">
                    <SidebarMenu />
                </div>
            </div>
        </div>
    );
}
