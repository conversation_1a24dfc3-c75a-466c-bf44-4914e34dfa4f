import { MENU_SIDEBAR } from '@admin/config/menu.config';
import { useMenu } from '@admin/hooks/use-menu';
import { cn } from '@admin/lib/utils';
import { useLocation } from '@hooks/navigation';
import { Link } from '@inertiajs/react';
import { ChevronRight } from 'lucide-react';
import { Fragment } from 'react';

function Toolbar({ children }) {
    return (
        <div className="pb-7.5 flex flex-wrap items-center justify-between gap-5">
            {children}
        </div>
    );
}

function ToolbarActions({ children }) {
    return <div className="flex items-center gap-2.5">{children}</div>;
}

function ToolbarBreadcrumbs() {
    const { pathname } = useLocation();
    const { getBreadcrumb, isActive } = useMenu(pathname);
    const items = getBreadcrumb(MENU_SIDEBAR);

    if (items.length === 0) {
        return null;
    }

    return (
        <div className="[.header_&]:below-lg:hidden gap-1.25 mb-2.5 flex items-center text-xs font-medium lg:mb-0 lg:text-sm">
            <div className="breadcrumb flex items-center gap-1">
                {items.map((item, index) => {
                    const isLast = index === items.length - 1;
                    const active = item.path ? isActive(item.path) : false;

                    return (
                        <Fragment key={index}>
                            {item.path ? (
                                <Link
                                    to={item.path}
                                    className={cn(
                                        'flex items-center gap-1',
                                        active
                                            ? 'text-mono'
                                            : 'text-muted-foreground hover:text-primary',
                                    )}
                                >
                                    {item.title}
                                </Link>
                            ) : (
                                <span
                                    className={cn(
                                        isLast
                                            ? 'text-mono'
                                            : 'text-muted-foreground',
                                    )}
                                >
                                    {item.title}
                                </span>
                            )}
                            {!isLast && (
                                <ChevronRight className="muted-foreground size-3.5" />
                            )}
                        </Fragment>
                    );
                })}
            </div>
        </div>
    );
}

function ToolbarHeading({ title = '', description }) {
    const { pathname } = useLocation();
    const { getCurrentItem } = useMenu(pathname);
    const item = getCurrentItem(MENU_SIDEBAR);

    return (
        <div className="flex flex-col justify-center gap-2">
            <h1 className="text-mono text-xl font-medium leading-none">
                {title || item?.title || 'Untitled'}
            </h1>
            {description && (
                <div className="text-muted-foreground flex items-center gap-2 text-sm font-normal">
                    {description}
                </div>
            )}
        </div>
    );
}

export { Toolbar, ToolbarActions, ToolbarBreadcrumbs, ToolbarHeading };
