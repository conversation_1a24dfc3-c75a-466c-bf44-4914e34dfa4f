import { Container } from '@admin/components/common/container';
import { useIsMobile } from '@admin/hooks/use-mobile';

import { Breadcrumb } from './breadcrumb';

export function Content({ children }) {
    const mobile = useIsMobile();

    return (
        <div className="content grow pt-5" role="content">
            {mobile && (
                <Container>
                    <Breadcrumb />
                </Container>
            )}
            {children}
        </div>
    );
}
