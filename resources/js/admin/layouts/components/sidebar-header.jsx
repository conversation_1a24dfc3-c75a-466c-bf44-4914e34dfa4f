import { Button } from '@admin/components/ui/button';
import { cn } from '@admin/lib/utils';
import { useSettings } from '@admin/providers/settings-provider';
import { Link } from '@inertiajs/react';
import { ChevronFirst } from 'lucide-react';

export function SidebarHeader() {
    const { settings, storeOption } = useSettings();

    const handleToggleClick = () => {
        storeOption(
            'layouts.demo1.sidebarCollapse',
            !settings.layouts.demo1.sidebarCollapse,
        );
    };

    return (
        <div className="sidebar-header relative hidden shrink-0 items-center justify-center px-3 lg:flex lg:px-6">
            <Link href="/">
                <div className="-mx-2 dark:hidden">
                    <img
                        src={'/logo.png'}
                        className="default-logo h-[36px] max-w-none"
                        alt="Default Logo"
                    />

                    <img
                        src={'/logo.png'}
                        className="small-logo h-[22px] max-w-full object-contain"
                        alt="Mini Logo"
                    />
                </div>
                <div className="hidden dark:block">
                    <img
                        src={'/logo.png'}
                        className="default-logo h-[22px] max-w-none"
                        alt="Logo"
                    />

                    <img
                        src={'/logo.png'}
                        className="small-logo h-[22px] max-w-full object-contain"
                        alt="Mini Logo"
                    />
                </div>
            </Link>
            <Button
                onClick={handleToggleClick}
                size="sm"
                mode="icon"
                variant="outline"
                className={cn(
                    'absolute start-full top-2/4 size-7 -translate-x-2/4 -translate-y-2/4 rtl:translate-x-2/4',
                    settings.layouts.demo1.sidebarCollapse
                        ? 'ltr:rotate-180'
                        : 'rtl:rotate-180',
                )}
            >
                <ChevronFirst className="size-4!" />
            </Button>
        </div>
    );
}
