import '@css/admin/main.css';

import { Container } from '../components/common/container';
import { Toaster } from '../components/ui/sonner';
import { SettingsProvider } from '../providers/settings-provider';
import { Toolbar, ToolbarActions, ToolbarHeading } from './components/toolbar';
import LayoutTemplate from './LayoutTemplate';

export default function AdminLayout({
    children,
    actions,
    title,
    description,
    toolbar = true,
}) {
    return (
        <>
            <SettingsProvider>
                <LayoutTemplate>
                    <Container>
                        {toolbar && (
                            <Toolbar>
                                <ToolbarHeading
                                    title={title}
                                    description={description}
                                />
                                <ToolbarActions>{actions}</ToolbarActions>
                            </Toolbar>
                        )}
                    </Container>
                    <Container>{children}</Container>
                </LayoutTemplate>
            </SettingsProvider>
            <Toaster />
        </>
    );
}
