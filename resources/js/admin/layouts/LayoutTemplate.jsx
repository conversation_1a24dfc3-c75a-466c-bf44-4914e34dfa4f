import { MENU_SIDEBAR } from '@admin/config/menu.config';
import { useMenu } from '@admin/hooks/use-menu';
import { useIsMobile } from '@admin/hooks/use-mobile';
import { useSettings } from '@admin/providers/settings-provider';
import { useLocation } from '@hooks/navigation';
import { useEffect } from 'react';
import { Toaster } from '../components/ui/sonner';
import { Footer } from './components/footer';
import { Header } from './components/header';
import { Sidebar } from './components/sidebar';

export default function Layout({ children }) {
    const isMobile = useIsMobile();
    const { pathname } = useLocation();
    const { getCurrentItem } = useMenu(pathname);
    const item = getCurrentItem(MENU_SIDEBAR);
    const { settings, setOption } = useSettings();

    useEffect(() => {
        const bodyClass = document.body.classList;

        if (settings.layouts.demo1.sidebarCollapse) {
            bodyClass.add('sidebar-collapse');
        } else {
            bodyClass.remove('sidebar-collapse');
        }
    }, [settings]); // Runs only on settings update

    useEffect(() => {
        // Set current layout
        setOption('layout', 'demo1');
    }, [setOption]);

    useEffect(() => {
        const bodyClass = document.body.classList;

        // Add a class to the body element
        bodyClass.add('demo1');
        bodyClass.add('sidebar-fixed');
        bodyClass.add('header-fixed');

        const timer = setTimeout(() => {
            bodyClass.add('layout-initialized');
        }, 1000); // 1000 milliseconds

        // Remove the class when the component is unmounted
        return () => {
            bodyClass.remove('demo1');
            bodyClass.remove('sidebar-fixed');
            bodyClass.remove('sidebar-collapse');
            bodyClass.remove('header-fixed');
            bodyClass.remove('layout-initialized');
            clearTimeout(timer);
        };
    }, []); // Runs only once on mount

    return (
        <>
            {!isMobile && <Sidebar />}

            <div className="wrapper flex grow flex-col">
                <Header />

                <main className="grow pt-5" role="content">
                    {children}
                </main>

                <Footer />
            </div>
            <Toaster />
        </>
    );
}
