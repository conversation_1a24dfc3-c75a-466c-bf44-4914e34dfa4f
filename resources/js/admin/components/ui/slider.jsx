'use client';

import { cn } from '@admin/lib/utils';
import * as SliderPrimitive from '@radix-ui/react-slider';

function Slider({ className, children, ...props }) {
    return (
        <SliderPrimitive.Root
            data-slot="slider"
            className={cn(
                'relative flex h-4 w-full touch-none select-none items-center',
                className,
            )}
            {...props}
        >
            <SliderPrimitive.Track className="bg-accent relative h-1.5 w-full overflow-hidden rounded-full">
                <SliderPrimitive.Range className="bg-primary absolute h-full" />
            </SliderPrimitive.Track>
            {children}
        </SliderPrimitive.Root>
    );
}

function SliderThumb({ className, ...props }) {
    return (
        <SliderPrimitive.Thumb
            data-slot="slider-thumb"
            className={cn(
                'border-primary bg-primary-foreground shadow-xs outline-hidden focus:outline-hidden box-content block size-4 shrink-0 cursor-pointer rounded-full border-[2px] shadow-black/5',
                className,
            )}
            {...props}
        />
    );
}

export { Slider, SliderThumb };
