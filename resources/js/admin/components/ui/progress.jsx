'use client';

import { cn } from '@admin/lib/utils';
import * as ProgressPrimitive from '@radix-ui/react-progress';

function Progress({ className, indicatorClassName, value, ...props }) {
    return (
        <ProgressPrimitive.Root
            data-slot="progress"
            className={cn(
                'bg-secondary relative h-2 w-full overflow-hidden rounded-full',
                className,
            )}
            {...props}
        >
            <ProgressPrimitive.Indicator
                data-slot="progress-indicator"
                className={cn(
                    'bg-primary h-full w-full flex-1 transition-all',
                    indicatorClassName,
                )}
                style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
            />
        </ProgressPrimitive.Root>
    );
}

export { Progress };
