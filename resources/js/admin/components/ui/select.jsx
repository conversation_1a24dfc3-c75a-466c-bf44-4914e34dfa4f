'use client';

import { cn } from '@admin/lib/utils';
import * as SelectPrimitive from '@radix-ui/react-select';
import { cva } from 'class-variance-authority';
import { Check, ChevronDown, ChevronUp } from 'lucide-react';
import * as React from 'react';
import { isValidElement } from 'react';

// Create a Context for `indicatorPosition` and `indicator` control
const SelectContext = React.createContext({
    indicatorPosition: 'left',
    indicator: null,
    indicatorVisibility: true,
});

// Root Component
const Select = ({
    indicatorPosition = 'left',
    indicatorVisibility = true,
    indicator,
    ...props
}) => {
    return (
        <SelectContext.Provider
            value={{ indicatorPosition, indicatorVisibility, indicator }}
        >
            <SelectPrimitive.Root {...props} />
        </SelectContext.Provider>
    );
};

function SelectGroup({ ...props }) {
    return <SelectPrimitive.Group data-slot="select-group" {...props} />;
}

function SelectValue({ ...props }) {
    return <SelectPrimitive.Value data-slot="select-value" {...props} />;
}

// Define size variants for SelectTrigger
const selectTriggerVariants = cva(
    `
    flex bg-background w-full items-center justify-between outline-none border border-input shadow-xs shadow-black/5 transition-shadow
    text-foreground data-placeholder:text-muted-foreground focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px]
    focus-visible:ring-ring/30 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1
    aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20
    [[data-invalid=true]_&]:border-destructive/60 [[data-invalid=true]_&]:ring-destructive/10  dark:[[data-invalid=true]_&]:border-destructive dark:[[data-invalid=true]_&]:ring-destructive/20
  `,
    {
        variants: {
            size: {
                sm: 'h-7 px-2.5 text-xs gap-1 rounded-md',
                md: 'h-8.5 px-3 text-[0.8125rem] leading-(--text-sm--line-height) gap-1 rounded-md',
                lg: 'h-10 px-4 text-sm gap-1.5 rounded-md',
            },
        },
        defaultVariants: {
            size: 'md',
        },
    },
);

function SelectTrigger({ className, children, size, ...props }) {
    return (
        <SelectPrimitive.Trigger
            data-slot="select-trigger"
            className={cn(selectTriggerVariants({ size }), className)}
            {...props}
        >
            {children}
            <SelectPrimitive.Icon asChild>
                <ChevronDown className="-me-0.5 h-4 w-4 opacity-60" />
            </SelectPrimitive.Icon>
        </SelectPrimitive.Trigger>
    );
}

function SelectScrollUpButton({ className, ...props }) {
    return (
        <SelectPrimitive.ScrollUpButton
            data-slot="select-scroll-up-button"
            className={cn(
                'flex cursor-default items-center justify-center py-1',
                className,
            )}
            {...props}
        >
            <ChevronUp className="h-4 w-4" />
        </SelectPrimitive.ScrollUpButton>
    );
}

function SelectScrollDownButton({ className, ...props }) {
    return (
        <SelectPrimitive.ScrollDownButton
            data-slot="select-scroll-down-button"
            className={cn(
                'flex cursor-default items-center justify-center py-1',
                className,
            )}
            {...props}
        >
            <ChevronDown className="h-4 w-4" />
        </SelectPrimitive.ScrollDownButton>
    );
}

function SelectContent({ className, children, position = 'popper', ...props }) {
    return (
        <SelectPrimitive.Portal>
            <SelectPrimitive.Content
                data-slot="select-content"
                className={cn(
                    'border-border bg-popover text-secondary-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md shadow-black/5',
                    position === 'popper' &&
                        'data-[side=bottom]:translate-y-1.5 data-[side=left]:-translate-x-1.5 data-[side=right]:translate-x-1.5 data-[side=top]:-translate-y-1.5',
                    className,
                )}
                position={position}
                {...props}
            >
                <SelectScrollUpButton />
                <SelectPrimitive.Viewport
                    className={cn(
                        'p-1.5',
                        position === 'popper' &&
                            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
                    )}
                >
                    {children}
                </SelectPrimitive.Viewport>
                <SelectScrollDownButton />
            </SelectPrimitive.Content>
        </SelectPrimitive.Portal>
    );
}

function SelectLabel({ className, ...props }) {
    return (
        <SelectPrimitive.Label
            data-slot="select-label"
            className={cn(
                'text-muted-foreground py-1.5 pe-2 ps-8 text-xs font-medium',
                className,
            )}
            {...props}
        />
    );
}

function SelectItem({ className, children, ...props }) {
    const { indicatorPosition, indicatorVisibility, indicator } =
        React.useContext(SelectContext);

    return (
        <SelectPrimitive.Item
            data-slot="select-item"
            className={cn(
                'outline-hidden text-foreground hover:bg-accent focus:bg-accent data-disabled:pointer-events-none data-disabled:opacity-50 relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 text-sm',
                indicatorPosition === 'left' ? 'pe-2 ps-8' : 'pe-8 ps-2',
                className,
            )}
            {...props}
        >
            {indicatorVisibility &&
                (indicator && isValidElement(indicator) ? (
                    indicator
                ) : (
                    <span
                        className={cn(
                            'absolute flex h-3.5 w-3.5 items-center justify-center',
                            indicatorPosition === 'left' ? 'start-2' : 'end-2',
                        )}
                    >
                        <SelectPrimitive.ItemIndicator>
                            <Check className="text-primary h-4 w-4" />
                        </SelectPrimitive.ItemIndicator>
                    </span>
                ))}
            <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
        </SelectPrimitive.Item>
    );
}

function SelectIndicator({ children, className, ...props }) {
    const { indicatorPosition } = React.useContext(SelectContext);

    return (
        <span
            data-slot="select-indicator"
            className={cn(
                'absolute top-1/2 flex -translate-y-1/2 items-center justify-center',
                indicatorPosition === 'left' ? 'start-2' : 'end-2',
                className,
            )}
            {...props}
        >
            <SelectPrimitive.ItemIndicator>
                {children}
            </SelectPrimitive.ItemIndicator>
        </span>
    );
}

function SelectSeparator({ className, ...props }) {
    return (
        <SelectPrimitive.Separator
            data-slot="select-separator"
            className={cn('bg-border -mx-1.5 my-1.5 h-px', className)}
            {...props}
        />
    );
}

export {
    Select,
    SelectContent,
    SelectGroup,
    SelectIndicator,
    SelectItem,
    SelectLabel,
    SelectScrollDownButton,
    SelectScrollUpButton,
    SelectSeparator,
    SelectTrigger,
    SelectValue,
};
