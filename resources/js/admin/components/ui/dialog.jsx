'use client';

import { cn } from '@admin/lib/utils';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { cva } from 'class-variance-authority';
import { X } from 'lucide-react';

const dialogContentVariants = cva(
    'flex flex-col fixed outline-0 z-50 border border-border bg-background p-6 shadow-lg shadow-black/5 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg',
    {
        variants: {
            variant: {
                default:
                    'left-[50%] top-[50%] max-w-lg translate-x-[-50%] translate-y-[-50%] w-full',
                fullscreen: 'inset-5',
            },
        },
        defaultVariants: {
            variant: 'default',
        },
    },
);

function Dialog({ ...props }) {
    return <DialogPrimitive.Root data-slot="dialog" {...props} />;
}

function DialogTrigger({ ...props }) {
    return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />;
}

function DialogPortal({ ...props }) {
    return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

function DialogClose({ ...props }) {
    return <DialogPrimitive.Close data-slot="dialog-close" {...props} />;
}

function DialogOverlay({ className, ...props }) {
    return (
        <DialogPrimitive.Overlay
            data-slot="dialog-overlay"
            className={cn(
                'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/30 [backdrop-filter:blur(4px)]',
                className,
            )}
            {...props}
        />
    );
}

function DialogContent({
    className,
    children,
    close = true,
    overlay = true,
    variant,
    ...props
}) {
    return (
        <DialogPortal>
            {overlay && <DialogOverlay />}
            <DialogPrimitive.Content
                data-slot="dialog-content"
                className={cn(dialogContentVariants({ variant }), className)}
                {...props}
            >
                {children}
                {close && (
                    <DialogClose className="ring-offset-background focus:outline-hidden data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute end-5 top-5 cursor-pointer rounded-sm opacity-60 outline-0 transition-opacity hover:opacity-100 disabled:pointer-events-none">
                        <X className="size-4" />
                        <span className="sr-only">Close</span>
                    </DialogClose>
                )}
            </DialogPrimitive.Content>
        </DialogPortal>
    );
}

export default DialogContent;

const DialogHeader = ({ className, ...props }) => (
    <div
        data-slot="dialog-header"
        className={cn(
            'mb-5 flex flex-col space-y-1 text-center sm:text-start',
            className,
        )}
        {...props}
    />
);

const DialogFooter = ({ className, ...props }) => (
    <div
        data-slot="dialog-footer"
        className={cn(
            'flex flex-col-reverse pt-5 sm:flex-row sm:justify-end sm:space-x-2.5',
            className,
        )}
        {...props}
    />
);

function DialogTitle({ className, ...props }) {
    return (
        <DialogPrimitive.Title
            data-slot="dialog-title"
            className={cn(
                'text-lg font-semibold leading-none tracking-tight',
                className,
            )}
            {...props}
        />
    );
}

const DialogBody = ({ className, ...props }) => (
    <div data-slot="dialog-body" className={cn('grow', className)} {...props} />
);

function DialogDescription({ className, ...props }) {
    return (
        <DialogPrimitive.Description
            data-slot="dialog-description"
            className={cn('text-muted-foreground text-sm', className)}
            {...props}
        />
    );
}

export {
    Dialog,
    DialogBody,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogOverlay,
    DialogPortal,
    DialogTitle,
    DialogTrigger,
};
