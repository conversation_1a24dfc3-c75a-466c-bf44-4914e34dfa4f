import { cn } from '@admin/lib/utils';
import { Editor } from '@tinymce/tinymce-react';
import { forwardRef } from 'react';

const RichTextEditor = forwardRef(
    ({ className, value, onChange, placeholder, disabled, ...props }, ref) => {
        const handleEditorChange = (content) => {
            if (onChange) {
                onChange(content);
            }
        };

        const apiKey = import.meta.env.VITE_TINYMCE_API_KEY;

        return (
            <div className={cn('relative', className)}>
                <Editor
                    ref={ref}
                    apiKey={apiKey} // You can get a free API key from TinyMCE
                    value={value}
                    onEditorChange={handleEditorChange}
                    disabled={disabled}
                    init={{
                        height: 400,
                        menubar: true,
                        plugins: [
                            'advlist',
                            'autolink',
                            'lists',
                            'link',
                            'image',
                            'charmap',
                            'preview',
                            'anchor',
                            'searchreplace',
                            'visualblocks',
                            'code',
                            'fullscreen',
                            'insertdatetime',
                            'media',
                            'table',
                            'code',
                            'help',
                            'wordcount',
                            'emoticons',
                            'template',
                            'codesample',
                        ],
                        toolbar: [
                            'undo redo | blocks | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify',
                            'bullist numlist outdent indent | removeformat | help | fullscreen preview',
                            'link image media table | codesample emoticons charmap | code',
                        ].join(' | '),
                        content_style: `
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                            font-size: 14px;
                            line-height: 1.6;
                        }
                    `,
                        placeholder: placeholder || 'Start typing...',
                        branding: false,
                        promotion: false,
                        resize: 'vertical',
                        elementpath: false,
                        statusbar: true,
                        paste_data_images: true,
                        automatic_uploads: true,
                        file_picker_types: 'image',
                        images_upload_handler: (blobInfo, progress) => {
                            return new Promise(async (resolve, reject) => {
                                try {
                                    const formData = new FormData();
                                    // TinyMCE expects the key to be 'file'
                                    formData.append(
                                        'file',
                                        blobInfo.blob(),
                                        blobInfo.filename(),
                                    );

                                    const response = await fetch(
                                        '/admin/uploads/tinymce',
                                        {
                                            method: 'POST',
                                            body: formData,
                                        },
                                    );

                                    if (!response.ok) {
                                        const text = await response.text();
                                        return reject(
                                            `Upload failed (${response.status}): ${text}`,
                                        );
                                    }

                                    const data = await response.json();
                                    const url = data?.url || data?.location;

                                    if (!url) {
                                        return reject(
                                            'Upload failed: Invalid server response.',
                                        );
                                    }

                                    // Optionally report completion
                                    if (typeof progress === 'function') {
                                        progress(100);
                                    }

                                    resolve(url);
                                } catch (err) {
                                    reject(
                                        err?.message ||
                                            'Unexpected error during upload',
                                    );
                                }
                            });
                        },
                        setup: (editor) => {
                            editor.on('init', () => {
                                // Apply custom styling to match the theme
                                const iframe = editor
                                    .getContainer()
                                    .querySelector('iframe');
                                if (iframe) {
                                    iframe.style.border =
                                        '1px solid hsl(var(--border))';
                                    iframe.style.borderRadius =
                                        'calc(var(--radius) - 2px)';
                                }
                            });
                        },
                    }}
                    {...props}
                />
            </div>
        );
    },
);

RichTextEditor.displayName = 'RichTextEditor';

export { RichTextEditor };
