'use client';

import { toggleVariants } from '@admin/components/ui/toggle';
import { cn } from '@admin/lib/utils';
import * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group';
import * as React from 'react';

const ToggleGroupContext = React.createContext({
    size: 'md',
    variant: 'default',
});

function ToggleGroup({ className, variant, size, children, ...props }) {
    return (
        <ToggleGroupPrimitive.Root
            data-slot="toggle-group"
            data-variant={variant}
            data-size={size}
            className={cn(
                'group/toggle-group data-[variant=outline]:shadow-xs flex items-center gap-1 rounded-md data-[variant=outline]:gap-0',
                className,
            )}
            {...props}
        >
            <ToggleGroupContext.Provider value={{ variant, size }}>
                {children}
            </ToggleGroupContext.Provider>
        </ToggleGroupPrimitive.Root>
    );
}

function ToggleGroupItem({ className, children, variant, size, ...props }) {
    const context = React.useContext(ToggleGroupContext);

    return (
        <ToggleGroupPrimitive.Item
            data-slot="toggle-group-item"
            data-variant={context.variant || variant}
            data-size={context.size || size}
            className={cn(
                toggleVariants({
                    variant: context.variant || variant,
                    size: context.size || size,
                }),
                'shrink-0 shadow-none focus:z-10 focus-visible:z-10 data-[variant=outline]:rounded-none data-[variant=outline]:border-s-0 data-[variant=outline]:first:rounded-s-md data-[variant=outline]:first:border-s data-[variant=outline]:last:rounded-e-md',
                className,
            )}
            {...props}
        >
            {children}
        </ToggleGroupPrimitive.Item>
    );
}

export { ToggleGroup, ToggleGroupItem };
