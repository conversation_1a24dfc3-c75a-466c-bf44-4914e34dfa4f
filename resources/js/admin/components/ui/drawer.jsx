'use client';

import { cn } from '@admin/lib/utils';
import { Drawer as DrawerPrimitive } from 'vaul';

const Drawer = ({ shouldScaleBackground = true, ...props }) => (
    <DrawerPrimitive.Root
        shouldScaleBackground={shouldScaleBackground}
        {...props}
    />
);

function DrawerTrigger({ ...props }) {
    return <DrawerPrimitive.Trigger data-slot="drawer-trigger" {...props} />;
}

function DrawerPortal({ ...props }) {
    return <DrawerPrimitive.Portal data-slot="drawer-portal" {...props} />;
}

function DrawerClose({ ...props }) {
    return <DrawerPrimitive.Close data-slot="drawer-close" {...props} />;
}

function DrawerOverlay({ className, ...props }) {
    return (
        <DrawerPrimitive.Overlay
            data-slot="drawer-overlay"
            className={cn('fixed inset-0 z-50 bg-black/80', className)}
            {...props}
        />
    );
}

function DrawerContent({ className, children, ...props }) {
    return (
        <DrawerPortal>
            <DrawerOverlay />
            <DrawerPrimitive.Content
                data-slot="drawer-content"
                className={cn(
                    'bg-background fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border',
                    className,
                )}
                {...props}
            >
                <div className="bg-muted mx-auto mt-4 h-2 w-[100px] rounded-full" />
                {children}
            </DrawerPrimitive.Content>
        </DrawerPortal>
    );
}

const DrawerHeader = ({ className, ...props }) => (
    <div
        data-slot="drawer-header"
        className={cn('grid gap-1.5 p-4 text-center sm:text-left', className)}
        {...props}
    />
);

const DrawerFooter = ({ className, ...props }) => (
    <div
        data-slot="drawer-footer"
        className={cn('mt-auto flex flex-col gap-2 p-4', className)}
        {...props}
    />
);

function DrawerTitle({ className, ...props }) {
    return (
        <DrawerPrimitive.Title
            data-slot="drawer-title"
            className={cn(
                'text-lg font-semibold leading-none tracking-tight',
                className,
            )}
            {...props}
        />
    );
}

function DrawerDescription({ className, ...props }) {
    return (
        <DrawerPrimitive.Description
            data-slot="drawer-description"
            className={cn('text-muted-foreground text-sm', className)}
            {...props}
        />
    );
}

export {
    Drawer,
    DrawerClose,
    DrawerContent,
    DrawerDescription,
    DrawerFooter,
    DrawerHeader,
    DrawerOverlay,
    DrawerPortal,
    DrawerTitle,
    DrawerTrigger,
};
