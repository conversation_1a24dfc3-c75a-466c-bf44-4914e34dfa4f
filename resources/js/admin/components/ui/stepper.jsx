/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { cn } from '@admin/lib/utils';
import * as React from 'react';
import { createContext, useContext } from 'react';

const StepperContext = createContext(undefined);
const StepItemContext = createContext(undefined);

function useStepper() {
    const ctx = useContext(StepperContext);
    if (!ctx) throw new Error('useStepper must be used within a Stepper');
    return ctx;
}

function useStepItem() {
    const ctx = useContext(StepItemContext);
    if (!ctx) throw new Error('useStepItem must be used within a StepperItem');
    return ctx;
}

function Stepper({
    defaultValue = 1,
    value,
    onValueChange,
    orientation = 'horizontal',
    className,
    children,
    indicators = {},
    ...props
}) {
    const [activeStep, setActiveStep] = React.useState(defaultValue);
    const [triggerNodes, setTriggerNodes] = React.useState([]);

    // Register/unregister triggers
    const registerTrigger = React.useCallback((node) => {
        setTriggerNodes((prev) => {
            if (node && !prev.includes(node)) {
                return [...prev, node];
            } else if (!node && prev.includes(node)) {
                return prev.filter((n) => n !== node);
            } else {
                return prev;
            }
        });
    }, []);

    const handleSetActiveStep = React.useCallback(
        (step) => {
            if (value === undefined) {
                setActiveStep(step);
            }
            onValueChange?.(step);
        },
        [value, onValueChange],
    );

    const currentStep = value ?? activeStep;

    // Keyboard navigation logic
    const focusTrigger = (idx) => {
        if (triggerNodes[idx]) triggerNodes[idx].focus();
    };
    const focusNext = (currentIdx) =>
        focusTrigger((currentIdx + 1) % triggerNodes.length);
    const focusPrev = (currentIdx) =>
        focusTrigger(
            (currentIdx - 1 + triggerNodes.length) % triggerNodes.length,
        );
    const focusFirst = () => focusTrigger(0);
    const focusLast = () => focusTrigger(triggerNodes.length - 1);

    // Context value
    const contextValue = React.useMemo(
        () => ({
            activeStep: currentStep,
            setActiveStep: handleSetActiveStep,
            stepsCount: React.Children.toArray(children).filter(
                (child) =>
                    React.isValidElement(child) &&
                    child.type.displayName === 'StepperItem',
            ).length,
            orientation,
            registerTrigger,
            focusNext,
            focusPrev,
            focusFirst,
            focusLast,
            triggerNodes,
            indicators,
        }),
        [
            currentStep,
            handleSetActiveStep,
            children,
            orientation,
            registerTrigger,
            triggerNodes,
        ],
    );

    return (
        <StepperContext.Provider value={contextValue}>
            <div
                role="tablist"
                aria-orientation={orientation}
                data-slot="stepper"
                className={cn('w-full', className)}
                data-orientation={orientation}
                {...props}
            >
                {children}
            </div>
        </StepperContext.Provider>
    );
}

function StepperItem({
    step,
    completed = false,
    disabled = false,
    loading = false,
    className,
    children,
    ...props
}) {
    const { activeStep } = useStepper();

    const state =
        completed || step < activeStep
            ? 'completed'
            : activeStep === step
              ? 'active'
              : 'inactive';

    const isLoading = loading && step === activeStep;

    return (
        <StepItemContext.Provider
            value={{ step, state, isDisabled: disabled, isLoading }}
        >
            <div
                data-slot="stepper-item"
                className={cn(
                    'group/step not-last:flex-1 flex items-center group-data-[orientation=horizontal]/stepper:flex-row group-data-[orientation=vertical]/stepper:flex-col',
                    className,
                )}
                data-state={state}
                {...(isLoading ? { 'data-loading': true } : {})}
                {...props}
            >
                {children}
            </div>
        </StepItemContext.Provider>
    );
}

function StepperTrigger({
    asChild = false,
    className,
    children,
    tabIndex,
    ...props
}) {
    const { state, isLoading } = useStepItem();
    const stepperCtx = useStepper();
    const {
        setActiveStep,
        activeStep,
        registerTrigger,
        triggerNodes,
        focusNext,
        focusPrev,
        focusFirst,
        focusLast,
    } = stepperCtx;
    const { step, isDisabled } = useStepItem();
    const isSelected = activeStep === step;
    const id = `stepper-tab-${step}`;
    const panelId = `stepper-panel-${step}`;

    // Register this trigger for keyboard navigation
    const btnRef = React.useRef(null);
    React.useEffect(() => {
        if (btnRef.current) {
            registerTrigger(btnRef.current);
        }
    }, [btnRef.current]);

    // Find our index among triggers for navigation
    const myIdx = React.useMemo(
        () => triggerNodes.findIndex((n) => n === btnRef.current),
        [triggerNodes, btnRef.current],
    );

    const handleKeyDown = (e) => {
        switch (e.key) {
            case 'ArrowRight':
            case 'ArrowDown':
                e.preventDefault();
                if (myIdx !== -1 && focusNext) focusNext(myIdx);
                break;
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                if (myIdx !== -1 && focusPrev) focusPrev(myIdx);
                break;
            case 'Home':
                e.preventDefault();
                if (focusFirst) focusFirst();
                break;
            case 'End':
                e.preventDefault();
                if (focusLast) focusLast();
                break;
            case 'Enter':
            case ' ':
                e.preventDefault();
                setActiveStep(step);
                break;
        }
    };

    if (asChild) {
        return (
            <span
                data-slot="stepper-trigger"
                data-state={state}
                className={className}
            >
                {children}
            </span>
        );
    }

    return (
        <button
            ref={btnRef}
            role="tab"
            id={id}
            aria-selected={isSelected}
            aria-controls={panelId}
            tabIndex={
                typeof tabIndex === 'number' ? tabIndex : isSelected ? 0 : -1
            }
            data-slot="stepper-trigger"
            data-state={state}
            data-loading={isLoading}
            className={cn(
                'focus-visible:border-ring focus-visible:ring-ring/30 inline-flex cursor-pointer items-center justify-center rounded-full outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-60',
                className,
            )}
            onClick={() => setActiveStep(step)}
            onKeyDown={handleKeyDown}
            disabled={isDisabled}
            {...props}
        >
            {children}
        </button>
    );
}

function StepperIndicator({ children, className }) {
    const { state, isLoading } = useStepItem();
    const { indicators } = useStepper();

    return (
        <div
            data-slot="stepper-indicator"
            data-state={state}
            className={cn(
                'border-background bg-accent text-accent-foreground data-[state=completed]:bg-primary data-[state=completed]:text-primary-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground flex size-9 shrink-0 items-center justify-center rounded-full border-4 text-sm',
                className,
            )}
        >
            {indicators &&
            ((isLoading && indicators.loading) ||
                (state === 'completed' && indicators.completed) ||
                (state === 'active' && indicators.active) ||
                (state === 'inactive' && indicators.inactive))
                ? (isLoading && indicators.loading) ||
                  (state === 'completed' && indicators.completed) ||
                  (state === 'active' && indicators.active) ||
                  (state === 'inactive' && indicators.inactive)
                : children}
        </div>
    );
}

function StepperSeparator({ className }) {
    const { state } = useStepItem();

    return (
        <div
            data-slot="stepper-separator"
            data-state={state}
            className={cn('border-b-border flex-1 border-b', className)}
        />
    );
}

function StepperTitle({ children, className }) {
    const { state } = useStepItem();

    return (
        <h3
            data-slot="stepper-title"
            data-state={state}
            className={cn('text-sm font-medium leading-none', className)}
        >
            {children}
        </h3>
    );
}

function StepperDescription({ children, className }) {
    const { state } = useStepItem();

    return (
        <div
            data-slot="stepper-description"
            data-state={state}
            className={cn('text-muted-foreground text-sm', className)}
        >
            {children}
        </div>
    );
}

function StepperNav({ children, className }) {
    const { activeStep } = useStepper();

    return (
        <nav
            data-slot="stepper-nav"
            data-state={activeStep}
            className={cn(
                'group/stepper flex data-[orientation=horizontal]:w-full data-[orientation=horizontal]:flex-row data-[orientation=vertical]:flex-col',
                className,
            )}
        >
            {children}
        </nav>
    );
}

function StepperPanel({ children, className }) {
    const { activeStep } = useStepper();

    return (
        <div
            data-slot="stepper-panel"
            data-state={activeStep}
            className={cn('w-full', className)}
        >
            {children}
        </div>
    );
}

function StepperContent({ value, forceMount, children, className }) {
    const { activeStep } = useStepper();
    const isActive = value === activeStep;

    if (!forceMount && !isActive) {
        return null;
    }

    return (
        <div
            data-slot="stepper-content"
            data-state={activeStep}
            className={cn(
                'w-full',
                className,
                !isActive && forceMount && 'hidden',
            )}
            hidden={!isActive && forceMount}
        >
            {children}
        </div>
    );
}

export {
    Stepper,
    StepperContent,
    StepperDescription,
    StepperIndicator,
    StepperItem,
    StepperNav,
    StepperPanel,
    StepperSeparator,
    StepperTitle,
    StepperTrigger,
    useStepItem,
    useStepper,
};
