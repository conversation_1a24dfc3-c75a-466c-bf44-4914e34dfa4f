/* eslint-disable react-refresh/only-export-components */
'use client';

import { Label } from '@admin/components/ui/label';
import { cn } from '@admin/lib/utils';
import { Slot } from '@radix-ui/react-slot';
import * as React from 'react';
import { Controller, FormProvider, useFormContext } from 'react-hook-form';

const Form = FormProvider;

const FormFieldContext = React.createContext({});

const FormField = ({ ...props }) => {
    return (
        <FormFieldContext.Provider value={{ name: props.name }}>
            <Controller {...props} />
        </FormFieldContext.Provider>
    );
};

const useFormField = () => {
    const fieldContext = React.useContext(FormFieldContext);
    const itemContext = React.useContext(FormItemContext);
    const { getFieldState, formState } = useFormContext();

    const fieldState = getFieldState(fieldContext.name, formState);

    if (!fieldContext) {
        throw new Error('useFormField should be used within <FormField>');
    }

    const { id } = itemContext;

    return {
        id,
        name: fieldContext.name,
        formItemId: `${id}-form-item`,
        formDescriptionId: `${id}-form-item-description`,
        formMessageId: `${id}-form-item-message`,
        ...fieldState,
    };
};

const FormItemContext = React.createContext({});

function FormItem({ className, ...props }) {
    const id = React.useId();
    const { error } = useFormField();

    return (
        <FormItemContext.Provider value={{ id }}>
            <div
                data-slot="form-item"
                className={cn('flex flex-col gap-2.5', className)}
                data-invalid={!!error}
                {...props}
            />
        </FormItemContext.Provider>
    );
}

function FormLabel({ className, ...props }) {
    const { formItemId } = useFormField();

    return (
        <Label
            data-slot="form-label"
            className={cn('text-foreground font-medium', className)}
            htmlFor={formItemId}
            {...props}
        />
    );
}

function FormControl({ ...props }) {
    const { error, formItemId, formDescriptionId, formMessageId } =
        useFormField();

    return (
        <Slot
            data-slot="form-control"
            id={formItemId}
            aria-describedby={
                !error
                    ? `${formDescriptionId}`
                    : `${formDescriptionId} ${formMessageId}`
            }
            aria-invalid={!!error}
            {...props}
        />
    );
}

function FormDescription({ className, ...props }) {
    const { formDescriptionId, error } = useFormField();

    if (error) {
        return null; // Hide the description when there's an error
    }

    return (
        <div
            data-slot="form-description"
            id={formDescriptionId}
            className={cn('text-muted-foreground -mt-0.5 text-xs', className)}
            {...props}
        />
    );
}

function FormMessage({ className, children, ...props }) {
    const { error, formMessageId } = useFormField();
    const body = error ? String(error?.message) : children;

    if (!body) {
        return null;
    }

    return (
        <div
            data-slot="form-message"
            id={formMessageId}
            className={cn(
                'text-destructive -mt-0.5 text-xs font-normal',
                className,
            )}
            {...props}
        >
            {body}
        </div>
    );
}

export {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    useFormField,
};
