'use client';

import { cn } from '@admin/lib/utils';
import { OTPInput, OTPInputContext } from 'input-otp';
import { Dot } from 'lucide-react';
import * as React from 'react';

function InputOTP({ className, containerClassName, ...props }) {
    return (
        <OTPInput
            data-slot="input-otp"
            containerClassName={cn(
                'has-disabled:opacity-50 flex items-center gap-2',
                containerClassName,
            )}
            className={cn('disabled:cursor-not-allowed', className)}
            {...props}
        />
    );
}

function InputOTPGroup({ className, ...props }) {
    return (
        <div
            data-slot="input-otp-group"
            className={cn('flex items-center', className)}
            {...props}
        />
    );
}

function InputOTPSlot({ index, className, ...props }) {
    const inputOTPContext = React.useContext(OTPInputContext);
    const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index];

    return (
        <div
            data-slot="input-otp-slot"
            className={cn(
                'border-input outline-hidden focus:border-ring relative flex h-10 w-10 items-center justify-center border-y border-e text-sm transition-all first:rounded-s-md first:border-s last:rounded-e-md',
                isActive && 'ring-ring ring-offset-background z-10 ring-2',
                className,
            )}
            {...props}
        >
            {char}
            {hasFakeCaret && (
                <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
                    <div className="animate-caret-blink bg-foreground h-4 w-px duration-1000" />
                </div>
            )}
        </div>
    );
}

function InputOTPSeparator({ ...props }) {
    return (
        <div data-slot="input-otp-separator" role="separator" {...props}>
            <Dot />
        </div>
    );
}

export { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot };
