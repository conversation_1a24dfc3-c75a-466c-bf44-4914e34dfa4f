'use client';

import { cn } from '@admin/lib/utils';
import { cva } from 'class-variance-authority';
import * as React from 'react';
import ReactSelect from 'react-select';

// Define exact variants matching Input component
const multiSelectVariants = cva(
    `
    flex w-full bg-background border border-input shadow-xs shadow-black/5 transition-[color,box-shadow] text-foreground placeholder:text-muted-foreground/80
    focus-within:ring-ring/30 focus-within:border-ring focus-within:outline-none focus-within:ring-[3px] rounded-md
    disabled:cursor-not-allowed disabled:opacity-60
    `,
    {
        variants: {
            size: {
                sm: 'h-7 px-2.5 text-xs',
                md: 'h-8.5 px-3 text-[0.8125rem] leading-(--text-sm--line-height)',
                lg: 'h-10 px-4 text-sm',
            },
        },
        defaultVariants: {
            size: 'md',
        },
    },
);

const MultiSelect = React.forwardRef(
    (
        {
            className,
            size = 'md',
            options = [],
            value = [],
            onChange,
            placeholder = 'Select options...',
            isDisabled = false,
            isClearable = true,
            isSearchable = true,
            noOptionsMessage = () => 'No options found',
            menuPortalTarget,
            ...props
        },
        ref,
    ) => {
        return (
            <ReactSelect
                ref={ref}
                isMulti
                options={options}
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                isDisabled={isDisabled}
                isClearable={isClearable}
                isSearchable={isSearchable}
                noOptionsMessage={noOptionsMessage}
                menuPortalTarget={menuPortalTarget}
                unstyled
                classNames={{
                    control: ({ isFocused }) =>
                        cn(
                            multiSelectVariants({ size }),
                            // Override focus styles to match Tailwind behavior
                            isFocused && 'ring-ring/30 border-ring ring-[3px]',
                            className,
                        ),
                    valueContainer: () => 'flex flex-wrap gap-1 p-0',
                    input: () =>
                        'text-foreground placeholder:text-muted-foreground/80',
                    placeholder: () => 'text-muted-foreground/80',
                    multiValue: () =>
                        'bg-secondary text-secondary-foreground rounded px-2 py-0.5 text-xs flex items-center gap-1',
                    multiValueLabel: () => 'text-xs',
                    multiValueRemove: () =>
                        'text-secondary-foreground/60 hover:text-secondary-foreground hover:bg-secondary-foreground/20 rounded-sm ml-1 flex items-center justify-center w-4 h-4',
                    menu: () =>
                        'bg-popover border border-border rounded-md shadow-lg mt-1 py-1 z-50',
                    menuList: () => 'max-h-64 overflow-auto',
                    option: ({ isSelected, isFocused }) =>
                        cn(
                            'cursor-pointer px-3 py-2 text-sm',
                            isSelected
                                ? 'bg-accent text-accent-foreground'
                                : isFocused
                                  ? 'bg-accent/50 text-accent-foreground'
                                  : 'text-popover-foreground hover:bg-accent/50',
                        ),
                    noOptionsMessage: () =>
                        'text-muted-foreground text-sm py-2 px-3',
                    indicatorSeparator: () => 'hidden',
                    dropdownIndicator: () =>
                        'text-muted-foreground hover:text-foreground p-1',
                    clearIndicator: () =>
                        'text-muted-foreground hover:text-foreground p-1',
                }}
                {...props}
            />
        );
    },
);

MultiSelect.displayName = 'MultiSelect';

export { MultiSelect };
