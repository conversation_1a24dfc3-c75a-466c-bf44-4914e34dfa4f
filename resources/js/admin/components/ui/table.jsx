'use client';

import { cn } from '@admin/lib/utils';

function Table({ className, ...props }) {
    return (
        <div
            data-slot="table-wrapper"
            className="relative w-full overflow-auto"
        >
            <table
                data-slot="table"
                className={cn(
                    'text-foreground w-full caption-bottom text-sm',
                    className,
                )}
                {...props}
            />
        </div>
    );
}

function TableHeader({ className, ...props }) {
    return (
        <thead
            data-slot="table-header"
            className={cn('[&_tr]:border-b', className)}
            {...props}
        />
    );
}

function TableBody({ className, ...props }) {
    return (
        <tbody
            data-slot="table-body"
            className={cn('[&_tr:last-child]:border-0', className)}
            {...props}
        />
    );
}

function TableFooter({ className, ...props }) {
    return (
        <tfoot
            data-slot="table-footer"
            className={cn(
                'bg-muted/50 border-t font-medium last:[&>tr]:border-b-0',
                className,
            )}
            {...props}
        />
    );
}

function TableRow({ className, ...props }) {
    return (
        <tr
            data-slot="table-row"
            className={cn(
                '[&:has(td):hover]:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',
                className,
            )}
            {...props}
        />
    );
}

function TableHead({ className, ...props }) {
    return (
        <th
            data-slot="table-head"
            className={cn(
                'text-muted-foreground h-12 px-4 text-left align-middle font-normal rtl:text-right [&:has([role=checkbox])]:pe-0',
                className,
            )}
            {...props}
        />
    );
}

function TableCell({ className, ...props }) {
    return (
        <td
            data-slot="table-cell"
            className={cn(
                'p-4 align-middle [&:has([role=checkbox])]:pe-0',
                className,
            )}
            {...props}
        />
    );
}

function TableCaption({ className, ...props }) {
    return (
        <caption
            data-slot="table-caption"
            className={cn('text-muted-foreground mt-4 text-sm', className)}
            {...props}
        />
    );
}

export {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
};
