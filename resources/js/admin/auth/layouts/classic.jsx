import { Card, CardContent } from '@admin/components/ui/card';
import { Link } from '@inertiajs/react';

export function ClassicLayout({ children }) {
    return (
        <>
            <style>
                {`
                    .page-bg {
                        background-image: url('/assets/images/bg-10.png');
                    }
                    .dark .page-bg {
                        background-image: url('/assets/images/bg-10-dark.png');
                    }
                `}
            </style>
            <div className="page-bg flex h-screen grow flex-col items-center justify-center bg-center bg-no-repeat">
                <div className="m-5">
                    <Link href="/">
                        <img
                            src={'/logo.png'}
                            className="h-[35px] max-w-none"
                            alt=""
                        />
                    </Link>
                </div>
                <Card className="w-full max-w-[400px]">
                    <CardContent className="p-6">{children}</CardContent>
                </Card>
            </div>
        </>
    );
}
