import { useAuth } from '@admin/auth/context/auth-context';
import { zodResolver } from '@hookform/resolvers/zod';
import {
    AlertCircle,
    Check,
    Eye,
    EyeOff,
    LoaderCircleIcon,
} from 'lucide-react';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Alert, AlertIcon, AlertTitle } from '@admin/components/ui/alert';
import { Button } from '@admin/components/ui/button';
import { Checkbox } from '@admin/components/ui/checkbox';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@admin/components/ui/form';
import { Input } from '@admin/components/ui/input';
import { useNavigate, useSearchParams } from '@hooks/navigation';
import { Link } from '@inertiajs/react';
import { getLoginSchema } from '../schemas/login-schema';

export function LoginForm() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { login, loading } = useAuth();
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);

    // Check for success message from password reset or error messages
    useEffect(() => {
        const pwdReset = searchParams.get('pwd_reset');
        const errorParam = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (pwdReset === 'success') {
            setSuccessMessage(
                'Your password has been successfully reset. You can now sign in with your new password.',
            );
        }

        if (errorParam) {
            switch (errorParam) {
                case 'auth_callback_failed':
                    setError(
                        errorDescription ||
                            'Authentication failed. Please try again.',
                    );
                    break;
                case 'auth_callback_error':
                    setError(
                        errorDescription ||
                            'An error occurred during authentication. Please try again.',
                    );
                    break;
                case 'auth_token_error':
                    setError(
                        errorDescription ||
                            'Failed to set authentication session. Please try again.',
                    );
                    break;
                default:
                    setError(
                        errorDescription ||
                            'Authentication error. Please try again.',
                    );
                    break;
            }
        }
    }, [searchParams]);

    const form = useForm({
        resolver: zodResolver(getLoginSchema()),
        defaultValues: {
            email: '',
            password: '',
            rememberMe: true,
        },
    });

    async function onSubmit(values) {
        try {
            setIsProcessing(true);
            setError(null);

            // Attempting to sign in with email

            // Simple validation
            if (!values.email.trim() || !values.password) {
                setError('Email and password are required');
                return;
            }

            // Sign in using the auth context
            await login(values.email, values.password, values.rememberMe);
        } catch (err) {
            console.error('Unexpected sign-in error:', err);
            setError(
                err instanceof Error
                    ? err.message
                    : 'An unexpected error occurred. Please try again.',
            );
        } finally {
            setIsProcessing(false);
        }
    }

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="block w-full space-y-5"
            >
                <div className="space-y-1 pb-3 text-center">
                    <h1 className="text-2xl font-semibold tracking-tight">
                        Sign In
                    </h1>
                    <p className="text-muted-foreground text-sm">
                        Welcome back! Log in with your credentials.
                    </p>
                </div>

                {error && (
                    <Alert
                        variant="destructive"
                        appearance="light"
                        onClose={() => setError(null)}
                    >
                        <AlertIcon>
                            <AlertCircle />
                        </AlertIcon>
                        <AlertTitle>{error}</AlertTitle>
                    </Alert>
                )}

                {successMessage && (
                    <Alert
                        appearance="light"
                        onClose={() => setSuccessMessage(null)}
                    >
                        <AlertIcon>
                            <Check />
                        </AlertIcon>
                        <AlertTitle>{successMessage}</AlertTitle>
                    </Alert>
                )}

                <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                                <Input placeholder="Your email" {...field} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem>
                            <div className="flex items-center justify-between gap-2.5">
                                <FormLabel>Password</FormLabel>
                            </div>
                            <div className="relative">
                                <Input
                                    placeholder="Your password"
                                    type={passwordVisible ? 'text' : 'password'} // Toggle input type
                                    {...field}
                                />

                                <Button
                                    type="button"
                                    variant="ghost"
                                    mode="icon"
                                    onClick={() =>
                                        setPasswordVisible(!passwordVisible)
                                    }
                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                >
                                    {passwordVisible ? (
                                        <EyeOff className="text-muted-foreground" />
                                    ) : (
                                        <Eye className="text-muted-foreground" />
                                    )}
                                </Button>
                            </div>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="rememberMe"
                    render={({ field }) => (
                        <FormItem className="flex flex-col space-y-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <FormLabel className="cursor-pointer text-sm font-normal">
                                        Remember me
                                    </FormLabel>
                                </div>
                                <Link
                                    href="/auth/reset-password"
                                    className="text-foreground hover:text-primary text-sm font-semibold"
                                >
                                    Forgot Password?
                                </Link>
                            </div>
                        </FormItem>
                    )}
                />

                <Button
                    type="submit"
                    className="w-full"
                    disabled={isProcessing}
                >
                    {isProcessing ? (
                        <span className="flex items-center gap-2">
                            <LoaderCircleIcon className="h-4 w-4 animate-spin" />{' '}
                            Loading...
                        </span>
                    ) : (
                        <span>Login</span>
                    )}
                </Button>
            </form>
        </Form>
    );
}
