import { makePageTitle, resolvePage } from '@/helpers/app-helper.js';
import { createInertiaApp } from '@inertiajs/react';
import 'preline';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import 'vanilla-calendar-pro';

createInertiaApp({
    title: makePageTitle,
    resolve: (name) => resolvePage(name),
    setup({ el, App, props }) {
        createRoot(el).render(
            <StrictMode>
                <App {...props} />
            </StrictMode>,
        );
    },
    progress: {
        color: '#004728',
    },
}).then((res) => res);
