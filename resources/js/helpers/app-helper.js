import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

const APP_NAME = import.meta.env.VITE_APP_NAME;

export const appName = APP_NAME || 'Everest Sherpa Adventure';

export function makePageTitle(title) {
    if (title === undefined || title === null || title === '') {
        return appName;
    }
    return `${title} - ${appName}`;
}

export function resolvePage(name) {
    return resolvePageComponent(
        `../pages/${name}.jsx`,
        import.meta.glob('../pages/**/*.jsx'),
    );
}

export function resolveAdminPage(name) {
    return resolvePageComponent(
        `../admin/pages/${name}.jsx`,
        import.meta.glob('../admin/pages/**/*.jsx'),
    );
}
