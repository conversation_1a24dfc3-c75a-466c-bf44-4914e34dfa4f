import { RxCalendar } from 'react-icons/rx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function BlogsDetails({ title, blog }) {
    const breadcrumbLinks = [
        { title: 'Blogs', href: '/blogs' },
        { title: 'Blog Details' },
    ];

    // Format the date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
        });
    };

    // Get the blog image URL
    const getBlogImageUrl = (image) => {
        if (!image) return '/assets/img-landscape.png'; // fallback image
        return image.startsWith('/storage/') ? image : `/storage/${image}`;
    };

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="pb-18 container px-4 pt-8">
                    <div className="h-auto w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg">
                        <img
                            src={getBlogImageUrl(blog?.image)}
                            alt={blog?.title || 'Blog post image'}
                            className="h-full max-h-[480px] w-full object-cover"
                        />
                    </div>
                    <div className="lg:w-9/10 mx-auto mt-4 py-6">
                        <div className="flex flex-wrap items-center gap-3 md:gap-6">
                            {/*<div className="inline-flex shrink-0 items-center gap-2">
                                <BsPerson className="text-primary size-6" />
                                <span className="text-md font-medium">
                                    By Admin
                                </span>
                            </div>*/}
                            <div className="inline-flex shrink-0 items-center gap-2">
                                <RxCalendar className="text-primary size-6" />
                                <span className="text-md font-medium">
                                    {blog?.created_at
                                        ? formatDate(blog.created_at)
                                        : 'Date not available'}
                                </span>
                            </div>
                            {/*<div className="inline-flex shrink-0 items-center gap-2">
                                <GoComment className="text-primary size-6" />
                                <span className="text-md font-medium">
                                    0 Comments
                                </span>
                            </div>*/}
                        </div>
                        <h1 className="mt-8 text-3xl font-semibold md:text-4xl">
                            {blog?.title || 'Blog Post Title'}
                        </h1>
                        {blog?.destination && (
                            <div className="mt-4">
                                <span className="bg-primary/10 text-primary inline-block rounded-full px-3 py-1 text-sm font-medium">
                                    {blog.destination.name}
                                </span>
                            </div>
                        )}
                        <div
                            className="blog-content text-md mt-6 md:text-lg [&>h2]:mb-2 [&>h2]:mt-6 [&>h2]:text-2xl [&>h2]:font-[600] [&>h3]:mb-2 [&>h3]:mt-4 [&>h3]:text-xl [&>h3]:font-[600] [&_div]:mb-4 [&_p]:mb-4 [&_p]:font-medium [&_p]:leading-8 [&_p]:text-slate-800"
                            dangerouslySetInnerHTML={{
                                __html:
                                    blog?.content ||
                                    '<p>Content not available</p>',
                            }}
                        />
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
