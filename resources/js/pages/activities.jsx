import ActivityCard from '../components/package/ActivityCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Destinations({
    title,
    destination,
    activities = [],
    destinationData = {},
    error = null,
}) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        { title: destinationData.name || title },
    ];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 py-12">
                    {destinationData.description && (
                        <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: destinationData.description,
                                }}
                            ></div>
                        </div>
                    )}
                </section>
                <section className="pb-18 container px-4 pt-8">
                    <div className="py-4 text-center">
                        <h2 className="ff-myrd-web text-primary mb-3 text-4xl md:text-5xl">
                            Activities
                        </h2>
                    </div>
                    {error ? (
                        <div className="py-12 text-center">
                            <div className="mx-auto max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
                                <div className="mb-2 text-lg font-medium text-red-600">
                                    Error Loading Activities
                                </div>
                                <p className="text-red-500">{error}</p>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="mt-4 rounded bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
                                >
                                    Try Again
                                </button>
                            </div>
                        </div>
                    ) : activities.length === 0 ? (
                        <div className="py-12 text-center">
                            <div className="text-lg text-gray-500">
                                No activities available for this destination.
                            </div>
                        </div>
                    ) : (
                        <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                            {activities.map((activity, index) => (
                                <ActivityCard
                                    key={activity.id || activity.slug || index}
                                    name={activity.name}
                                    image={activity.image}
                                    packages={
                                        activity.packages_count ||
                                        activity.packages
                                    }
                                    href={`/packages/${destination}/${activity.slug}`}
                                />
                            ))}
                        </div>
                    )}
                </section>
            </AppLayout>
        </>
    );
}
