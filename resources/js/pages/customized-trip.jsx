import Breadcrumb from '@/components/shared/Breadcrumb.jsx';
import HeroTopSection from '@/components/shared/HeroTopSection.jsx';
import Alert from '@/components/ui/Alert.jsx';
import Button from '@/components/ui/Button.jsx';
import FloatingDatepicker from '@/components/ui/form-elements/FloatingDatepicker.jsx';
import FloatingInput from '@/components/ui/form-elements/FloatingInput.jsx';
import FloatingSelect from '@/components/ui/form-elements/FloatingSelect.jsx';
import FloatingTextarea from '@/components/ui/form-elements/FloatingTextarea.jsx';
import AppLayout from '@/layouts/AppLayout.jsx';
import { useForm } from '@inertiajs/react';
import { useEffect, useState } from 'react';

export default function CustomizedTrip({ title, countries, packages, flash }) {
    const breadcrumbLinks = [{ title }];
    const [alert, setAlert] = useState(null);

    const { data, setData, post, processing, errors, reset } = useForm({
        full_name: '',
        email: '',
        phone: '',
        country_id: '',
        package_id: '',
        travel_date: '',
        trip_duration: '',
        number_of_adults: 1,
        number_of_children: 0,
        estimated_budget: '',
        notes: '',
        accept_terms: false,
    });

    useEffect(() => {
        if (flash?.success) {
            setAlert({
                type: 'success',
                message: flash.success,
            });
            reset();
        }
    }, [flash]);

    const handleSubmit = (event) => {
        event.preventDefault();
        setAlert(null);

        if (!data.accept_terms) {
            setAlert({
                type: 'warning',
                message: 'You must accept the terms and conditions.',
            });
            return;
        }

        post(route('front.customized-trip.submit'), {
            onError: () => {
                setAlert({
                    type: 'error',
                    message:
                        'There was an error submitting your request. Please check the form and try again.',
                });
            },
        });
    };

    return (
        <AppLayout title={title}>
            <HeroTopSection title={title}>
                <Breadcrumb links={breadcrumbLinks} />
            </HeroTopSection>
            <section className="pb-18 container px-4 pt-8">
                <div className="card bg-base-100 card-md border border-gray-100 shadow-sm">
                    <div className="card-body">
                        <form className="my-8" onSubmit={handleSubmit}>
                            <div className="grid gap-6 sm:grid-cols-1">
                                <h3 className="text-primary text-lg font-semibold">
                                    Personal Information
                                </h3>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="Full Name*"
                                        name="full_name"
                                        value={data.full_name}
                                        onChange={(e) =>
                                            setData('full_name', e.target.value)
                                        }
                                        error={errors.full_name}
                                    />
                                    <FloatingInput
                                        label="Email Address*"
                                        name="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) =>
                                            setData('email', e.target.value)
                                        }
                                        error={errors.email}
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="Country Code + Phone*"
                                        name="phone"
                                        value={data.phone}
                                        onChange={(e) =>
                                            setData('phone', e.target.value)
                                        }
                                        error={errors.phone}
                                    />
                                    <FloatingSelect
                                        label="Country*"
                                        name="country_id"
                                        value={data.country_id}
                                        onChange={(e) =>
                                            setData(
                                                'country_id',
                                                e.target.value,
                                            )
                                        }
                                        error={errors.country_id}
                                    >
                                        <option value="">Choose country</option>
                                        {countries?.map((country) => (
                                            <option
                                                key={country.id}
                                                value={country.id}
                                            >
                                                {country.name}
                                            </option>
                                        ))}
                                    </FloatingSelect>
                                </div>
                                <h3 className="text-primary text-lg font-semibold">
                                    Trip Details
                                </h3>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingSelect
                                        label="Choose a package (optional)"
                                        name="package_id"
                                        value={data.package_id}
                                        onChange={(e) =>
                                            setData(
                                                'package_id',
                                                e.target.value,
                                            )
                                        }
                                        error={errors.package_id}
                                    >
                                        <option value="">
                                            Choose package (optional)
                                        </option>
                                        {packages?.map((pkg) => (
                                            <option key={pkg.id} value={pkg.id}>
                                                {pkg.name}
                                            </option>
                                        ))}
                                    </FloatingSelect>
                                    <FloatingDatepicker
                                        label="Approx. Date of Travel"
                                        name="travel_date"
                                        value={data.travel_date}
                                        onChange={(e) =>
                                            setData(
                                                'travel_date',
                                                e.target.value,
                                            )
                                        }
                                        error={errors.travel_date}
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <div className="grid gap-6 sm:grid-cols-2">
                                        <FloatingInput
                                            type="number"
                                            label="No. of Adults*"
                                            name="number_of_adults"
                                            min="1"
                                            value={data.number_of_adults}
                                            onChange={(e) =>
                                                setData(
                                                    'number_of_adults',
                                                    parseInt(e.target.value) ||
                                                        1,
                                                )
                                            }
                                            error={errors.number_of_adults}
                                        />
                                        <FloatingInput
                                            type="number"
                                            label="No. of Children*"
                                            name="number_of_children"
                                            min="0"
                                            value={data.number_of_children}
                                            onChange={(e) =>
                                                setData(
                                                    'number_of_children',
                                                    parseInt(e.target.value) ||
                                                        0,
                                                )
                                            }
                                            error={errors.number_of_children}
                                        />
                                    </div>
                                    <FloatingInput
                                        type="number"
                                        label="Trip Duration (days)"
                                        name="trip_duration"
                                        min="1"
                                        value={data.trip_duration}
                                        onChange={(e) =>
                                            setData(
                                                'trip_duration',
                                                e.target.value,
                                            )
                                        }
                                        error={errors.trip_duration}
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        type="number"
                                        label="Estimated Budget (USD)"
                                        name="estimated_budget"
                                        min="0"
                                        step="0.01"
                                        value={data.estimated_budget}
                                        onChange={(e) =>
                                            setData(
                                                'estimated_budget',
                                                e.target.value,
                                            )
                                        }
                                        error={errors.estimated_budget}
                                    />
                                    <div></div>
                                </div>
                                <div className="">
                                    <FloatingTextarea
                                        label="Additional Notes/Requirements"
                                        name="notes"
                                        rows={6}
                                        value={data.notes}
                                        onChange={(e) =>
                                            setData('notes', e.target.value)
                                        }
                                        error={errors.notes}
                                    />
                                </div>
                                <div className="">
                                    <label className="label">
                                        <input
                                            type="checkbox"
                                            className="checkbox checkbox-primary"
                                            name="accept_terms"
                                            checked={data.accept_terms}
                                            onChange={(e) =>
                                                setData(
                                                    'accept_terms',
                                                    e.target.checked,
                                                )
                                            }
                                        />
                                        I read and accept all the{' '}
                                        <a
                                            href="/terms"
                                            className="text-primary"
                                        >
                                            terms and conditions
                                        </a>
                                    </label>
                                </div>

                                {alert && (
                                    <Alert
                                        type={alert.type}
                                        message={alert.message}
                                    />
                                )}
                                <div className="card-actions">
                                    <Button
                                        variant="primary"
                                        size="md"
                                        className="px-12"
                                        disabled={processing}
                                    >
                                        {processing
                                            ? 'Submitting...'
                                            : 'Submit'}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </section>
        </AppLayout>
    );
}
