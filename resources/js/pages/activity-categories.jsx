import ActivityCard from '../components/package/ActivityCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function ActivityCategories({ title, destination, category }) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        { title: 'Nepal', href: '/activities/nepal' },
        { title: title },
    ];
    const activities = [
        {
            name: 'Everest Treks',
            slug: 'everest-treks',
            image: '/assets/destinations/nepal.jpg',
            packages: 150,
        },
        {
            name: 'Annapurna Treks',
            slug: 'annapurna-treks',
            image: '/assets/destinations/nepal.jpg',
            packages: 150,
        },
        {
            name: 'Langtang Treks',
            slug: 'langtang-treks',
            image: '/assets/destinations/nepal.jpg',
            packages: 150,
        },
        {
            name: 'Manaslu Treks',
            slug: 'manaslu-treks',
            image: '/assets/destinations/nepal.jpg',
            packages: 150,
        },
    ];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 py-12">
                    <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                        <p className="mb-8">
                            Trekking Nepal Nepal was NepalIt was exactly 6 am in
                            the morning when my travel partner Upashana and I
                            took the bus to Bhaktapur from Chabahil for a day in
                            Bhaktapur Durbar Square. Curious to know why we head
                            out that early? Well, you need to keep reading the
                            blog till the last, and I’ll try to keep it shorter
                            and more enjoyable.
                        </p>
                        <p className="mb-0">
                            Nepal was exactly 6 am in the morning Nepal when my
                            travel partner Upashana and I took the bus to
                            Bhaktapur from Chabahil for a day in Bhaktapur
                            Durbar Square. Curious to know why we head out that
                            early? Well, you need to keep reading the blog till
                            the last, and I’ll try to keep it shorter and more
                            enjoyable Nepal.
                        </p>
                    </div>
                </section>
                <section className="container px-4 pt-8 pb-18">
                    <div className="py-4 text-center">
                        <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                            Activities
                        </h2>
                    </div>
                    <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                        {[...activities, ...activities].map(
                            (activity, index) => (
                                <ActivityCard
                                    key={activity.slug + index}
                                    name={activity.name}
                                    image={activity.image}
                                    packages={activity.packages}
                                    href={`/packages/${destination}/${activity.slug}`}
                                />
                            ),
                        )}
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
