import ArticleCard from '@/components/blog/ArticleCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import Pagination from '../components/shared/Pagination.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Destinations({ title, blogs }) {
    const breadcrumbLinks = [{ title: 'Blogs' }];

    const blogList = blogs?.data || blogs || [];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 pt-8 pb-18">
                    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        {blogList.map((blog, index) => (
                            <ArticleCard
                                key={blog.id || index}
                                title={blog.title}
                                imgSrc={blog.image}
                                content={blog.content}
                                url={blog.url}
                                created_at={blog.created_at}
                                imgClassName={'aspect-[5/3]'}
                            />
                        ))}
                    </div>
                    <Pagination data={blogs} />
                </section>
            </AppLayout>
        </>
    );
}
