import BestOffers from '../components/home/<USER>';
import HeroSection from '../components/home/<USER>';
import News from '../components/home/<USER>';
import PlanYourTrip from '../components/home/<USER>';
import PopularPackages from '../components/home/<USER>';
import ReviewAndBrands from '../components/home/<USER>';
import SearchForm from '../components/home/<USER>';
import SolabansVillage from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import TopDestinations from '../components/home/<USER>';
import TravelExperience from '../components/home/<USER>';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Home({ featuredPackages = [], topDestinations = [], testimonials = [], latestPosts = [], homePageSettings = {} }) {
    return (
        <>
            <AppLayout title="Home" containerClass="home-page">
                <HeroSection settings={homePageSettings.hero || {}} />
                <div className="relative -mt-[40px] mb-8 md:-mt-[70px] xl:-mt-10">
                    <SearchForm />
                </div>
                <TopDestinations destinations={topDestinations} settings={homePageSettings.top_destination || {}} />
                <PlanYourTrip settings={homePageSettings.plan_trip || {}} />
                <BestOffers settings={homePageSettings.best_offers || {}} />
                <PopularPackages packages={featuredPackages} settings={homePageSettings.popular_packages || {}} />
                <TravelExperience settings={homePageSettings.travel_experience || {}} />
                <SolabansVillage settings={homePageSettings.solabans_village || {}} />
                <Testimonials testimonials={testimonials} settings={homePageSettings.testimonials || {}} />
                <ReviewAndBrands settings={homePageSettings.review_brands || {}} />
                <News posts={latestPosts} settings={homePageSettings.news || {}} />
            </AppLayout>
        </>
    );
}
