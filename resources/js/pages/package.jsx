import PackageReply from '@/components/package/PackageReply.jsx';
import ReviewRatingInput from '@/components/package/ReviewRatingInput.jsx';
import LiveSupportCard from '@/components/shared/LiveSupportCard.jsx';
import Accordion from '@/components/ui/Accordion.jsx';
import CustomAccordion from '@/components/ui/CustomAccordion.jsx';
import FloatingInput from '@/components/ui/form-elements/FloatingInput.jsx';
import FloatingTextarea from '@/components/ui/form-elements/FloatingTextarea.jsx';
import Icon from '@/components/ui/Icon.jsx';
import { Link } from '@inertiajs/react';
import { CgArrowLongRight } from 'react-icons/cg';
import { FaGlobeAsia, FaPlay, FaStar } from 'react-icons/fa';
import { GiMountainRoad } from 'react-icons/gi';
import { IoMdShare } from 'react-icons/io';
import { PiMapPin } from 'react-icons/pi';
import { TbCalendar, TbCalendarClock } from 'react-icons/tb';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import Button from '../components/ui/Button.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Package(props) {
    const { title, destination, category, slug, package: packageData } = props;

    console.log(props);

    // Helper function to get attribute value by name
    const getAttributeValue = (name, defaultValue = 'N/A') => {
        const attribute = packageData?.attributes?.find(
            (attr) => attr.name?.toLowerCase() === name.toLowerCase(),
        );
        return attribute?.value || defaultValue;
    };

    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        {
            title: packageData?.destination || 'Destination',
            href: `/activities/${destination}`,
        },
        {
            title: packageData?.activities?.[0]?.name || 'Activity',
            href: `/activities/${destination}/${category}`,
        },
        {
            title: `${packageData?.activities?.[0]?.name || 'Activity'}`,
            href: `/packages/${destination}/${category}`,
        },
        {
            title: packageData?.name || title,
        },
    ];

    // Use dynamic pricing data
    const prices =
        packageData?.prices?.length > 0
            ? packageData.prices.map((price) => ({
                  title: price.group_size,
                  price: price.price,
              }))
            : [];

    // Use dynamic plan data from package
    const planData =
        packageData?.plans?.length > 0
            ? packageData.plans.map((plan) => ({
                  day: plan.day,
                  title: plan.title,
                  description: plan.description,
                  icon: <Icon name={'trekking'} className="size-4.5" />,
              }))
            : [];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="pb-18 container px-4 pt-8">
                    <div className="h-auto w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg">
                        {packageData?.image ? (
                            <img
                                src={packageData?.image}
                                alt={packageData?.name || title}
                                className="h-full w-full object-cover"
                            />
                        ) : (
                            <img
                                src={'/assets/img-landscape.png'}
                                alt={packageData?.name || title}
                                className="max-h-[350px] w-full object-cover"
                            />
                        )}
                    </div>
                    <div className="w-full">
                        <div className="mt-6 flex flex-col gap-4 sm:mt-8 sm:flex-row sm:justify-between">
                            <div className="flex flex-col">
                                <h1 className="text-primary mb-3 text-2xl font-semibold sm:text-3xl md:text-4xl">
                                    {packageData?.name || title}
                                </h1>
                                <div className="flex flex-col gap-2 sm:flex-row sm:gap-6">
                                    <div className="inline-flex shrink-0 items-center gap-2">
                                        <span className="sm:text-md text-sm font-medium">
                                            {packageData?.reviews_count || 0}{' '}
                                            review
                                            {packageData?.reviews_count !== 1
                                                ? 's'
                                                : ''}
                                        </span>
                                        <div className="flex gap-1">
                                            {[...Array(5)].map((_, i) => (
                                                <FaStar
                                                    key={i}
                                                    className={`size-4 sm:size-5 ${
                                                        i <
                                                        Math.floor(
                                                            packageData?.average_rating ||
                                                                0,
                                                        )
                                                            ? 'text-yellow-400'
                                                            : 'text-gray-300'
                                                    }`}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                    <div className="inline-flex shrink-0 items-center gap-2">
                                        <PiMapPin className="size-4 text-gray-800 sm:size-5" />
                                        <span className="sm:text-md text-sm font-medium">
                                            {packageData?.location ||
                                                'Solukhumbu, Nepal'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="item-center flex">
                                <Button outline>
                                    <span>Share</span>
                                    <IoMdShare className="size-4" />
                                </Button>
                            </div>
                        </div>
                        <div className="my-6 grid gap-6 border-y border-gray-300 py-4 sm:grid-cols-2 lg:grid-cols-4">
                            <div className="flex items-center gap-3">
                                <span className="border-1 inline-block rounded-full p-3">
                                    <TbCalendarClock className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs font-medium leading-[1.3] text-gray-600">
                                        Duration
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        {packageData?.duration || '12 days'}
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="border-1 inline-block rounded-full p-3">
                                    <FaGlobeAsia className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs font-medium leading-[1.3] text-gray-600">
                                        Country
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        {packageData?.destination || 'Nepal'}
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="border-1 inline-block rounded-full p-3">
                                    <GiMountainRoad className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs font-medium leading-[1.3] text-gray-600">
                                        Maximum Altitude
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        {getAttributeValue(
                                            'Maximum Altitude',
                                            'N/A',
                                        )}
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="border-1 inline-block rounded-full p-3">
                                    <TbCalendar className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs font-medium leading-[1.2] text-gray-600">
                                        Best Time
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        {getAttributeValue(
                                            'Best Time',
                                            'Year Round',
                                        )}
                                    </strong>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 gap-0 md:grid-cols-3 md:gap-8 [&>div]:mb-4">
                        <div className="col-span-2">
                            {/** Overview */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Overview
                                </h2>
                                <div className="text-md text-gray-700 [&_p]:mb-4">
                                    {packageData?.description ? (
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: packageData.description,
                                            }}
                                        />
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No overview available for this
                                            package.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {/** Photos & Videos */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Photos & Videos
                                </h2>
                                {packageData?.media?.length > 0 ? (
                                    <>
                                        <div className="grid w-full gap-3 sm:grid-cols-3 [&_img]:h-full [&_img]:w-full [&_img]:rounded-lg [&_img]:object-cover [&_img]:object-center">
                                            {packageData.media[0] && (
                                                <div className="w-max-full sm:col-span-2">
                                                    <img
                                                        src={
                                                            packageData.media[0]
                                                                .url
                                                        }
                                                        alt={
                                                            packageData.media[0]
                                                                .caption ||
                                                            packageData.name
                                                        }
                                                    />
                                                </div>
                                            )}
                                            <div className="grid gap-3 sm:col-span-1">
                                                {packageData.media
                                                    .slice(1, 3)
                                                    .map((media, index) => (
                                                        <img
                                                            key={
                                                                media.id ||
                                                                index
                                                            }
                                                            src={media.url}
                                                            alt={
                                                                media.caption ||
                                                                packageData.name
                                                            }
                                                        />
                                                    ))}
                                            </div>
                                        </div>
                                        {packageData.media.length > 3 && (
                                            <div className="relative mt-3 overflow-hidden rounded-lg [&_img]:h-full [&_img]:w-full [&_img]:rounded-lg [&_img]:object-cover [&_img]:object-center">
                                                <img
                                                    src={
                                                        packageData.media[3]
                                                            ?.url
                                                    }
                                                    alt={
                                                        packageData.media[3]
                                                            ?.caption ||
                                                        packageData.name
                                                    }
                                                />
                                                {packageData.media[3]?.type ===
                                                    'video' && (
                                                    <>
                                                        <div className="absolute left-0 top-0 h-full w-full bg-black/25"></div>
                                                        <div className="-translate-1/2 absolute left-1/2 top-1/2">
                                                            <button className="inline-block cursor-pointer rounded-full bg-white/50 p-6 text-white">
                                                                <FaPlay className="size-8" />
                                                            </button>
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        )}
                                    </>
                                ) : (
                                    <p className="italic text-gray-500">
                                        No media available for this package.
                                    </p>
                                )}
                            </div>
                            {/** Plan/Itinerary */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Plan/Itinerary
                                </h2>
                                <div className="text-md text-gray-700 [&_p]:mb-4">
                                    {planData.length > 0 ? (
                                        <CustomAccordion data={planData} />
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No itinerary available for this
                                            package.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {/** Route Map */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Route Map
                                </h2>
                                <div className="text-md mb-4 w-full text-gray-700">
                                    {packageData?.route_map ? (
                                        <img
                                            src={packageData.route_map}
                                            alt={`Route map for ${packageData?.name || title}`}
                                            className="w-full rounded-lg"
                                        />
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No route map available for this
                                            package.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {/** Cost Details */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Cost Details
                                </h2>
                                <div className="text-md text-gray-700 [&_p]:mb-4">
                                    {packageData?.cost_details?.length > 0 ? (
                                        <>
                                            {/* Group cost details by type */}
                                            {Object.entries(
                                                packageData.cost_details.reduce(
                                                    (groups, cost) => {
                                                        const type =
                                                            cost.type ||
                                                            'Other';
                                                        if (!groups[type])
                                                            groups[type] = [];
                                                        groups[type].push(cost);
                                                        return groups;
                                                    },
                                                    {},
                                                ),
                                            ).map(([type, costs]) => (
                                                <div
                                                    key={type}
                                                    className="mb-6"
                                                >
                                                    <h4 className="mb-3 text-xl font-semibold capitalize">
                                                        {type === 'included'
                                                            ? 'Includes'
                                                            : type ===
                                                                'excluded'
                                                              ? 'Excludes'
                                                              : type}
                                                    </h4>
                                                    <ul
                                                        className={`list-outside ${type === 'included' ? 'list-image-(--list-bullet-img-includes)' : 'list-image-(--list-bullet-img-excludes)'} pl-7 [&>li]:pb-2`}
                                                    >
                                                        {costs.map(
                                                            (cost, index) => (
                                                                <li
                                                                    key={
                                                                        cost.id ||
                                                                        index
                                                                    }
                                                                >
                                                                    {
                                                                        cost.description
                                                                    }
                                                                </li>
                                                            ),
                                                        )}
                                                    </ul>
                                                </div>
                                            ))}
                                        </>
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No cost details available for this
                                            package.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {/** FAQ */}
                            <div className="mb-4">
                                <h2 className="mb-3 text-2xl font-semibold">
                                    Frequently Asked Questions
                                </h2>
                                <div className="">
                                    {packageData?.faqs?.length > 0 ? (
                                        <Accordion
                                            id="faq-details"
                                            items={packageData.faqs.map(
                                                (faq) => ({
                                                    question: faq.question,
                                                    answer: faq.answer,
                                                }),
                                            )}
                                        />
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No FAQs available for this package.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {/** Client Reviews */}
                            <div className="mb-4">
                                <h2 className="mb-4 text-2xl font-semibold">
                                    Client Reviews
                                </h2>
                                <div className="mb-6 flex flex-col gap-4">
                                    {packageData?.reviews?.length > 0 ? (
                                        packageData.reviews.map(
                                            (review, index) => (
                                                <PackageReply
                                                    key={review.id || index}
                                                    name={review.name}
                                                    rating={review.rating}
                                                    comment={review.comment}
                                                    date={review.created_at}
                                                />
                                            ),
                                        )
                                    ) : (
                                        <p className="italic text-gray-500">
                                            No reviews available for this
                                            package yet.
                                        </p>
                                    )}
                                </div>
                                <div className="rounded-xl bg-gray-100 px-5 py-6">
                                    <h3 className="mb-4 text-xl font-semibold">
                                        Add Your Review
                                    </h3>
                                    <div className="mb-4 flex items-center gap-4">
                                        <span className="text-lg font-semibold text-gray-500">
                                            Your Rating
                                        </span>
                                        <ReviewRatingInput
                                            value={4}
                                            onInput={(val) =>
                                                console.log(
                                                    'Rating changed: ' + val,
                                                )
                                            }
                                        />
                                    </div>
                                    <div className="mb-6 flex w-full flex-col gap-5">
                                        <div className="grid gap-5 sm:grid-cols-2">
                                            <FloatingInput
                                                name="name"
                                                label="Your name"
                                            />
                                            <FloatingInput
                                                name="phone"
                                                label="Your phone"
                                            />
                                        </div>
                                        <FloatingInput
                                            type="email"
                                            name="email"
                                            label="Your email"
                                        />
                                        <FloatingTextarea
                                            type="email"
                                            name="email"
                                            label="Type your message"
                                        />
                                    </div>
                                    <div className="mb-4">
                                        <Button
                                            variant="primary"
                                            size="lg"
                                            className="!text-md font-medium"
                                        >
                                            Post Your Comment
                                            <CgArrowLongRight />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-span-1">
                            <div className="mb-8 rounded-xl border border-gray-100 p-5 shadow-lg">
                                <div className="mb-4 flex justify-between gap-4">
                                    <p className="text-primary font-medium">
                                        Price From
                                    </p>
                                    <p className="text-primary text-lg font-semibold">
                                        $
                                        {packageData?.base_price ||
                                            (prices.length > 0
                                                ? prices[0]?.price
                                                : 'Contact Us')}
                                    </p>
                                </div>
                                {prices.length > 0 && (
                                    <div className="border-primary mb-4 overflow-hidden rounded-xl border">
                                        <div className="text-md border-primary bg-primary border px-3 py-2 text-white">
                                            Group Discount Price
                                        </div>
                                        <div className="grid gap-1 px-4 py-3">
                                            {prices.map((price, index) => (
                                                <p
                                                    className="flex justify-between gap-2"
                                                    key={index}
                                                >
                                                    <span>{price.title}</span>
                                                    <span>${price.price}</span>
                                                </p>
                                            ))}
                                        </div>
                                    </div>
                                )}
                                <div className="mb-4">
                                    <Link
                                        href={route('front.booking.create', packageData.id)}
                                        className="btn btn-primary btn-lg mb-3 w-full rounded-xl font-medium"
                                    >
                                        Book This Trip
                                    </Link>
                                    <Link
                                        href={`/customized-trip`}
                                        className="btn btn-lg w-full rounded-xl font-medium"
                                    >
                                        Customize a Trip
                                    </Link>
                                </div>
                                <div className="text-center">
                                    <button className="group/btn-download inline-flex cursor-pointer items-center gap-1 text-xs font-semibold text-gray-600 hover:text-gray-800">
                                        <Icon
                                            name="file-download"
                                            size="16"
                                            className="fill-gray-600 group-hover/btn-download:fill-gray-800"
                                        />
                                        Download a Brouchure
                                    </button>
                                </div>
                            </div>

                            <LiveSupportCard />
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
