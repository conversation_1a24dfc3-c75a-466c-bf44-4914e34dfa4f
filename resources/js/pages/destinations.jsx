import DestinationCard from '../components/package/DestinationCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Destinations({
    title,
    destinations = [],
    error = null,
}) {
    const breadcrumbLinks = [{ title }];

    // Fallback destinations if none provided from backend
    const destinationList = destinations.length > 0 ? destinations : [];

    return (
        <>
            <AppLayout title="Activities">
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container mt-8 px-4 py-12">
                    {error ? (
                        <div className="py-12 text-center">
                            <div className="mx-auto max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
                                <div className="mb-2 text-lg font-medium text-red-600">
                                    Error Loading Destinations
                                </div>
                                <p className="text-red-500">{error}</p>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="mt-4 rounded bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
                                >
                                    Try Again
                                </button>
                            </div>
                        </div>
                    ) : destinationList.length === 0 ? (
                        <div className="py-12 text-center">
                            <div className="text-lg text-gray-500">
                                No destinations available at the moment.
                            </div>
                        </div>
                    ) : (
                        <div className="grid gap-6 sm:grid-cols-2">
                            {destinationList.map((destination) => (
                                <DestinationCard
                                    key={destination.slug}
                                    name={destination.name}
                                    image={destination.image}
                                    slug={destination.slug}
                                    packages={destination.packages_count}
                                />
                            ))}
                        </div>
                    )}
                </section>
            </AppLayout>
        </>
    );
}
