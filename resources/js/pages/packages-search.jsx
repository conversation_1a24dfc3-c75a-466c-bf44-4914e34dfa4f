import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import PackageCard from '@/components/package/PackageCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import OffCanvas from '../components/ui/OffCanvas.jsx';
import AppLayout from '../layouts/AppLayout.jsx';
import { FaSearch, FaFilter } from 'react-icons/fa';
import { MdClear } from 'react-icons/md';

export default function PackagesSearch({ packages, destinations, activities, filters }) {
    const [searchForm, setSearchForm] = useState({
        search: filters.search || '',
        destination: filters.destination || '',
        type: filters.type || '',
        duration: filters.duration || ''
    });

    const [isLoading, setIsLoading] = useState(false);

    const breadcrumbLinks = [
        { name: 'Home', href: '/' },
        { name: 'Package Search', href: '/packages' }
    ];

    const packageList = packages?.data || [];

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        
        const searchParams = new URLSearchParams();
        Object.keys(searchForm).forEach(key => {
            if (searchForm[key]) {
                searchParams.append(key, searchForm[key]);
            }
        });

        router.get('/packages', Object.fromEntries(searchParams), {
            preserveState: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleClear = () => {
        setSearchForm({
            search: '',
            destination: '',
            type: '',
            duration: ''
        });
        setIsLoading(true);
        router.get('/packages', {}, {
            preserveState: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleInputChange = (field, value) => {
        setSearchForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    return (
        <>
            <AppLayout>
                <HeroTopSection>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>

                <section className="container mx-auto px-4 py-8">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Search Packages</h1>
                        <p className="text-gray-600">Find your perfect adventure package</p>
                    </div>

                    {/* Search and Filter Form */}
                    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                {/* Search Input */}
                                <div className="lg:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Search
                                    </label>
                                    <div className="relative">
                                        <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                        <input
                                            type="text"
                                            value={searchForm.search}
                                            onChange={(e) => handleInputChange('search', e.target.value)}
                                            placeholder="Search by package name, description, or location..."
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        />
                                    </div>
                                </div>

                                {/* Destination Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Destination
                                    </label>
                                    <select
                                        value={searchForm.destination}
                                        onChange={(e) => handleInputChange('destination', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">All Destinations</option>
                                        {destinations?.map((destination) => (
                                            <option key={destination.id} value={destination.id}>
                                                {destination.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Activity Type Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Activity Type
                                    </label>
                                    <select
                                        value={searchForm.type}
                                        onChange={(e) => handleInputChange('type', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">All Activities</option>
                                        {activities?.map((activity) => (
                                            <option key={activity.id} value={activity.name}>
                                                {activity.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Duration Filter */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Duration (days)
                                    </label>
                                    <select
                                        value={searchForm.duration}
                                        onChange={(e) => handleInputChange('duration', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Any Duration</option>
                                        <option value="1-3">1-3 days</option>
                                        <option value="4-7">4-7 days</option>
                                        <option value="8-14">8-14 days</option>
                                        <option value="15+">15+ days</option>
                                    </select>
                                </div>

                                {/* Action Buttons */}
                                <div className="md:col-span-2 flex items-end gap-3">
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex items-center gap-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        <FaSearch className="w-4 h-4" />
                                        {isLoading ? 'Searching...' : 'Search'}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleClear}
                                        className="flex items-center gap-2 bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                                    >
                                        <MdClear className="w-4 h-4" />
                                        Clear
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    {/* Results Section */}
                    <div className="mb-6">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold text-gray-900">
                                {packages?.total ? `${packages.total} packages found` : 'Search Results'}
                            </h2>
                            {Object.values(filters).some(filter => filter) && (
                                <div className="text-sm text-gray-600">
                                    Active filters applied
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Package Grid */}
                    {packageList.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {packageList.map((_package, index) => (
                                <PackageCard
                                    key={_package.id || index}
                                    {..._package}
                                    href={`/packages/${_package.destination?.slug || 'destination'}/${_package.activities?.[0]?.slug || 'activity'}/${_package.slug}`}
                                    forSlider={false}
                                />
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 text-center">
                            <div className="text-lg text-gray-500 mb-4">
                                {Object.values(filters).some(filter => filter) 
                                    ? 'No packages found matching your search criteria.' 
                                    : 'No packages available at the moment.'
                                }
                            </div>
                            {Object.values(filters).some(filter => filter) && (
                                <button
                                    onClick={handleClear}
                                    className="text-blue-600 hover:text-blue-800 underline"
                                >
                                    Clear filters and show all packages
                                </button>
                            )}
                        </div>
                    )}

                    {/* Pagination */}
                    {packages?.links && packages.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <nav className="flex items-center space-x-2">
                                {packages.links.map((link, index) => {
                                    if (link.url === null) {
                                        return (
                                            <span
                                                key={index}
                                                className="px-3 py-2 text-gray-400 cursor-not-allowed"
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        );
                                    }
                                    
                                    return (
                                        <button
                                            key={index}
                                            onClick={() => {
                                                setIsLoading(true);
                                                router.get(link.url, {}, {
                                                    preserveState: true,
                                                    onFinish: () => setIsLoading(false)
                                                });
                                            }}
                                            className={`px-3 py-2 rounded-lg transition-colors ${
                                                link.active
                                                    ? 'bg-blue-600 text-white'
                                                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                            }`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    );
                                })}
                            </nav>
                        </div>
                    )}
                </section>
            </AppLayout>

            <OffCanvas idRef="filter-offcanvas" />
        </>
    );
}