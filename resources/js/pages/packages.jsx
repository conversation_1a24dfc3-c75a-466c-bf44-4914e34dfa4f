import PackageCard from '@/components/package/PackageCard.jsx';
import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import OffCanvas from '../components/ui/OffCanvas.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Packages({
    title,
    destination,
    category,
    packages = [],
    destinationData = {},
    activityData = {},
}) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        {
            title: destinationData.name || destination,
            href: `/activities/${destination}`,
        },
        {
            title: activityData.name || category,
            href: `/activities/${destination}/${category}`,
        },
        { title: title },
    ];

    const packageList = packages.data;

    console.log(packageList);

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 py-12">
                    <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                        {activityData.description && (
                            <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: activityData.description,
                                    }}
                                ></div>
                            </div>
                        )}
                    </div>
                </section>
                <section className="pb-18 container px-4 pt-8">
                    <div className="py-4 text-center">
                        <h2 className="ff-myrd-web text-primary mb-3 text-4xl md:text-5xl">
                            Packages
                        </h2>
                    </div>
                    <div className="mb-2 flex justify-between">
                        <p className="text-md font-medium">
                            Showing {packageList.length ?? 0} Packages
                        </p>
                        {/*<div className="text-md font-medium">
                            <button
                                className="flex cursor-pointer items-center gap-1"
                                type="button"
                                aria-haspopup="dialog"
                                aria-expanded="false"
                                aria-controls="filter-offcanvas"
                                data-hs-overlay="#filter-offcanvas"
                            >
                                <LuListFilter className="size-5" />{' '}
                                <span>Filter</span>
                            </button>
                        </div>*/}
                    </div>
                    {packageList.length > 0 && (
                        <div className="grid grid-cols-[repeat(auto-fit,_minmax(275px,_1fr))] gap-1 lg:-mx-4">
                            {packageList.map((_package, index) => (
                                <PackageCard
                                    key={_package.id || index}
                                    {..._package}
                                    href={`/packages/${destination}/${category}/${_package.slug}`}
                                    forSlider={false}
                                />
                            ))}
                        </div>
                    )}
                    {packageList.length === 0 && (
                        <div className="py-12 text-center">
                            <div className="text-lg text-gray-500">
                                No packages available at the moment.
                            </div>
                        </div>
                    )}
                </section>
            </AppLayout>

            <OffCanvas idRef="filter-offcanvas" />
        </>
    );
}
