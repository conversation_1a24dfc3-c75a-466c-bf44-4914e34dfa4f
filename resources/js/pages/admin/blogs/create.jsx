import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { RichTextEditor } from '@admin/components/ui/rich-text-editor.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function CreateBlog({ destinations, title, description }) {
    const { data, setData, post, processing, errors } = useForm({
        title: '',
        slug: '',
        content: '',
        image: null,
        status: 'draft',
        destination_id: 'none',
    });

    const [imagePreview, setImagePreview] = useState(null);

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onload = (e) => setImagePreview(e.target.result);
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.blogs.store'), {
            onSuccess: () => {
                toast.success('Blog created successfully');
            },
            onError: () => {
                toast.error('Failed to create blog');
            },
        });
    };

    return (
        <AdminLayout
            actions={
                <Link href={route('admin.blogs.index')}>
                    <Button variant="ghost" size="sm">
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                </Link>
            }
            title={title}
            description={description}
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Blog Details</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) =>
                                                setData('title', e.target.value)
                                            }
                                            placeholder="Enter blog title"
                                            className={
                                                errors.title
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.title && (
                                            <p className="text-destructive text-sm">
                                                {errors.title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="slug">Slug *</Label>
                                        <Input
                                            id="slug"
                                            value={data.slug}
                                            onChange={(e) =>
                                                setData('slug', e.target.value)
                                            }
                                            placeholder="Enter blog slug (e.g., my-blog-post)"
                                            className={
                                                errors.slug
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.slug && (
                                            <p className="text-destructive text-sm">
                                                {errors.slug}
                                            </p>
                                        )}
                                        <p className="text-sm text-muted-foreground">
                                            URL-friendly version of the title. Use lowercase letters, numbers, and hyphens only.
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">
                                            Content *
                                        </Label>
                                        <RichTextEditor
                                            value={data.content}
                                            onChange={(content) =>
                                                setData('content', content)
                                            }
                                            placeholder="Enter blog content"
                                            className={
                                                errors.content
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.content && (
                                            <p className="text-destructive text-sm">
                                                {errors.content}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Image Upload */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Featured Image</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="image">Image</Label>
                                        <Input
                                            id="image"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleImageChange}
                                            className={
                                                errors.image
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.image && (
                                            <p className="text-destructive text-sm">
                                                {errors.image}
                                            </p>
                                        )}
                                    </div>

                                    {imagePreview && (
                                        <div className="mt-4">
                                            <img
                                                src={imagePreview}
                                                alt="Preview"
                                                className="h-48 max-w-full rounded-lg border object-cover"
                                            />
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Publish Settings</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status *</Label>
                                        <Select
                                            value={data.status}
                                            onValueChange={(value) =>
                                                setData('status', value)
                                            }
                                        >
                                            <SelectTrigger
                                                className={
                                                    errors.status
                                                        ? 'border-destructive'
                                                        : ''
                                                }
                                            >
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="draft">
                                                    Draft
                                                </SelectItem>
                                                <SelectItem value="published">
                                                    Published
                                                </SelectItem>
                                                <SelectItem value="archived">
                                                    Archived
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && (
                                            <p className="text-destructive text-sm">
                                                {errors.status}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="destination">
                                            Destination
                                        </Label>
                                        <Select
                                            value={data.destination_id}
                                            onValueChange={(value) =>
                                                setData('destination_id', value)
                                            }
                                        >
                                            <SelectTrigger
                                                className={
                                                    errors.destination_id
                                                        ? 'border-destructive'
                                                        : ''
                                                }
                                            >
                                                <SelectValue placeholder="Select destination" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">
                                                    No destination
                                                </SelectItem>
                                                {destinations.map(
                                                    (destination) => (
                                                        <SelectItem
                                                            key={destination.id}
                                                            value={destination.id.toString()}
                                                        >
                                                            {destination.name}
                                                        </SelectItem>
                                                    ),
                                                )}
                                            </SelectContent>
                                        </Select>
                                        {errors.destination_id && (
                                            <p className="text-destructive text-sm">
                                                {errors.destination_id}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardContent className="pt-6">
                                    <div className="flex flex-col space-y-2">
                                        <Button
                                            type="submit"
                                            disabled={processing}
                                        >
                                            <Save className="mr-2 h-4 w-4" />
                                            {processing
                                                ? 'Creating...'
                                                : 'Create Blog'}
                                        </Button>
                                        <Link href={route('admin.blogs.index')}>
                                            <Button
                                                variant="ghost"
                                                className="w-full"
                                            >
                                                Cancel
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
