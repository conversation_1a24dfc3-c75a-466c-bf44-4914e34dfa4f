import {
    Toolbar,
    ToolbarActions,
    ToolbarHeading,
} from '@/admin/layouts/components/toolbar';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router, useForm } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Edit,
    Plus,
    Search,
    Trash2,
    User,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function UsersIndex({
    users,
    filters,
    title,
    description,
    ...props
}) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingUser, setEditingUser] = useState(null);
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [typeFilter, setTypeFilter] = useState(filters.type || 'all');

    const {
        data: addData,
        setData: setAddData,
        post,
        processing: addProcessing,
        errors: addErrors,
        reset: resetAdd,
    } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        type: '',
    });

    const {
        data: editData,
        setData: setEditData,
        put,
        processing: editProcessing,
        errors: editErrors,
        reset: resetEdit,
    } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        type: '',
    });

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.users.index'),
            {
                search: searchTerm,
                type: typeFilter === 'all' ? '' : typeFilter,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleAddUser = (e) => {
        e.preventDefault();
        post(route('admin.users.store'), {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                resetAdd();
                toast.success('User created successfully');
            },
            onError: () => {
                toast.error('Failed to create user');
            },
        });
    };

    const handleEditUser = (e) => {
        e.preventDefault();
        put(route('admin.users.update', editingUser.id), {
            onSuccess: () => {
                setIsEditDialogOpen(false);
                setEditingUser(null);
                resetEdit();
                toast.success('User updated successfully');
            },
            onError: () => {
                toast.error('Failed to update user');
            },
        });
    };

    const handleDeleteUser = (userId) => {
        router.delete(route('admin.users.destroy', userId), {
            onSuccess: () => {
                toast.success('User deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete user');
            },
        });
    };

    const openEditDialog = (user) => {
        setEditingUser(user);
        setEditData({
            name: user.name,
            email: user.email,
            password: '',
            password_confirmation: '',
            type: user.type || '',
        });
        setIsEditDialogOpen(true);
    };

    return (
        <>
            <Head title={title} />
            <AdminLayout toolbar={false}>
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <Dialog
                            open={isAddDialogOpen}
                            onOpenChange={setIsAddDialogOpen}
                        >
                            <div className="w-full">
                                <Toolbar>
                                    <ToolbarHeading
                                        title={title}
                                        description={description}
                                    />
                                    <ToolbarActions>
                                        <DialogTrigger asChild>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Add User
                                            </Button>
                                        </DialogTrigger>
                                    </ToolbarActions>
                                </Toolbar>
                            </div>

                            <DialogContent className="sm:max-w-[425px]">
                                <form onSubmit={handleAddUser}>
                                    <DialogHeader>
                                        <DialogTitle>Add New User</DialogTitle>
                                        <DialogDescription>
                                            Create a new user account with the
                                            details below.
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="grid gap-4 py-4">
                                        <div className="grid gap-2">
                                            <Label htmlFor="add-name">
                                                Name
                                            </Label>
                                            <Input
                                                id="add-name"
                                                value={addData.name}
                                                onChange={(e) =>
                                                    setAddData(
                                                        'name',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder="Enter user name"
                                            />
                                            {addErrors.name && (
                                                <span className="text-destructive text-sm">
                                                    {addErrors.name}
                                                </span>
                                            )}
                                        </div>
                                        <div className="grid gap-2">
                                            <Label htmlFor="add-email">
                                                Email
                                            </Label>
                                            <Input
                                                id="add-email"
                                                type="email"
                                                value={addData.email}
                                                onChange={(e) =>
                                                    setAddData(
                                                        'email',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder="Enter email address"
                                            />
                                            {addErrors.email && (
                                                <span className="text-destructive text-sm">
                                                    {addErrors.email}
                                                </span>
                                            )}
                                        </div>
                                        <div className="grid gap-2">
                                            <Label htmlFor="add-type">
                                                Type
                                            </Label>
                                            <Select
                                                value={addData.type}
                                                onValueChange={(value) =>
                                                    setAddData('type', value)
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select user type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="user">
                                                        User
                                                    </SelectItem>
                                                    <SelectItem value="admin">
                                                        Admin
                                                    </SelectItem>
                                                    <SelectItem value="partner">
                                                        Partner
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {addErrors.type && (
                                                <span className="text-destructive text-sm">
                                                    {addErrors.type}
                                                </span>
                                            )}
                                        </div>
                                        <div className="grid gap-2">
                                            <Label htmlFor="add-password">
                                                Password
                                            </Label>
                                            <Input
                                                id="add-password"
                                                type="password"
                                                value={addData.password}
                                                onChange={(e) =>
                                                    setAddData(
                                                        'password',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder="Enter password"
                                            />
                                            {addErrors.password && (
                                                <span className="text-destructive text-sm">
                                                    {addErrors.password}
                                                </span>
                                            )}
                                        </div>
                                        <div className="grid gap-2">
                                            <Label htmlFor="add-password-confirmation">
                                                Confirm Password
                                            </Label>
                                            <Input
                                                id="add-password-confirmation"
                                                type="password"
                                                value={
                                                    addData.password_confirmation
                                                }
                                                onChange={(e) =>
                                                    setAddData(
                                                        'password_confirmation',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder="Confirm password"
                                            />
                                        </div>
                                    </div>
                                    <DialogFooter>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() =>
                                                setIsAddDialogOpen(false)
                                            }
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            type="submit"
                                            disabled={addProcessing}
                                        >
                                            {addProcessing
                                                ? 'Creating...'
                                                : 'Create User'}
                                        </Button>
                                    </DialogFooter>
                                </form>
                            </DialogContent>
                        </Dialog>
                    </div>

                    {/* Filters */}
                    <Card>
                        <CardContent>
                            <form
                                onSubmit={handleSearch}
                                className="flex items-end gap-4"
                            >
                                <div className="flex-1">
                                    <Label htmlFor="search">Search</Label>
                                    <div className="relative">
                                        <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
                                        <Input
                                            id="search"
                                            value={searchTerm}
                                            onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                            }
                                            placeholder="Search by name or email..."
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div className="w-48">
                                    <Label htmlFor="type-filter">
                                        Type Filter
                                    </Label>
                                    <Select
                                        value={typeFilter}
                                        onValueChange={setTypeFilter}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All types" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                All types
                                            </SelectItem>
                                            <SelectItem value="admin">
                                                Admin
                                            </SelectItem>
                                            <SelectItem value="user">
                                                User
                                            </SelectItem>
                                            <SelectItem value="partner">
                                                Partner
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <Button type="submit">Search</Button>
                                {(searchTerm ||
                                    (typeFilter && typeFilter !== 'all')) && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setTypeFilter('all');
                                            router.get(
                                                route('admin.users.index'),
                                                {},
                                                {
                                                    preserveState: true,
                                                    replace: true,
                                                },
                                            );
                                        }}
                                    >
                                        Clear
                                    </Button>
                                )}
                            </form>
                        </CardContent>
                    </Card>

                    {/* Users Table */}
                    <Card>
                        <CardContent className="p-0">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-16">
                                            ID
                                        </TableHead>
                                        <TableHead>User</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="w-32">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {users.data.length > 0 ? (
                                        users.data.map((user) => (
                                            <TableRow key={user.id}>
                                                <TableCell className="font-medium">
                                                    {user.id}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        <div className="flex-shrink-0">
                                                            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                                <User className="text-primary h-4 w-4" />
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <div className="font-medium">
                                                                {user.name}
                                                            </div>
                                                            <div className="text-muted-foreground text-sm">
                                                                {user.email}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {user.type ? (
                                                        <Badge variant="secondary">
                                                            {user.type}
                                                        </Badge>
                                                    ) : (
                                                        <span className="text-muted-foreground">
                                                            -
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        variant={
                                                            user.email_verified_at
                                                                ? 'default'
                                                                : 'destructive'
                                                        }
                                                    >
                                                        {user.email_verified_at
                                                            ? 'Verified'
                                                            : 'Unverified'}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(
                                                        user.created_at,
                                                    ).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                openEditDialog(
                                                                    user,
                                                                )
                                                            }
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-destructive"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Are you
                                                                        sure?
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                        This
                                                                        will
                                                                        permanently
                                                                        delete
                                                                        the user
                                                                        "
                                                                        {
                                                                            user.name
                                                                        }
                                                                        " and
                                                                        all
                                                                        associated
                                                                        data.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeleteUser(
                                                                                user.id,
                                                                            )
                                                                        }
                                                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell
                                                colSpan={6}
                                                className="py-8 text-center"
                                            >
                                                <div className="text-muted-foreground">
                                                    No users found.
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>

                            {/* Pagination */}
                            <div className="border-t px-4 py-4">
                                <div className="flex items-center justify-between">
                                    <div className="text-muted-foreground text-sm">
                                        Showing {users.from || 0} to{' '}
                                        {users.to || 0} of {users.total} results
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                if (users.current_page > 1) {
                                                    router.get(
                                                        route(
                                                            'admin.users.index',
                                                        ),
                                                        {
                                                            page:
                                                                users.current_page -
                                                                1,
                                                            search: searchTerm,
                                                            type:
                                                                typeFilter ===
                                                                'all'
                                                                    ? ''
                                                                    : typeFilter,
                                                        },
                                                        {
                                                            preserveState: true,
                                                            replace: true,
                                                        },
                                                    );
                                                }
                                            }}
                                            disabled={users.current_page <= 1}
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            Previous
                                        </Button>
                                        <span className="text-sm">
                                            Page {users.current_page} of{' '}
                                            {users.last_page}
                                        </span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                if (
                                                    users.current_page <
                                                    users.last_page
                                                ) {
                                                    router.get(
                                                        route(
                                                            'admin.users.index',
                                                        ),
                                                        {
                                                            page:
                                                                users.current_page +
                                                                1,
                                                            search: searchTerm,
                                                            type:
                                                                typeFilter ===
                                                                'all'
                                                                    ? ''
                                                                    : typeFilter,
                                                        },
                                                        {
                                                            preserveState: true,
                                                            replace: true,
                                                        },
                                                    );
                                                }
                                            }}
                                            disabled={
                                                users.current_page >=
                                                users.last_page
                                            }
                                        >
                                            Next
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Edit User Dialog */}
                    <Dialog
                        open={isEditDialogOpen}
                        onOpenChange={setIsEditDialogOpen}
                    >
                        <DialogContent className="sm:max-w-[425px]">
                            <form onSubmit={handleEditUser}>
                                <DialogHeader>
                                    <DialogTitle>Edit User</DialogTitle>
                                    <DialogDescription>
                                        Update the user details below. Leave
                                        password fields empty to keep current
                                        password.
                                    </DialogDescription>
                                </DialogHeader>
                                <div className="grid gap-4 py-4">
                                    <div className="grid gap-2">
                                        <Label htmlFor="edit-name">Name</Label>
                                        <Input
                                            id="edit-name"
                                            value={editData.name}
                                            onChange={(e) =>
                                                setEditData(
                                                    'name',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter user name"
                                        />
                                        {editErrors.name && (
                                            <span className="text-destructive text-sm">
                                                {editErrors.name}
                                            </span>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="edit-email">
                                            Email
                                        </Label>
                                        <Input
                                            id="edit-email"
                                            type="email"
                                            value={editData.email}
                                            onChange={(e) =>
                                                setEditData(
                                                    'email',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter email address"
                                        />
                                        {editErrors.email && (
                                            <span className="text-destructive text-sm">
                                                {editErrors.email}
                                            </span>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="edit-type">Type</Label>
                                        <Select
                                            value={editData.type}
                                            onValueChange={(value) =>
                                                setEditData('type', value)
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select user type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="user">
                                                    User
                                                </SelectItem>
                                                <SelectItem value="admin">
                                                    Admin
                                                </SelectItem>
                                                <SelectItem value="partner">
                                                    Partner
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {editErrors.type && (
                                            <span className="text-destructive text-sm">
                                                {editErrors.type}
                                            </span>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="edit-password">
                                            New Password (optional)
                                        </Label>
                                        <Input
                                            id="edit-password"
                                            type="password"
                                            value={editData.password}
                                            onChange={(e) =>
                                                setEditData(
                                                    'password',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter new password"
                                        />
                                        {editErrors.password && (
                                            <span className="text-destructive text-sm">
                                                {editErrors.password}
                                            </span>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="edit-password-confirmation">
                                            Confirm New Password
                                        </Label>
                                        <Input
                                            id="edit-password-confirmation"
                                            type="password"
                                            value={
                                                editData.password_confirmation
                                            }
                                            onChange={(e) =>
                                                setEditData(
                                                    'password_confirmation',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Confirm new password"
                                        />
                                    </div>
                                </div>
                                <DialogFooter>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() =>
                                            setIsEditDialogOpen(false)
                                        }
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={editProcessing}
                                    >
                                        {editProcessing
                                            ? 'Updating...'
                                            : 'Update User'}
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>
            </AdminLayout>
        </>
    );
}
