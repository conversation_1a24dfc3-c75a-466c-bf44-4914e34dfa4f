import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router, useForm } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Edit,
    FolderOpen,
    Plus,
    Search,
    Trash2,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function FaqGroupsIndex({
    faqGroups,
    filters,
    title,
    description,
    ...props
}) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingFaqGroup, setEditingFaqGroup] = useState(null);
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const {
        data: addData,
        setData: setAddData,
        post,
        processing: addProcessing,
        errors: addErrors,
        reset: resetAdd,
    } = useForm({
        name: '',
    });

    const {
        data: editData,
        setData: setEditData,
        put,
        processing: editProcessing,
        errors: editErrors,
        reset: resetEdit,
    } = useForm({
        name: '',
    });

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.faq-groups.index'),
            { search: searchTerm },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearSearch = () => {
        setSearchTerm('');
        router.get(
            route('admin.faq-groups.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleAddFaqGroup = (e) => {
        e.preventDefault();
        post(route('admin.faq-groups.store'), {
            onSuccess: () => {
                toast.success('FAQ Group created successfully');
                setIsAddDialogOpen(false);
                resetAdd();
            },
            onError: (errors) => {
                console.error('Add FAQ group errors:', errors);
                toast.error('Failed to create FAQ group');
            },
        });
    };

    const handleEditFaqGroup = (faqGroup) => {
        setEditingFaqGroup(faqGroup);
        setEditData({
            name: faqGroup.name,
        });
        setIsEditDialogOpen(true);
    };

    const handleUpdateFaqGroup = (e) => {
        e.preventDefault();
        put(route('admin.faq-groups.update', editingFaqGroup.id), {
            onSuccess: () => {
                toast.success('FAQ Group updated successfully');
                setIsEditDialogOpen(false);
                setEditingFaqGroup(null);
                resetEdit();
            },
            onError: (errors) => {
                console.error('Update FAQ group errors:', errors);
                toast.error('Failed to update FAQ group');
            },
        });
    };

    const handleDeleteFaqGroup = (faqGroup) => {
        router.delete(route('admin.faq-groups.destroy', faqGroup.id), {
            onSuccess: () => {
                toast.success('FAQ Group deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete FAQ group errors:', errors);
                if (errors?.error) {
                    toast.error(errors.error);
                } else {
                    toast.error('Failed to delete FAQ group');
                }
            },
        });
    };

    const closeAddDialog = () => {
        setIsAddDialogOpen(false);
        resetAdd();
    };

    const closeEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingFaqGroup(null);
        resetEdit();
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Dialog
                    open={isAddDialogOpen}
                    onOpenChange={setIsAddDialogOpen}
                >
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add FAQ Group
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                        <form onSubmit={handleAddFaqGroup}>
                            <DialogHeader>
                                <DialogTitle>Add New FAQ Group</DialogTitle>
                                <DialogDescription>
                                    Create a new FAQ group to organize
                                    questions.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="add-name">Group Name</Label>
                                    <Input
                                        id="add-name"
                                        value={addData.name}
                                        onChange={(e) =>
                                            setAddData('name', e.target.value)
                                        }
                                        placeholder="Enter FAQ group name"
                                        className={
                                            addErrors.name
                                                ? 'border-red-500'
                                                : ''
                                        }
                                    />
                                    {addErrors.name && (
                                        <p className="text-sm text-red-500">
                                            {addErrors.name}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={closeAddDialog}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={addProcessing}>
                                    {addProcessing
                                        ? 'Creating...'
                                        : 'Create Group'}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            }
        >
            <Head title={title} />

            <div className="py-8">
                <Card>
                    <CardContent className="p-6">
                        {/* Search and Filters */}
                        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <form
                                onSubmit={handleSearch}
                                className="flex gap-2"
                            >
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search FAQ groups..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                        className="w-64 pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                                {filters.search && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleClearSearch}
                                    >
                                        Clear
                                    </Button>
                                )}
                            </form>
                        </div>

                        {/* FAQ Groups Table */}
                        <div className="overflow-hidden rounded-lg border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        <TableHead>FAQs Count</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {faqGroups.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={4}
                                                className="text-muted-foreground py-8 text-center"
                                            >
                                                No FAQ groups found.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        faqGroups.data.map((faqGroup) => (
                                            <TableRow key={faqGroup.id}>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center gap-2">
                                                        <FolderOpen className="h-4 w-4 text-blue-500" />
                                                        {faqGroup.name}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="secondary">
                                                        {faqGroup.faqs_count ||
                                                            0}{' '}
                                                        FAQs
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(
                                                        faqGroup.created_at,
                                                    ).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                handleEditFaqGroup(
                                                                    faqGroup,
                                                                )
                                                            }
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        FAQ
                                                                        Group
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete "
                                                                        {
                                                                            faqGroup.name
                                                                        }
                                                                        "? This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeleteFaqGroup(
                                                                                faqGroup,
                                                                            )
                                                                        }
                                                                        className="bg-red-600 hover:bg-red-700"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {faqGroups.total > faqGroups.per_page && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {faqGroups.from} to {faqGroups.to}{' '}
                                    of {faqGroups.total} results
                                </div>
                                <div className="flex gap-2">
                                    {faqGroups.prev_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    faqGroups.prev_page_url,
                                                )
                                            }
                                        >
                                            <ChevronLeft className="mr-1 h-4 w-4" />
                                            Previous
                                        </Button>
                                    )}
                                    {faqGroups.next_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    faqGroups.next_page_url,
                                                )
                                            }
                                        >
                                            Next
                                            <ChevronRight className="ml-1 h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="max-w-md">
                    <form onSubmit={handleUpdateFaqGroup}>
                        <DialogHeader>
                            <DialogTitle>Edit FAQ Group</DialogTitle>
                            <DialogDescription>
                                Update the FAQ group information.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="edit-name">Group Name</Label>
                                <Input
                                    id="edit-name"
                                    value={editData.name}
                                    onChange={(e) =>
                                        setEditData('name', e.target.value)
                                    }
                                    placeholder="Enter FAQ group name"
                                    className={
                                        editErrors.name ? 'border-red-500' : ''
                                    }
                                />
                                {editErrors.name && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.name}
                                    </p>
                                )}
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={closeEditDialog}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={editProcessing}>
                                {editProcessing
                                    ? 'Updating...'
                                    : 'Update Group'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
