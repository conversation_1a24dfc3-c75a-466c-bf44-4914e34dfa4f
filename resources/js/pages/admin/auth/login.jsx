import { LoginForm } from '@/admin/auth/forms/login-form';
import AuthLayout from '@admin/layouts/AuthLayout';
import { useLocation } from '@hooks/navigation';
import { Head, usePage } from '@inertiajs/react';

export default function Login() {
    const location = useLocation();
    const { props } = usePage();

    return (
        <>
            <Head title="Login" />
            <AuthLayout>
                <LoginForm />
            </AuthLayout>
        </>
    );
}
