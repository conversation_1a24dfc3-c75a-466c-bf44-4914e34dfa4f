import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Button } from '@admin/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@admin/components/ui/card';
import { Input } from '@admin/components/ui/input';
import { Label } from '@admin/components/ui/label';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@admin/components/ui/tabs';
import { Head, useForm } from '@inertiajs/react';
import { toast } from 'sonner';
import { User, Lock, Save } from 'lucide-react';
import { useEffect } from 'react';

export default function EditProfile({ user, status }) {
    const title = 'My Profile';

    // Profile form
    const profileForm = useForm({
        name: user.name || '',
        email: user.email || '',
        type: user.type || '',
    });

    // Password form
    const passwordForm = useForm({
        current_password: '',
        password: '',
        password_confirmation: '',
    });

    // Handle profile update
    const handleProfileSubmit = (e) => {
        e.preventDefault();
        profileForm.patch(route('admin.profile.update'), {
            onSuccess: () => {
                toast.success('Profile updated successfully!');
            },
            onError: (errors) => {
                Object.values(errors).forEach(error => {
                    toast.error(error);
                });
            },
        });
    };

    // Handle password update
    const handlePasswordSubmit = (e) => {
        e.preventDefault();
        passwordForm.put(route('admin.profile.password.update'), {
            onSuccess: () => {
                toast.success('Password updated successfully!');
                passwordForm.reset();
            },
            onError: (errors) => {
                Object.values(errors).forEach(error => {
                    toast.error(error);
                });
            },
        });
    };

    // Show success message from backend
    useEffect(() => {
        if (status) {
            toast.success(status);
        }
    }, [status]);

    return (
        <>
            <Head title={title} />
            <AdminLayout title={title} description="Manage your profile information and security settings">
                <div className="max-w-4xl mx-auto">
                    <Tabs defaultValue="profile" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="profile" className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                Profile Information
                            </TabsTrigger>
                            <TabsTrigger value="password" className="flex items-center gap-2">
                                <Lock className="h-4 w-4" />
                                Change Password
                            </TabsTrigger>
                        </TabsList>

                        {/* Profile Information Tab */}
                        <TabsContent value="profile">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Profile Information</CardTitle>
                                    <CardDescription>
                                        Update your account's profile information and email address.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleProfileSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="name">Full Name</Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={profileForm.data.name}
                                                    onChange={(e) => profileForm.setData('name', e.target.value)}
                                                    className={profileForm.errors.name ? 'border-red-500' : ''}
                                                    required
                                                />
                                                {profileForm.errors.name && (
                                                    <p className="text-sm text-red-600">{profileForm.errors.name}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="email">Email Address</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={profileForm.data.email}
                                                    onChange={(e) => profileForm.setData('email', e.target.value)}
                                                    className={profileForm.errors.email ? 'border-red-500' : ''}
                                                    required
                                                />
                                                {profileForm.errors.email && (
                                                    <p className="text-sm text-red-600">{profileForm.errors.email}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="type">User Type</Label>
                                                <Input
                                                    id="type"
                                                    type="text"
                                                    value={profileForm.data.type}
                                                    onChange={(e) => profileForm.setData('type', e.target.value)}
                                                    className={profileForm.errors.type ? 'border-red-500' : ''}
                                                    placeholder="e.g., admin, manager"
                                                />
                                                {profileForm.errors.type && (
                                                    <p className="text-sm text-red-600">{profileForm.errors.type}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex justify-end">
                                            <Button
                                                type="submit"
                                                disabled={profileForm.processing}
                                                className="flex items-center gap-2"
                                            >
                                                <Save className="h-4 w-4" />
                                                {profileForm.processing ? 'Updating...' : 'Update Profile'}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Change Password Tab */}
                        <TabsContent value="password">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Change Password</CardTitle>
                                    <CardDescription>
                                        Ensure your account is using a long, random password to stay secure.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handlePasswordSubmit} className="space-y-6">
                                        <div className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="current_password">Current Password</Label>
                                                <Input
                                                    id="current_password"
                                                    type="password"
                                                    value={passwordForm.data.current_password}
                                                    onChange={(e) => passwordForm.setData('current_password', e.target.value)}
                                                    className={passwordForm.errors.current_password ? 'border-red-500' : ''}
                                                    required
                                                />
                                                {passwordForm.errors.current_password && (
                                                    <p className="text-sm text-red-600">{passwordForm.errors.current_password}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="password">New Password</Label>
                                                <Input
                                                    id="password"
                                                    type="password"
                                                    value={passwordForm.data.password}
                                                    onChange={(e) => passwordForm.setData('password', e.target.value)}
                                                    className={passwordForm.errors.password ? 'border-red-500' : ''}
                                                    required
                                                />
                                                {passwordForm.errors.password && (
                                                    <p className="text-sm text-red-600">{passwordForm.errors.password}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="password_confirmation">Confirm New Password</Label>
                                                <Input
                                                    id="password_confirmation"
                                                    type="password"
                                                    value={passwordForm.data.password_confirmation}
                                                    onChange={(e) => passwordForm.setData('password_confirmation', e.target.value)}
                                                    className={passwordForm.errors.password_confirmation ? 'border-red-500' : ''}
                                                    required
                                                />
                                                {passwordForm.errors.password_confirmation && (
                                                    <p className="text-sm text-red-600">{passwordForm.errors.password_confirmation}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex justify-end">
                                            <Button
                                                type="submit"
                                                disabled={passwordForm.processing}
                                                className="flex items-center gap-2"
                                            >
                                                <Lock className="h-4 w-4" />
                                                {passwordForm.processing ? 'Updating...' : 'Update Password'}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </AdminLayout>
        </>
    );
}