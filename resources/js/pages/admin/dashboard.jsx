import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@admin/components/ui/card.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head } from '@inertiajs/react';
import { CalendarCheck, ClipboardCheck, DollarSign, TrendingUp } from 'lucide-react';

export default function DashboardPage({ analytics, ...props }) {
    const title = 'Dashboard';

    console.log(analytics);

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount || 0);
    };

    return (
        <>
            <Head title={title} />
            <AdminLayout title={title}>
                <div className="space-y-6">
                    {/* Analytics Cards */}
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                        {/* Revenue Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    Total Revenue
                                </CardTitle>
                                <DollarSign className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {formatCurrency(
                                        analytics?.revenue.total || 0,
                                    )}
                                </div>
                                <p className="text-muted-foreground text-xs">
                                    From confirmed bookings
                                </p>
                            </CardContent>
                        </Card>

                        {/* Confirmed Bookings Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    Confirmed Bookings
                                </CardTitle>
                                <CalendarCheck className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {analytics?.confirmedBookings || 0}
                                </div>
                                <p className="text-muted-foreground text-xs">
                                    Total confirmed
                                </p>
                            </CardContent>
                        </Card>

                        {/* Pending Bookings Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    Pending Bookings
                                </CardTitle>
                                <CalendarCheck className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {analytics?.pendingBookings || 0}
                                </div>
                                <p className="text-muted-foreground text-xs">
                                    Awaiting confirmation
                                </p>
                            </CardContent>
                        </Card>

                        {/* Customized Trips Card */}
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    Customized Trips
                                </CardTitle>
                                <ClipboardCheck className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {(analytics?.confirmedCustomizedTrips ||
                                        0) +
                                        (analytics?.pendingCustomizedTrips ||
                                            0)}
                                </div>
                                <p className="text-muted-foreground text-xs">
                                    {analytics?.confirmedCustomizedTrips || 0}{' '}
                                    confirmed,{' '}
                                    {analytics?.pendingCustomizedTrips || 0}{' '}
                                    pending
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Monthly Revenue Chart */}
                    {analytics?.monthlyRevenue &&
                        analytics.monthlyRevenue.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <TrendingUp className="mr-2 h-5 w-5" />
                                        Monthly Revenue Trend (Last 6 Months)
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {analytics.monthlyRevenue.map(
                                            (month, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center justify-between"
                                                >
                                                    <span className="text-sm font-medium">
                                                        {month.month}
                                                    </span>
                                                    <span className="text-sm font-bold">
                                                        {formatCurrency(
                                                            month.revenue,
                                                        )}
                                                    </span>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                </div>
            </AdminLayout>
        </>
    );
}
