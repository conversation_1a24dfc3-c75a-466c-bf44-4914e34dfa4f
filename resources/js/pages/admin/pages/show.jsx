import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Lock, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function ShowPage({ page, title, description }) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = () => {
        setIsDeleting(true);
        router.delete(route('admin.pages.destroy', page.id), {
            onSuccess: () => {
                toast.success('Page deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete page');
                setIsDeleting(false);
            },
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.pages.index')}>
                    <Button variant="outline">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Pages
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <CardTitle className="text-2xl">
                                            {page.title}
                                        </CardTitle>
                                        {page.is_default && (
                                            <Badge
                                                variant="secondary"
                                                className="flex items-center space-x-1"
                                            >
                                                <Lock className="h-3 w-3" />
                                                <span>Protected Page</span>
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {page.image && (
                                    <div className="mb-6">
                                        <img
                                            src={`/storage/${page.image}`}
                                            alt={page.title}
                                            className="h-64 w-full rounded-lg border object-cover"
                                        />
                                    </div>
                                )}

                                <div className="prose max-w-none">
                                    <div className="whitespace-pre-wrap leading-relaxed text-gray-700">
                                        {page.content}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Page Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Status
                                    </h4>
                                    <Badge
                                        className={getStatusColor(page.status)}
                                    >
                                        {page.status.charAt(0).toUpperCase() +
                                            page.status.slice(1)}
                                    </Badge>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Created
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(page.created_at)}
                                    </p>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Last Updated
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(page.updated_at)}
                                    </p>
                                </div>

                                {page.published_at && (
                                    <div>
                                        <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                            Published
                                        </h4>
                                        <p className="text-sm">
                                            {formatDate(page.published_at)}
                                        </p>
                                    </div>
                                )}

                                <div className="mt-4 flex items-center space-x-2">
                                    <Link
                                        href={route(
                                            'admin.pages.edit',
                                            page.id,
                                        )}
                                    >
                                        <Button>
                                            <Edit className="mr-2 h-4 w-4" />
                                            Edit
                                        </Button>
                                    </Link>
                                    {!page.is_default && (
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    className="text-red-500"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>
                                                        Are you absolutely sure?
                                                    </AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be
                                                        undone. This will
                                                        permanently delete the
                                                        page and remove all
                                                        associated data.
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>
                                                        Cancel
                                                    </AlertDialogCancel>
                                                    <AlertDialogAction
                                                        onClick={handleDelete}
                                                        disabled={isDeleting}
                                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                    >
                                                        {isDeleting
                                                            ? 'Deleting...'
                                                            : 'Delete'}
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <Link
                                    href={route('admin.pages.edit', page.id)}
                                    className="block"
                                >
                                    <Button
                                        variant="outline"
                                        className="w-full justify-start"
                                    >
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Page
                                    </Button>
                                </Link>
                                <Link
                                    href={route('admin.pages.create')}
                                    className="block"
                                >
                                    <Button
                                        variant="outline"
                                        className="w-full justify-start"
                                    >
                                        Create New Page
                                    </Button>
                                </Link>
                                <Link
                                    href={route('admin.pages.index')}
                                    className="block"
                                >
                                    <Button
                                        variant="ghost"
                                        className="w-full justify-start"
                                    >
                                        Back to Pages
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
