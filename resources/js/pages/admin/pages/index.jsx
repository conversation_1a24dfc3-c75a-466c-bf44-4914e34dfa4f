import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Edit,
    Eye,
    FileText,
    Lock,
    Plus,
    Search,
    Trash2,
    X,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function PagesIndex({
    pages,
    destinations,
    filters,
    title,
    description,
    ...props
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [destinationFilter, setDestinationFilter] = useState(
        filters.destination || 'all',
    );
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [showFilters, setShowFilters] = useState(false);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.pages.index'),
            {
                search: searchTerm,
                destination:
                    destinationFilter === 'all' ? '' : destinationFilter,
                status: statusFilter === 'all' ? '' : statusFilter,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearFilters = () => {
        setSearchTerm('');
        setDestinationFilter('all');
        setStatusFilter('all');
        router.get(
            route('admin.pages.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDeletePage = (pageId) => {
        router.delete(route('admin.pages.destroy', pageId), {
            onSuccess: () => {
                toast.success('Page deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete page');
            },
        });
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            draft: { variant: 'secondary', label: 'Draft' },
            published: { variant: 'default', label: 'Published' },
            archived: { variant: 'outline', label: 'Archived' },
        };

        const config = statusConfig[status] || statusConfig.draft;
        return (
            <Badge variant={config.variant} className="capitalize">
                {config.label}
            </Badge>
        );
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.pages.create')}>
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Page
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Search and Filters */}
                <Card>
                    <CardContent className="p-6">
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="flex items-end space-x-4">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
                                        <Input
                                            placeholder="Search pages..."
                                            value={searchTerm}
                                            onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                            }
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div className="">
                                    <Select
                                        value={statusFilter}
                                        onValueChange={setStatusFilter}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                All statuses
                                            </SelectItem>
                                            <SelectItem value="draft">
                                                Draft
                                            </SelectItem>
                                            <SelectItem value="published">
                                                Published
                                            </SelectItem>
                                            <SelectItem value="archived">
                                                Archived
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <Button type="submit">Search</Button>
                                {(filters.search ||
                                    filters.destination ||
                                    filters.status) && (
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        onClick={clearFilters}
                                    >
                                        <X className="mr-2 h-4 w-4" />
                                        Clear
                                    </Button>
                                )}
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Pages Table */}
                <Card>
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Image</TableHead>
                                    <TableHead>Title</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead className="text-right">
                                        Actions
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {pages.data.length === 0 ? (
                                    <TableRow>
                                        <TableCell
                                            colSpan={6}
                                            className="py-8 text-center"
                                        >
                                            <div className="flex flex-col items-center space-y-2">
                                                <FileText className="text-muted-foreground h-8 w-8" />
                                                <p className="text-muted-foreground">
                                                    No pages found
                                                </p>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    pages.data.map((page) => (
                                        <TableRow key={page.id}>
                                            <TableCell>
                                                {page.image ? (
                                                    <img
                                                        src={`/storage/${page.image}`}
                                                        alt={page.title}
                                                        className="h-12 w-12 rounded object-cover"
                                                    />
                                                ) : (
                                                    <div className="bg-muted flex h-12 w-12 items-center justify-center rounded">
                                                        <FileText className="text-muted-foreground h-6 w-6" />
                                                    </div>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="flex items-center space-x-2">
                                                        <p className="font-medium">
                                                            {page.title}
                                                        </p>
                                                        {page.is_default && (
                                                            <Badge
                                                                variant="secondary"
                                                                className="flex items-center space-x-1 text-xs"
                                                            >
                                                                <Lock className="h-3 w-3" />
                                                                <span>
                                                                    Protected
                                                                </span>
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <p className="text-muted-foreground max-w-xs truncate text-sm">
                                                        {page.content
                                                            .replace(
                                                                /<[^>]*>/g,
                                                                '',
                                                            )
                                                            .substring(0, 100)}
                                                        ...
                                                    </p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(page.status)}
                                            </TableCell>
                                            <TableCell>
                                                {new Date(
                                                    page.created_at,
                                                ).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <Link
                                                        href={route(
                                                            'admin.pages.show',
                                                            page.id,
                                                        )}
                                                    >
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link
                                                        href={route(
                                                            'admin.pages.edit',
                                                            page.id,
                                                        )}
                                                    >
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    {!page.is_default && (
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        Page
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete "
                                                                        {
                                                                            page.title
                                                                        }
                                                                        "? This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeletePage(
                                                                                page.id,
                                                                            )
                                                                        }
                                                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {pages.last_page > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-muted-foreground text-sm">
                            Showing {pages.from} to {pages.to} of {pages.total}{' '}
                            results
                        </div>
                        <div className="flex items-center space-x-2">
                            {pages.prev_page_url && (
                                <Link href={pages.prev_page_url}>
                                    <Button variant="outline" size="sm">
                                        <ChevronLeft className="h-4 w-4" />
                                        Previous
                                    </Button>
                                </Link>
                            )}
                            {pages.next_page_url && (
                                <Link href={pages.next_page_url}>
                                    <Button variant="outline" size="sm">
                                        Next
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </AdminLayout>
    );
}
