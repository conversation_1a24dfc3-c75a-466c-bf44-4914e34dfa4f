import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { RichTextEditor } from '@admin/components/ui/rich-text-editor.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function EditPage({ page, title, description }) {
    const { data, setData, post, processing, errors } = useForm({
        title: page.title || '',
        slug: page.slug || '',
        content: page.content || '',
        image: null,
        status: page.status || 'draft',
        _method: 'PUT',
    });

    const [imagePreview, setImagePreview] = useState(
        page.image ? `/storage/${page.image}` : null,
    );

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onload = (e) => setImagePreview(e.target.result);
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.pages.update', page.id), {
            onSuccess: () => {
                toast.success('Page updated successfully');
            },
            onError: () => {
                toast.error('Failed to update page');
            },
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.pages.index')}>
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Pages
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Page Details</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) =>
                                                setData('title', e.target.value)
                                            }
                                            placeholder="Enter page title"
                                            className={
                                                errors.title
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.title && (
                                            <p className="text-destructive text-sm">
                                                {errors.title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="slug">Slug *</Label>
                                        <Input
                                            id="slug"
                                            value={data.slug}
                                            onChange={(e) =>
                                                setData('slug', e.target.value)
                                            }
                                            placeholder="Enter page slug (e.g., about-us)"
                                            className={
                                                errors.slug
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.slug && (
                                            <p className="text-destructive text-sm">
                                                {errors.slug}
                                            </p>
                                        )}
                                        <p className="text-sm text-muted-foreground">
                                            URL-friendly version of the title. Use lowercase letters, numbers, and hyphens only.
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">
                                            Content *
                                        </Label>
                                        <RichTextEditor
                                            value={data.content}
                                            onChange={(content) =>
                                                setData('content', content)
                                            }
                                            placeholder="Enter page content"
                                            className={
                                                errors.content
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.content && (
                                            <p className="text-destructive text-sm">
                                                {errors.content}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Image Upload */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Featured Image</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="image">Image</Label>
                                        <Input
                                            id="image"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleImageChange}
                                            className={
                                                errors.image
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.image && (
                                            <p className="text-destructive text-sm">
                                                {errors.image}
                                            </p>
                                        )}
                                    </div>

                                    {imagePreview && (
                                        <div className="mt-4">
                                            <img
                                                src={imagePreview}
                                                alt="Preview"
                                                className="h-48 max-w-full rounded-lg border object-cover"
                                            />
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Publish Settings</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status *</Label>
                                        <Select
                                            value={data.status}
                                            onValueChange={(value) =>
                                                setData('status', value)
                                            }
                                        >
                                            <SelectTrigger
                                                className={
                                                    errors.status
                                                        ? 'border-destructive'
                                                        : ''
                                                }
                                            >
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="draft">
                                                    Draft
                                                </SelectItem>
                                                <SelectItem value="published">
                                                    Published
                                                </SelectItem>
                                                <SelectItem value="archived">
                                                    Archived
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && (
                                            <p className="text-destructive text-sm">
                                                {errors.status}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardContent className="pt-6">
                                    <div className="flex flex-col space-y-2">
                                        <Button
                                            type="submit"
                                            disabled={processing}
                                        >
                                            <Save className="mr-2 h-4 w-4" />
                                            {processing
                                                ? 'Updating...'
                                                : 'Update Page'}
                                        </Button>
                                        <Link
                                            href={route(
                                                'admin.pages.show',
                                                page.id,
                                            )}
                                        >
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                            >
                                                View Page
                                            </Button>
                                        </Link>
                                        <Link href={route('admin.pages.index')}>
                                            <Button
                                                variant="ghost"
                                                className="w-full"
                                            >
                                                Cancel
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
