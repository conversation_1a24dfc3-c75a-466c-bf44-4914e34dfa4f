import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';

import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@admin/components/ui/dialog.jsx';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@admin/components/ui/dropdown-menu.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router } from '@inertiajs/react';
import {
    CalendarCheck,
    ChevronDown,
    ChevronLeft,
    ChevronRight,
    DollarSign,
    Eye,
    Trash2,
    X,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function BookingsIndex({
    bookings,
    analytics,
    filters,
    statuses,
    title,
    description,
    ...props
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');
    const [viewingBooking, setViewingBooking] = useState(null);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [updatingStatus, setUpdatingStatus] = useState(null);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.bookings.index'),
            {
                search: searchTerm,
                status: statusFilter === 'all' ? '' : statusFilter,
                date_from: dateFrom,
                date_to: dateTo,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearFilters = () => {
        setSearchTerm('');
        setStatusFilter('all');
        setDateFrom('');
        setDateTo('');
        router.get(route('admin.bookings.index'));
    };

    const viewBooking = async (booking) => {
        try {
            const response = await fetch(
                route('admin.bookings.show', booking.id),
            );
            const data = await response.json();
            setViewingBooking(data.booking);
            setIsViewOpen(true);
        } catch (error) {
            toast.error('Failed to load booking details');
        }
    };

    const deleteBooking = (booking) => {
        router.delete(route('admin.bookings.destroy', booking.id), {
            onSuccess: () => {
                toast.success('Booking deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete booking');
            },
        });
    };

    const updateBookingStatus = (booking, newStatus) => {
        setUpdatingStatus(booking.id);
        router.put(
            route('admin.bookings.updateStatus', booking.id),
            { status: newStatus },
            {
                onSuccess: () => {
                    toast.success('Booking status updated successfully');
                    setUpdatingStatus(null);
                },
                onError: () => {
                    toast.error('Failed to update booking status');
                    setUpdatingStatus(null);
                },
            },
        );
    };



    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AdminLayout
            title={title || 'Bookings'}
            description={description || 'Manage all travel package bookings'}
        >
            <Head title={title || 'Bookings'} />

            <div className="space-y-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CalendarCheck className="h-6 w-6 text-blue-600" />
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Total Bookings
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {analytics?.total_bookings || 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CalendarCheck className="h-6 w-6 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Confirmed
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {analytics?.confirmed_bookings || 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CalendarCheck className="h-6 w-6 text-yellow-600" />
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Pending
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {analytics?.pending_bookings || 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <DollarSign className="h-6 w-6 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Total Revenue
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {formatCurrency(analytics?.total_revenue || 0)}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                                <div>
                                    <Label htmlFor="search">Search</Label>
                                    <Input
                                        id="search"
                                        placeholder="Search by name, email, phone..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={statusFilter}
                                        onValueChange={setStatusFilter}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                All statuses
                                            </SelectItem>
                                            {Object.entries(statuses).map(
                                                ([key, label]) => (
                                                    <SelectItem
                                                        key={key}
                                                        value={key}
                                                    >
                                                        {label}
                                                    </SelectItem>
                                                ),
                                            )}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="date_from">Date From</Label>
                                    <Input
                                        id="date_from"
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) =>
                                            setDateFrom(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="date_to">Date To</Label>
                                    <Input
                                        id="date_to"
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) =>
                                            setDateTo(e.target.value)
                                        }
                                    />
                                </div>
                                <div className="flex items-end gap-2">
                                    <Button type="submit" className="flex-1">
                                        Search
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={clearFilters}
                                    >
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Bookings Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CalendarCheck className="h-5 w-5" />
                            Bookings ({bookings.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {bookings.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <CalendarCheck className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                                <p className="text-muted-foreground">
                                    No bookings found
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Customer</TableHead>
                                                <TableHead>Package</TableHead>
                                                <TableHead>
                                                    Booking Date
                                                </TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Contact</TableHead>
                                                <TableHead>Country</TableHead>
                                                <TableHead className="text-right">
                                                    Actions
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {bookings.data.map((booking) => (
                                                <TableRow key={booking.id}>
                                                    <TableCell>
                                                        <div>
                                                            <p className="font-medium">
                                                                {
                                                                    booking.first_name
                                                                }{' '}
                                                                {
                                                                    booking.last_name
                                                                }
                                                            </p>
                                                            <p className="text-muted-foreground text-sm">
                                                                {booking.email}
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <p className="font-medium">
                                                            {booking.package
                                                                ?.name || 'N/A'}
                                                        </p>
                                                    </TableCell>
                                                    <TableCell>
                                                        {formatDate(
                                                            booking.booking_date,
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <DropdownMenu>
                                                            <DropdownMenuTrigger asChild>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className={`h-8 w-auto capitalize ${
                                                                        booking.status === 'confirmed'
                                                                            ? 'text-green-700 border-green-300 bg-green-50'
                                                                            : booking.status === 'cancelled'
                                                                            ? 'text-red-700 border-red-300 bg-red-50'
                                                                            : 'text-yellow-700 border-yellow-300 bg-yellow-50'
                                                                    }`}
                                                                    disabled={updatingStatus === booking.id}
                                                                >
                                                                    {booking.status || 'pending'}
                                                                    <ChevronDown className="ml-1 h-3 w-3" />
                                                                </Button>
                                                            </DropdownMenuTrigger>
                                                            <DropdownMenuContent>
                                                                <DropdownMenuItem
                                                                    onClick={() =>
                                                                        updateBookingStatus(
                                                                            booking,
                                                                            'pending'
                                                                        )
                                                                    }
                                                                >
                                                                    Pending
                                                                </DropdownMenuItem>
                                                                <DropdownMenuItem
                                                                    onClick={() =>
                                                                        updateBookingStatus(
                                                                            booking,
                                                                            'confirmed'
                                                                        )
                                                                    }
                                                                >
                                                                    Confirmed
                                                                </DropdownMenuItem>
                                                                <DropdownMenuItem
                                                                    onClick={() =>
                                                                        updateBookingStatus(
                                                                            booking,
                                                                            'cancelled'
                                                                        )
                                                                    }
                                                                >
                                                                    Cancelled
                                                                </DropdownMenuItem>
                                                            </DropdownMenuContent>
                                                        </DropdownMenu>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="text-sm">
                                                            <p>
                                                                {booking.phone}
                                                            </p>
                                                            {booking.whatsapp && (
                                                                <p className="text-muted-foreground">
                                                                    WhatsApp:{' '}
                                                                    {
                                                                        booking.whatsapp
                                                                    }
                                                                </p>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        {booking.country
                                                            ?.name || 'N/A'}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() =>
                                                                    viewBooking(
                                                                        booking,
                                                                    )
                                                                }
                                                            >
                                                                <Eye className="h-4 w-4" />
                                                            </Button>
                                                            <AlertDialog>
                                                                <AlertDialogTrigger
                                                                    asChild
                                                                >
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        className="text-red-600 hover:text-red-700"
                                                                    >
                                                                        <Trash2 className="h-4 w-4" />
                                                                    </Button>
                                                                </AlertDialogTrigger>
                                                                <AlertDialogContent>
                                                                    <AlertDialogHeader>
                                                                        <AlertDialogTitle>
                                                                            Delete
                                                                            Booking
                                                                        </AlertDialogTitle>
                                                                        <AlertDialogDescription>
                                                                            Are
                                                                            you
                                                                            sure
                                                                            you
                                                                            want
                                                                            to
                                                                            delete
                                                                            this
                                                                            booking
                                                                            for{' '}
                                                                            <strong>
                                                                                {
                                                                                    booking.first_name
                                                                                }{' '}
                                                                                {
                                                                                    booking.last_name
                                                                                }
                                                                            </strong>
                                                                            ?
                                                                            This
                                                                            action
                                                                            cannot
                                                                            be
                                                                            undone.
                                                                        </AlertDialogDescription>
                                                                    </AlertDialogHeader>
                                                                    <AlertDialogFooter>
                                                                        <AlertDialogCancel>
                                                                            Cancel
                                                                        </AlertDialogCancel>
                                                                        <AlertDialogAction
                                                                            onClick={() =>
                                                                                deleteBooking(
                                                                                    booking,
                                                                                )
                                                                            }
                                                                            className="bg-red-600 hover:bg-red-700"
                                                                        >
                                                                            Delete
                                                                        </AlertDialogAction>
                                                                    </AlertDialogFooter>
                                                                </AlertDialogContent>
                                                            </AlertDialog>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {bookings.last_page > 1 && (
                                    <div className="mt-4 flex items-center justify-between">
                                        <p className="text-muted-foreground text-sm">
                                            Showing {bookings.from}-
                                            {bookings.to} of {bookings.total}{' '}
                                            results
                                        </p>
                                        <div className="flex items-center space-x-2">
                                            {bookings.prev_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            bookings.prev_page_url,
                                                        )
                                                    }
                                                >
                                                    <ChevronLeft className="h-4 w-4" />
                                                    Previous
                                                </Button>
                                            )}
                                            {bookings.next_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            bookings.next_page_url,
                                                        )
                                                    }
                                                >
                                                    Next
                                                    <ChevronRight className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* View Booking Modal */}
            <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Booking Details</DialogTitle>
                        <DialogDescription>
                            Complete information about this booking
                        </DialogDescription>
                    </DialogHeader>
                    {viewingBooking && (
                        <div className="space-y-6">
                            {/* Customer Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Customer Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Full Name</Label>
                                        <p className="text-sm">
                                            {viewingBooking.first_name}{' '}
                                            {viewingBooking.last_name}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Email</Label>
                                        <p className="text-sm">
                                            {viewingBooking.email}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Phone</Label>
                                        <p className="text-sm">
                                            {viewingBooking.phone}
                                        </p>
                                    </div>
                                    {viewingBooking.whatsapp && (
                                        <div>
                                            <Label>WhatsApp</Label>
                                            <p className="text-sm">
                                                {viewingBooking.whatsapp}
                                            </p>
                                        </div>
                                    )}
                                    <div>
                                        <Label>Country</Label>
                                        <p className="text-sm">
                                            {viewingBooking.country?.name ||
                                                'N/A'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Booking Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Booking Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Package</Label>
                                        <p className="text-sm">
                                            {viewingBooking.package?.name ||
                                                'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Booking Date</Label>
                                        <p className="text-sm">
                                            {formatDate(
                                                viewingBooking.booking_date,
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Status</Label>
                                        <Badge
                                            className={
                                                viewingBooking.status === 'confirmed'
                                                    ? 'bg-green-100 text-green-800'
                                                    : viewingBooking.status === 'cancelled'
                                                    ? 'bg-red-100 text-red-800'
                                                    : 'bg-yellow-100 text-yellow-800'
                                            }
                                        >
                                            {statuses[viewingBooking.status]}
                                        </Badge>
                                    </div>
                                    <div>
                                        <Label>Created</Label>
                                        <p className="text-sm">
                                            {formatDate(
                                                viewingBooking.created_at,
                                            )}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Extra Notes */}
                            {viewingBooking.extra_notes && (
                                <div>
                                    <h3 className="mb-3 text-lg font-semibold">
                                        Extra Notes
                                    </h3>
                                    <p className="rounded-md bg-gray-50 p-3 text-sm">
                                        {viewingBooking.extra_notes}
                                    </p>
                                </div>
                            )}

                            {/* Payments */}
                            {viewingBooking.payments &&
                                viewingBooking.payments.length > 0 && (
                                    <div>
                                        <h3 className="mb-3 text-lg font-semibold">
                                            Payments
                                        </h3>
                                        <div className="space-y-2">
                                            {viewingBooking.payments.map(
                                                (payment, index) => (
                                                    <div
                                                        key={index}
                                                        className="flex items-center justify-between rounded-md bg-gray-50 p-3"
                                                    >
                                                        <div>
                                                            <p className="font-medium">
                                                                {payment.type}
                                                            </p>
                                                            <p className="text-muted-foreground text-sm">
                                                                {formatDate(
                                                                    payment.created_at,
                                                                )}
                                                            </p>
                                                        </div>
                                                        <p className="font-medium">
                                                            {formatCurrency(
                                                                payment.amount,
                                                            )}
                                                        </p>
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                )}

                            {/* Commissions */}
                            {viewingBooking.commissions &&
                                viewingBooking.commissions.length > 0 && (
                                    <div>
                                        <h3 className="mb-3 text-lg font-semibold">
                                            Commissions
                                        </h3>
                                        <div className="space-y-2">
                                            {viewingBooking.commissions.map(
                                                (commission, index) => (
                                                    <div
                                                        key={index}
                                                        className="flex items-center justify-between rounded-md bg-gray-50 p-3"
                                                    >
                                                        <div>
                                                            <p className="font-medium">
                                                                Commission
                                                            </p>
                                                            <p className="text-muted-foreground text-sm">
                                                                {
                                                                    commission.percentage
                                                                }
                                                                % of booking
                                                            </p>
                                                        </div>
                                                        <p className="font-medium">
                                                            {formatCurrency(
                                                                commission.amount,
                                                            )}
                                                        </p>
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                )}
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
