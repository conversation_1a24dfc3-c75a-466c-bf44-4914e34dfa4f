import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    DollarSign,
    Edit3,
    Eye,
    Search,
    Trash2,
    Users,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function CommissionsIndex({
    commissions,
    partners,
    filters,
    statuses,
    title,
    description,
    ...props
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [partnerFilter, setPartnerFilter] = useState(
        filters.partner_id || 'all',
    );
    const [amountMin, setAmountMin] = useState(filters.amount_min || '');
    const [amountMax, setAmountMax] = useState(filters.amount_max || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');
    const [viewingCommission, setViewingCommission] = useState(null);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [updatingStatus, setUpdatingStatus] = useState(null);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.commissions.index'),
            {
                search: searchTerm,
                status: statusFilter === 'all' ? '' : statusFilter,
                partner_id: partnerFilter === 'all' ? '' : partnerFilter,
                amount_min: amountMin,
                amount_max: amountMax,
                date_from: dateFrom,
                date_to: dateTo,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearFilters = () => {
        setSearchTerm('');
        setStatusFilter('all');
        setPartnerFilter('all');
        setAmountMin('');
        setAmountMax('');
        setDateFrom('');
        setDateTo('');
        router.get(route('admin.commissions.index'));
    };

    const viewCommission = async (commission) => {
        try {
            const response = await fetch(
                route('admin.commissions.show', commission.id),
            );
            const data = await response.json();
            setViewingCommission(data.commission);
            setIsViewOpen(true);
        } catch (error) {
            toast.error('Failed to load commission details');
        }
    };

    const deleteCommission = (commission) => {
        router.delete(route('admin.commissions.destroy', commission.id), {
            onSuccess: () => {
                toast.success('Commission deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete commission');
            },
        });
    };

    const updateCommissionStatus = (commission, newStatus) => {
        setUpdatingStatus(commission.id);
        router.put(
            route('admin.commissions.updateStatus', commission.id),
            { status: newStatus },
            {
                onSuccess: () => {
                    toast.success('Commission status updated successfully');
                    setUpdatingStatus(null);
                },
                onError: () => {
                    toast.error('Failed to update commission status');
                    setUpdatingStatus(null);
                },
            },
        );
    };

    const getStatusColor = (status) => {
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            paid: 'bg-green-100 text-green-800',
            cancelled: 'bg-red-100 text-red-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const getTotalCommissions = () => {
        return commissions.data.reduce(
            (total, commission) => total + parseFloat(commission.amount),
            0,
        );
    };

    const getStatusCounts = () => {
        const counts = { pending: 0, paid: 0, cancelled: 0 };
        commissions.data.forEach((commission) => {
            counts[commission.status]++;
        });
        return counts;
    };

    const statusCounts = getStatusCounts();

    return (
        <AdminLayout
            title={title || 'Commissions'}
            description={
                description || 'Manage partner commissions and payouts'
            }
        >
            <Head title={title || 'Commissions'} />

            <div className="space-y-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <DollarSign className="h-6 w-6 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Total Amount
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {formatCurrency(getTotalCommissions())}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-yellow-100">
                                    <span className="text-xs font-bold text-yellow-600">
                                        {statusCounts.pending}
                                    </span>
                                </div>
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Pending
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {statusCounts.pending}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
                                    <span className="text-xs font-bold text-green-600">
                                        {statusCounts.paid}
                                    </span>
                                </div>
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Paid
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {statusCounts.paid}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-red-100">
                                    <span className="text-xs font-bold text-red-600">
                                        {statusCounts.cancelled}
                                    </span>
                                </div>
                                <div className="ml-4">
                                    <p className="text-muted-foreground text-sm font-medium">
                                        Cancelled
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {statusCounts.cancelled}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-8">
                                <div>
                                    <Label htmlFor="search">Search</Label>
                                    <Input
                                        id="search"
                                        placeholder="Search by customer, partner..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={statusFilter}
                                        onValueChange={setStatusFilter}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                All statuses
                                            </SelectItem>
                                            {Object.entries(statuses).map(
                                                ([key, label]) => (
                                                    <SelectItem
                                                        key={key}
                                                        value={key}
                                                    >
                                                        {label}
                                                    </SelectItem>
                                                ),
                                            )}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="partner">Partner</Label>
                                    <Select
                                        value={partnerFilter}
                                        onValueChange={setPartnerFilter}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All partners" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                All partners
                                            </SelectItem>
                                            {partners.map((partner) => (
                                                <SelectItem
                                                    key={partner.id}
                                                    value={partner.id.toString()}
                                                >
                                                    {partner.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="amount_min">
                                        Min Amount
                                    </Label>
                                    <Input
                                        id="amount_min"
                                        type="number"
                                        placeholder="Min amount"
                                        value={amountMin}
                                        onChange={(e) =>
                                            setAmountMin(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="amount_max">
                                        Max Amount
                                    </Label>
                                    <Input
                                        id="amount_max"
                                        type="number"
                                        placeholder="Max amount"
                                        value={amountMax}
                                        onChange={(e) =>
                                            setAmountMax(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="date_from">Date From</Label>
                                    <Input
                                        id="date_from"
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) =>
                                            setDateFrom(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="date_to">Date To</Label>
                                    <Input
                                        id="date_to"
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) =>
                                            setDateTo(e.target.value)
                                        }
                                    />
                                </div>
                                <div className="flex items-end gap-2">
                                    <Button type="submit" className="flex-1">
                                        Search
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={clearFilters}
                                    >
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Commissions Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <DollarSign className="h-5 w-5" />
                            Commissions ({commissions.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {commissions.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <DollarSign className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                                <p className="text-muted-foreground">
                                    No commissions found
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Booking</TableHead>
                                                <TableHead>Partner</TableHead>
                                                <TableHead>Amount</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Date</TableHead>
                                                <TableHead className="text-right">
                                                    Actions
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {commissions.data.map(
                                                (commission) => (
                                                    <TableRow
                                                        key={commission.id}
                                                    >
                                                        <TableCell>
                                                            <div>
                                                                <p className="font-medium">
                                                                    {
                                                                        commission
                                                                            .booking
                                                                            ?.first_name
                                                                    }{' '}
                                                                    {
                                                                        commission
                                                                            .booking
                                                                            ?.last_name
                                                                    }
                                                                </p>
                                                                <p className="text-muted-foreground text-sm">
                                                                    {commission
                                                                        .booking
                                                                        ?.package
                                                                        ?.name ||
                                                                        'N/A'}
                                                                </p>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                <Users className="text-muted-foreground h-4 w-4" />
                                                                {commission
                                                                    .partner
                                                                    ?.name ||
                                                                    'N/A'}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-1">
                                                                <DollarSign className="h-4 w-4 text-green-600" />
                                                                <span className="font-medium">
                                                                    {formatCurrency(
                                                                        commission.amount,
                                                                    )}
                                                                </span>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                <Badge
                                                                    className={getStatusColor(
                                                                        commission.status,
                                                                    )}
                                                                >
                                                                    {
                                                                        statuses[
                                                                            commission
                                                                                .status
                                                                        ]
                                                                    }
                                                                </Badge>
                                                                <Select
                                                                    value={
                                                                        commission.status
                                                                    }
                                                                    onValueChange={(
                                                                        newStatus,
                                                                    ) =>
                                                                        updateCommissionStatus(
                                                                            commission,
                                                                            newStatus,
                                                                        )
                                                                    }
                                                                    disabled={
                                                                        updatingStatus ===
                                                                        commission.id
                                                                    }
                                                                >
                                                                    <SelectTrigger className="h-8 w-[120px]">
                                                                        <Edit3 className="h-3 w-3" />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        {Object.entries(
                                                                            statuses,
                                                                        ).map(
                                                                            ([
                                                                                key,
                                                                                label,
                                                                            ]) => (
                                                                                <SelectItem
                                                                                    key={
                                                                                        key
                                                                                    }
                                                                                    value={
                                                                                        key
                                                                                    }
                                                                                >
                                                                                    {
                                                                                        label
                                                                                    }
                                                                                </SelectItem>
                                                                            ),
                                                                        )}
                                                                    </SelectContent>
                                                                </Select>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            {formatDate(
                                                                commission.created_at,
                                                            )}
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            <div className="flex items-center justify-end gap-2">
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    onClick={() =>
                                                                        viewCommission(
                                                                            commission,
                                                                        )
                                                                    }
                                                                >
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                                <AlertDialog>
                                                                    <AlertDialogTrigger
                                                                        asChild
                                                                    >
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            className="text-red-600 hover:text-red-700"
                                                                        >
                                                                            <Trash2 className="h-4 w-4" />
                                                                        </Button>
                                                                    </AlertDialogTrigger>
                                                                    <AlertDialogContent>
                                                                        <AlertDialogHeader>
                                                                            <AlertDialogTitle>
                                                                                Delete
                                                                                Commission
                                                                            </AlertDialogTitle>
                                                                            <AlertDialogDescription>
                                                                                Are
                                                                                you
                                                                                sure
                                                                                you
                                                                                want
                                                                                to
                                                                                delete
                                                                                this
                                                                                commission
                                                                                for{' '}
                                                                                <strong>
                                                                                    {
                                                                                        commission
                                                                                            .partner
                                                                                            ?.name
                                                                                    }
                                                                                </strong>

                                                                                ?
                                                                                This
                                                                                action
                                                                                cannot
                                                                                be
                                                                                undone.
                                                                            </AlertDialogDescription>
                                                                        </AlertDialogHeader>
                                                                        <AlertDialogFooter>
                                                                            <AlertDialogCancel>
                                                                                Cancel
                                                                            </AlertDialogCancel>
                                                                            <AlertDialogAction
                                                                                onClick={() =>
                                                                                    deleteCommission(
                                                                                        commission,
                                                                                    )
                                                                                }
                                                                                className="bg-red-600 hover:bg-red-700"
                                                                            >
                                                                                Delete
                                                                            </AlertDialogAction>
                                                                        </AlertDialogFooter>
                                                                    </AlertDialogContent>
                                                                </AlertDialog>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                ),
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {commissions.last_page > 1 && (
                                    <div className="mt-4 flex items-center justify-between">
                                        <p className="text-muted-foreground text-sm">
                                            Showing {commissions.from}-
                                            {commissions.to} of{' '}
                                            {commissions.total} results
                                        </p>
                                        <div className="flex items-center space-x-2">
                                            {commissions.prev_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            commissions.prev_page_url,
                                                        )
                                                    }
                                                >
                                                    <ChevronLeft className="h-4 w-4" />
                                                    Previous
                                                </Button>
                                            )}
                                            {commissions.next_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            commissions.next_page_url,
                                                        )
                                                    }
                                                >
                                                    Next
                                                    <ChevronRight className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* View Commission Modal */}
            <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Commission Details</DialogTitle>
                        <DialogDescription>
                            Complete information about this commission
                        </DialogDescription>
                    </DialogHeader>
                    {viewingCommission && (
                        <div className="space-y-6">
                            {/* Commission Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Commission Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Commission ID</Label>
                                        <p className="text-sm">
                                            #{viewingCommission.id}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Amount</Label>
                                        <p className="text-sm font-medium text-green-600">
                                            {formatCurrency(
                                                viewingCommission.amount,
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Status</Label>
                                        <Badge
                                            className={getStatusColor(
                                                viewingCommission.status,
                                            )}
                                        >
                                            {statuses[viewingCommission.status]}
                                        </Badge>
                                    </div>
                                    <div>
                                        <Label>Created Date</Label>
                                        <p className="text-sm">
                                            {formatDate(
                                                viewingCommission.created_at,
                                            )}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Partner Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Partner Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Partner Name</Label>
                                        <p className="text-sm">
                                            {viewingCommission.partner?.name ||
                                                'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Partner Type</Label>
                                        <p className="text-sm">
                                            {viewingCommission.partner?.type ||
                                                'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Commission Rate</Label>
                                        <p className="text-sm">
                                            {viewingCommission.partner
                                                ?.commission_rate
                                                ? `${viewingCommission.partner.commission_rate}%`
                                                : 'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Partner Balance</Label>
                                        <p className="text-sm">
                                            {viewingCommission.partner?.balance
                                                ? formatCurrency(
                                                      viewingCommission.partner
                                                          .balance,
                                                  )
                                                : 'N/A'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Booking Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Related Booking
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Customer Name</Label>
                                        <p className="text-sm">
                                            {
                                                viewingCommission.booking
                                                    ?.first_name
                                            }{' '}
                                            {
                                                viewingCommission.booking
                                                    ?.last_name
                                            }
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Package</Label>
                                        <p className="text-sm">
                                            {viewingCommission.booking?.package
                                                ?.name || 'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Booking Date</Label>
                                        <p className="text-sm">
                                            {viewingCommission.booking
                                                ?.booking_date
                                                ? formatDate(
                                                      viewingCommission.booking
                                                          .booking_date,
                                                  )
                                                : 'N/A'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Country</Label>
                                        <p className="text-sm">
                                            {viewingCommission.booking?.country
                                                ?.name || 'N/A'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Partner Description */}
                            {viewingCommission.partner?.description && (
                                <div>
                                    <h3 className="mb-3 text-lg font-semibold">
                                        Partner Description
                                    </h3>
                                    <p className="rounded-md bg-gray-50 p-3 text-sm">
                                        {viewingCommission.partner.description}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
