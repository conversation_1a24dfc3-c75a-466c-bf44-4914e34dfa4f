import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Clock,
    Edit,
    Eye,
    Filter,
    MapPin,
    Package,
    Plus,
    Search,
    Trash2,
    X,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function PackagesIndex({
    packages,
    destinations,
    activities,
    filters,
    title,
    description,
    ...props
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [destinationFilter, setDestinationFilter] = useState(
        filters.destination || 'all',
    );
    const [activityFilter, setActivityFilter] = useState(
        filters.activity || 'all',
    );
    const [showFilters, setShowFilters] = useState(false);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.packages.index'),
            {
                search: searchTerm,
                destination:
                    destinationFilter === 'all' ? '' : destinationFilter,
                activity: activityFilter === 'all' ? '' : activityFilter,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearFilters = () => {
        setSearchTerm('');
        setDestinationFilter('all');
        setActivityFilter('all');
        router.get(
            route('admin.packages.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDeletePackage = (packageId) => {
        router.delete(route('admin.packages.destroy', packageId), {
            onSuccess: () => {
                toast.success('Package deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete package');
            },
        });
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getDurationText = (duration) => {
        return duration === 1 ? '1 day' : `${duration} days`;
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.packages.create')}>
                    <Button type="button">
                        <Plus className="mr-2 h-4 w-4" />
                        Add Package
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="container mx-auto pb-8">
                {/* Search and Filters */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="flex flex-col items-center gap-4 sm:flex-row">
                                <div className="flex-1">
                                    <div className="relative mt-1">
                                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                        <Input
                                            id="search"
                                            type="text"
                                            placeholder="Search by name, description, or location..."
                                            value={searchTerm}
                                            onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                            }
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() =>
                                            setShowFilters(!showFilters)
                                        }
                                    >
                                        <Filter className="mr-2 h-4 w-4" />
                                        Filters
                                    </Button>
                                    <Button type="submit">
                                        <Search className="mr-2 h-4 w-4" />
                                        Search
                                    </Button>
                                    {(searchTerm ||
                                        destinationFilter !== 'all' ||
                                        activityFilter !== 'all') && (
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={clearFilters}
                                        >
                                            <X className="mr-2 h-4 w-4" />
                                            Clear
                                        </Button>
                                    )}
                                </div>
                            </div>

                            {/* Advanced Filters */}
                            {showFilters && (
                                <div className="mt-4 border-t pt-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="destination">
                                                Destination
                                            </Label>
                                            <Select
                                                value={destinationFilter}
                                                onValueChange={
                                                    setDestinationFilter
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="All destinations" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">
                                                        All destinations
                                                    </SelectItem>
                                                    {destinations.map(
                                                        (destination) => (
                                                            <SelectItem
                                                                key={
                                                                    destination.id
                                                                }
                                                                value={destination.id.toString()}
                                                            >
                                                                {
                                                                    destination.name
                                                                }
                                                            </SelectItem>
                                                        ),
                                                    )}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label htmlFor="activity">
                                                Activity
                                            </Label>
                                            <Select
                                                value={activityFilter}
                                                onValueChange={
                                                    setActivityFilter
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="All activities" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">
                                                        All activities
                                                    </SelectItem>
                                                    {activities.map(
                                                        (activity) => (
                                                            <SelectItem
                                                                key={
                                                                    activity.id
                                                                }
                                                                value={activity.id.toString()}
                                                            >
                                                                {activity.name}
                                                                {activity.destination && (
                                                                    <span className="ml-2 text-gray-500">
                                                                        (
                                                                        {
                                                                            activity
                                                                                .destination
                                                                                .name
                                                                        }
                                                                        )
                                                                    </span>
                                                                )}
                                                            </SelectItem>
                                                        ),
                                                    )}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </form>
                    </CardContent>
                </Card>

                {/* Packages Table */}
                <Card>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Package</TableHead>
                                        <TableHead>Destination</TableHead>
                                        <TableHead>Activities</TableHead>
                                        <TableHead>Duration</TableHead>
                                        <TableHead>Base Price</TableHead>
                                        <TableHead>Location</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {packages.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={8}
                                                className="py-8 text-center"
                                            >
                                                <div className="flex flex-col items-center">
                                                    <Package className="mb-2 h-12 w-12 text-gray-400" />
                                                    <p className="text-gray-500">
                                                        No packages found
                                                    </p>
                                                    <p className="mt-1 text-sm text-gray-400">
                                                        Create your first
                                                        package to get started
                                                    </p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        packages.data.map((pkg) => (
                                            <TableRow key={pkg.id}>
                                                <TableCell>
                                                    <div className="flex items-start space-x-3">
                                                        {pkg.image ? (
                                                            <img
                                                                src={`/storage/${pkg.image}`}
                                                                alt={pkg.name}
                                                                className="h-16 w-16 flex-shrink-0 rounded-lg object-cover"
                                                            />
                                                        ) : (
                                                            <div className="flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-lg bg-gray-100">
                                                                <Package className="h-12 w-12 text-gray-300" />
                                                            </div>
                                                        )}
                                                        <div>
                                                            <div className="font-medium text-gray-900 dark:text-white">
                                                                {pkg.name}
                                                            </div>
                                                            <div className="mt-1 flex flex-wrap gap-1">
                                                                {pkg.is_featured && (
                                                                    <Badge
                                                                        variant="default"
                                                                        className="bg-yellow-100 text-xs text-yellow-800 hover:bg-yellow-200"
                                                                    >
                                                                        Featured
                                                                    </Badge>
                                                                )}
                                                                {pkg.add_to_slider && (
                                                                    <Badge
                                                                        variant="default"
                                                                        className="bg-blue-100 text-xs text-blue-800 hover:bg-blue-200"
                                                                    >
                                                                        Slider
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                            <div className="mt-1 line-clamp-2 text-sm text-gray-500 dark:text-gray-400">
                                                                {pkg.description
                                                                    .length >
                                                                100
                                                                    ? `${pkg.description.substring(0, 100)}...`
                                                                    : pkg.description}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center">
                                                        <MapPin className="mr-1 h-4 w-4 text-gray-400" />
                                                        <span className="text-sm font-medium">
                                                            {pkg.destination
                                                                ?.name || 'N/A'}
                                                        </span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {pkg.activities?.length >
                                                    0 ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {pkg.activities
                                                                .slice(0, 2)
                                                                .map(
                                                                    (
                                                                        activity,
                                                                    ) => (
                                                                        <Badge
                                                                            key={
                                                                                activity.id
                                                                            }
                                                                            variant="secondary"
                                                                            className="py-4 text-xs"
                                                                        >
                                                                            {
                                                                                activity.name
                                                                            }
                                                                        </Badge>
                                                                    ),
                                                                )}
                                                            {pkg.activities
                                                                .length > 2 && (
                                                                <Badge
                                                                    variant="outline"
                                                                    className="text-xs"
                                                                >
                                                                    +
                                                                    {pkg
                                                                        .activities
                                                                        .length -
                                                                        2}
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    ) : (
                                                        <span className="text-sm text-gray-500">
                                                            No activities
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center">
                                                        <Clock className="mr-1 h-4 w-4 text-gray-400" />
                                                        <span className="text-sm">
                                                            {getDurationText(
                                                                pkg.duration,
                                                            )}
                                                        </span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center">
                                                        <span className="font-medium">
                                                            {formatPrice(
                                                                pkg.base_price,
                                                            )}
                                                        </span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="text-sm text-gray-600 dark:text-gray-300">
                                                        {pkg.location}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="text-sm text-gray-500">
                                                        {new Date(
                                                            pkg.created_at,
                                                        ).toLocaleDateString()}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <Link
                                                            href={route(
                                                                'admin.packages.show',
                                                                pkg.id,
                                                            )}
                                                        >
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                            >
                                                                <Eye className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <Link
                                                            href={route(
                                                                'admin.packages.edit',
                                                                pkg.id,
                                                            )}
                                                        >
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        Package
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete "
                                                                        {
                                                                            pkg.name
                                                                        }
                                                                        "? This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone
                                                                        and will
                                                                        also
                                                                        delete
                                                                        all
                                                                        related
                                                                        data
                                                                        (attributes,
                                                                        prices,
                                                                        plans,
                                                                        etc.).
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeletePackage(
                                                                                pkg.id,
                                                                            )
                                                                        }
                                                                        className="bg-red-600 hover:bg-red-700"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {packages.last_page > 1 && (
                            <div className="flex items-center justify-between border-t px-6 py-4">
                                <div className="text-sm text-gray-700 dark:text-gray-300">
                                    Showing {packages.from} to {packages.to} of{' '}
                                    {packages.total} packages
                                </div>
                                <div className="flex items-center space-x-2">
                                    {packages.prev_page_url && (
                                        <Link href={packages.prev_page_url}>
                                            <Button variant="outline" size="sm">
                                                <ChevronLeft className="mr-1 h-4 w-4" />
                                                Previous
                                            </Button>
                                        </Link>
                                    )}
                                    {packages.next_page_url && (
                                        <Link href={packages.next_page_url}>
                                            <Button variant="outline" size="sm">
                                                Next
                                                <ChevronRight className="ml-1 h-4 w-4" />
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
