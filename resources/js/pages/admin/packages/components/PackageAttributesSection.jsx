import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { FileText, Plus, Trash2, Upload } from 'lucide-react';

export default function PackageAttributesSection({
    attributes,
    setAttributes,
    errors,
}) {
    const addAttribute = () => {
        setAttributes([...attributes, { name: '', value: '', icon: null }]);
    };

    const removeAttribute = (index) => {
        // Prevent deletion of default attributes
        if (attributes[index]?.isDefault) {
            return;
        }
        setAttributes(attributes.filter((_, i) => i !== index));
    };

    const updateAttribute = (index, field, value) => {
        const updated = [...attributes];
        updated[index][field] = value;
        setAttributes(updated);
    };

    const handleAttributeIconUpload = (index, file) => {
        const updated = [...attributes];
        updated[index]['icon'] = file;
        setAttributes(updated);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                        <FileText className="mr-2 h-5 w-5" />
                        Package Attributes
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {attributes.map((attribute, index) => (
                        <div
                            key={index}
                            className="grid grid-cols-12 items-center gap-4"
                        >
                            <div className="col-span-4">
                                <Input
                                    placeholder="Attribute name"
                                    value={attribute.name}
                                    onChange={(e) =>
                                        updateAttribute(
                                            index,
                                            'name',
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                            <div className="col-span-4">
                                <Input
                                    placeholder="Attribute value"
                                    value={attribute.value}
                                    onChange={(e) =>
                                        updateAttribute(
                                            index,
                                            'value',
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                            <div className="col-span-3">
                                <div className="flex items-center space-x-2">
                                    <Input
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => {
                                            const file = e.target.files[0];
                                            if (file) {
                                                handleAttributeIconUpload(
                                                    index,
                                                    file,
                                                );
                                            }
                                        }}
                                        className="text-sm"
                                    />
                                    {attribute.icon && (
                                        <div className="flex items-center text-green-600">
                                            <Upload className="h-4 w-4" />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="col-span-1 flex justify-end">
                                {!attribute.isDefault && (
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeAttribute(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
                <div className="mt-4">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={addAttribute}
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Attribute
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
