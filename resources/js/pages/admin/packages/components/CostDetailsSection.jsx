import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { DollarSign, Plus, Trash2 } from 'lucide-react';

export default function CostDetailsSection({
    costDetails,
    setCostDetails,
    errors,
}) {
    const addCostDetail = () => {
        setCostDetails([
            ...costDetails,
            { description: '', cost_type: 'included' },
        ]);
    };

    const removeCostDetail = (index) => {
        setCostDetails(costDetails.filter((_, i) => i !== index));
    };

    const updateCostDetail = (index, field, value) => {
        const updated = [...costDetails];
        updated[index][field] = value;
        setCostDetails(updated);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                        <DollarSign className="mr-2 h-5 w-5" />
                        Cost Details (Included/Excluded)
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {costDetails.length > 0 &&
                        costDetails.map((detail, index) => (
                            <div
                                key={index}
                                className="grid grid-cols-1 gap-4 rounded-lg border p-4 md:grid-cols-3"
                            >
                                <div className="md:col-span-2">
                                    <Label>Description</Label>
                                    <Textarea
                                        placeholder="What is included or excluded?"
                                        value={detail.description}
                                        onChange={(e) =>
                                            updateCostDetail(
                                                index,
                                                'description',
                                                e.target.value,
                                            )
                                        }
                                        rows={2}
                                    />
                                </div>
                                <div>
                                    <Label>Type</Label>
                                    <Select
                                        value={detail.cost_type}
                                        onValueChange={(value) =>
                                            updateCostDetail(
                                                index,
                                                'cost_type',
                                                value,
                                            )
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="included">
                                                Included
                                            </SelectItem>
                                            <SelectItem value="excluded">
                                                Excluded
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="flex justify-end md:col-span-3">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeCostDetail(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Remove
                                    </Button>
                                </div>
                            </div>
                        ))}

                    {costDetails.length === 0 && (
                        <div className="py-4 text-sm text-gray-500">
                            No cost details added yet.
                        </div>
                    )}
                </div>
                <div className="mt-4">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={addCostDetail}
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Cost Detail
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
