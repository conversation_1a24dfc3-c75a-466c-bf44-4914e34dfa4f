import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { DollarSign, Plus, Trash2 } from 'lucide-react';

export default function PriceTiersSection({ prices, setPrices, errors }) {
    const addPrice = () => {
        setPrices([...prices, { price: '', condition: '' }]);
    };

    const removePrice = (index) => {
        setPrices(prices.filter((_, i) => i !== index));
    };

    const updatePrice = (index, field, value) => {
        const updated = [...prices];
        updated[index][field] = value;
        setPrices(updated);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                        <DollarSign className="mr-2 h-5 w-5" />
                        Price Tiers
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {prices.length > 0 &&
                        prices.map((price, index) => (
                            <div
                                key={index}
                                className="grid grid-cols-1 gap-4 rounded-lg border p-4 md:grid-cols-2"
                            >
                                <div>
                                    <Label>Price (USD)</Label>
                                    <Input
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={price.price}
                                        onChange={(e) =>
                                            updatePrice(
                                                index,
                                                'price',
                                                e.target.value,
                                            )
                                        }
                                    />
                                </div>
                                <div className="flex items-end md:col-span-1">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removePrice(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Remove
                                    </Button>
                                </div>
                                <div className="md:col-span-2">
                                    <Label>Condition/Description</Label>
                                    <Textarea
                                        placeholder="Price condition or description (e.g., 'Standard package', 'Peak season rate')"
                                        value={price.condition}
                                        onChange={(e) =>
                                            updatePrice(
                                                index,
                                                'condition',
                                                e.target.value,
                                            )
                                        }
                                        rows={2}
                                    />
                                </div>
                            </div>
                        ))}
                    {prices.length === 0 && (
                        <div className="py-4 text-sm text-gray-500">
                            No price tiers added yet.
                        </div>
                    )}
                </div>
                <div className="mt-4">
                    <Button type="button" variant="outline" onClick={addPrice}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Price
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
