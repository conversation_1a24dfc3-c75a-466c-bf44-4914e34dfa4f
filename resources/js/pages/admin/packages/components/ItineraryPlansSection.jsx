import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { Calendar, Plus, Trash2 } from 'lucide-react';

export default function ItineraryPlansSection({ plans, setPlans, errors }) {
    const addPlan = () => {
        const nextDay = Math.max(...plans.map((p) => p.day_number)) + 1;
        setPlans([
            ...plans,
            { day_number: nextDay, plan_name: '', description: '' },
        ]);
    };

    const removePlan = (index) => {
        setPlans(plans.filter((_, i) => i !== index));
    };

    const updatePlan = (index, field, value) => {
        const updated = [...plans];
        updated[index][field] = value;
        setPlans(updated);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                        <Calendar className="mr-2 h-5 w-5" />
                        Itinerary Plans
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {plans.length > 0 &&
                        plans.map((plan, index) => (
                            <div
                                key={index}
                                className="grid grid-cols-1 gap-4 rounded-lg border p-4 md:grid-cols-3"
                            >
                                <div className="md:col-span-1">
                                    <Label>Day Number</Label>
                                    <Input
                                        type="number"
                                        min="1"
                                        placeholder="Day number"
                                        value={plan.day_number}
                                        onChange={(e) =>
                                            updatePlan(
                                                index,
                                                'day_number',
                                                parseInt(e.target.value),
                                            )
                                        }
                                    />
                                </div>
                                <div className="md:col-span-2">
                                    <Label>Plan Name</Label>
                                    <Input
                                        placeholder="Day title/name"
                                        value={plan.plan_name}
                                        onChange={(e) =>
                                            updatePlan(
                                                index,
                                                'plan_name',
                                                e.target.value,
                                            )
                                        }
                                    />
                                </div>
                                <div className="md:col-span-3">
                                    <Label>Description</Label>
                                    <Textarea
                                        placeholder="What happens on this day?"
                                        value={plan.description}
                                        onChange={(e) =>
                                            updatePlan(
                                                index,
                                                'description',
                                                e.target.value,
                                            )
                                        }
                                        rows={3}
                                    />
                                </div>
                                <div className="flex justify-end md:col-span-3">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removePlan(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Remove Day
                                    </Button>
                                </div>
                            </div>
                        ))}

                    {plans.length === 0 && (
                        <div className="py-4 text-sm text-gray-500">
                            No itinerary plans added yet.
                        </div>
                    )}
                </div>
                <div className="mt-4">
                    <Button type="button" variant="outline" onClick={addPlan}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Day
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
