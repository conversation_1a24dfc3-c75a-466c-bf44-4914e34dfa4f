import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { FileText, Upload } from 'lucide-react';

export default function RouteMapSection({
    data,
    setData,
    previewRouteMap,
    setPreviewRouteMap,
    errors,
}) {
    const handleRouteMapChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('route_map', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewRouteMap(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Route Map
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 gap-4">
                    <div>
                        <Label htmlFor="route_map">Route Map Image</Label>
                        <div className="mt-1">
                            <input
                                id="route_map"
                                type="file"
                                accept="image/*"
                                onChange={handleRouteMapChange}
                                className="hidden"
                            />
                            <label
                                htmlFor="route_map"
                                className="inline-flex cursor-pointer items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                            >
                                <Upload className="mr-2 h-4 w-4" />
                                {previewRouteMap
                                    ? 'Change Route Map'
                                    : 'Upload Route Map'}
                            </label>
                            {previewRouteMap && (
                                <div className="mt-4">
                                    <img
                                        src={previewRouteMap}
                                        alt="Route Map Preview"
                                        className="h-64 w-full rounded-lg border object-contain"
                                    />
                                </div>
                            )}
                        </div>
                        {errors.route_map && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.route_map}
                            </p>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
