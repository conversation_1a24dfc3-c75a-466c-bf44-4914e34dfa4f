import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { HelpCircle, Plus, Trash2 } from 'lucide-react';

export default function FaqsSection({ faqs, setFaqs, errors }) {
    const addFaq = () => {
        setFaqs([...faqs, { question: '', answer: '' }]);
    };

    const removeFaq = (index) => {
        setFaqs(faqs.filter((_, i) => i !== index));
    };

    const updateFaq = (index, field, value) => {
        const updated = [...faqs];
        updated[index][field] = value;
        setFaqs(updated);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                        <HelpCircle className="mr-2 h-5 w-5" />
                        Frequently Asked Questions
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {faqs.length > 0 &&
                        faqs.map((faq, index) => (
                            <div
                                key={index}
                                className="grid grid-cols-1 gap-4 rounded-lg border p-4"
                            >
                                <div>
                                    <Label>Question</Label>
                                    <Input
                                        placeholder="Enter the question"
                                        value={faq.question}
                                        onChange={(e) =>
                                            updateFaq(
                                                index,
                                                'question',
                                                e.target.value,
                                            )
                                        }
                                    />
                                </div>
                                <div>
                                    <Label>Answer</Label>
                                    <Textarea
                                        placeholder="Enter the answer"
                                        value={faq.answer}
                                        onChange={(e) =>
                                            updateFaq(
                                                index,
                                                'answer',
                                                e.target.value,
                                            )
                                        }
                                        rows={3}
                                    />
                                </div>
                                <div className="flex justify-end">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeFaq(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Remove FAQ
                                    </Button>
                                </div>
                            </div>
                        ))}

                    {faqs.length === 0 && (
                        <div className="py-4 text-sm text-gray-500">
                            No FAQs added yet.
                        </div>
                    )}
                </div>
                <div className="mt-4">
                    <Button type="button" variant="outline" onClick={addFaq}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add FAQ
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
