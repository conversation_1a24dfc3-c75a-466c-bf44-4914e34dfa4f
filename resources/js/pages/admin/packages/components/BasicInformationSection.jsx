import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { MultiSelect } from '@admin/components/ui/multi-select.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { Switch } from '@admin/components/ui/switch.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { FileText, Upload } from 'lucide-react';

export default function BasicInformationSection({
    data,
    setData,
    destinations,
    activities,
    selectedActivities,
    setSelectedActivities,
    errors,
    previewImage,
    setPreviewImage,
}) {
    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Basic Information
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div className="md:col-span-2">
                        <Label htmlFor="name">Package Name *</Label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            placeholder="Enter package name"
                            required
                        />
                        {errors.name && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.name}
                            </p>
                        )}
                    </div>

                    <div className="md:col-span-2">
                        <Label htmlFor="slug">Slug *</Label>
                        <Input
                            id="slug"
                            type="text"
                            value={data.slug}
                            onChange={(e) => setData('slug', e.target.value)}
                            placeholder="Enter package slug (e.g., everest-base-camp-trek)"
                            required
                        />
                        <p className="mt-1 text-sm text-gray-500">
                            URL-friendly version of the name. Used in the package URL.
                        </p>
                        {errors.slug && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.slug}
                            </p>
                        )}
                    </div>

                    <div className="md:col-span-2">
                        <Label htmlFor="description">Description *</Label>
                        <Textarea
                            id="description"
                            value={data.description}
                            onChange={(e) =>
                                setData('description', e.target.value)
                            }
                            placeholder="Enter package description"
                            rows={4}
                            required
                        />
                        {errors.description && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.description}
                            </p>
                        )}
                    </div>

                    <div>
                        <Label htmlFor="destination_id">Destination *</Label>
                        <Select
                            value={data.destination_id}
                            onValueChange={(value) => {
                                setData('destination_id', value);
                                // Clear selected activities when destination changes
                                setSelectedActivities([]);
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select destination" />
                            </SelectTrigger>
                            <SelectContent>
                                {destinations.map((destination) => (
                                    <SelectItem
                                        key={destination.id}
                                        value={destination.id.toString()}
                                    >
                                        {destination.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.destination_id && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.destination_id}
                            </p>
                        )}
                    </div>

                    <div>
                        <Label htmlFor="location">Location *</Label>
                        <Input
                            id="location"
                            type="text"
                            value={data.location}
                            onChange={(e) =>
                                setData('location', e.target.value)
                            }
                            placeholder="Enter location"
                            required
                        />
                        {errors.location && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.location}
                            </p>
                        )}
                    </div>

                    {/* Activities Selection - Multi-select */}
                    <div className="md:col-span-2">
                        <Label htmlFor="activities">Activities</Label>
                        <div className="mt-1">
                            {data.destination_id ? (
                                <MultiSelect
                                    options={activities
                                        .filter(
                                            (activity) =>
                                                activity.destinations &&
                                                activity.destinations.some(
                                                    (dest) =>
                                                        dest.id.toString() ===
                                                        data.destination_id,
                                                ),
                                        )
                                        .map((activity) => ({
                                            value: activity.id,
                                            label: activity.name,
                                        }))}
                                    value={activities
                                        .filter((activity) =>
                                            selectedActivities.includes(
                                                activity.id,
                                            ),
                                        )
                                        .map((activity) => ({
                                            value: activity.id,
                                            label: activity.name,
                                        }))}
                                    onChange={(selectedOptions) => {
                                        const ids = selectedOptions
                                            ? selectedOptions.map(
                                                  (option) => option.value,
                                              )
                                            : [];
                                        setSelectedActivities(ids);
                                    }}
                                    placeholder="Select activities..."
                                    noOptionsMessage={() =>
                                        'No activities found'
                                    }
                                />
                            ) : (
                                <div className="border-input bg-background text-muted-foreground flex h-10 w-full rounded-md border px-3 py-2 text-sm">
                                    Please select a destination first to view
                                    available activities.
                                </div>
                            )}
                        </div>
                        {errors.activities && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.activities}
                            </p>
                        )}
                    </div>

                    <div>
                        <Label htmlFor="duration">Duration (days) *</Label>
                        <Input
                            id="duration"
                            type="number"
                            min="1"
                            value={data.duration}
                            onChange={(e) =>
                                setData('duration', e.target.value)
                            }
                            placeholder="Enter duration in days"
                            required
                        />
                        {errors.duration && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.duration}
                            </p>
                        )}
                    </div>

                    <div>
                        <Label htmlFor="base_price">Base Price (USD) *</Label>
                        <Input
                            id="base_price"
                            type="number"
                            min="0"
                            step="0.01"
                            value={data.base_price}
                            onChange={(e) =>
                                setData('base_price', e.target.value)
                            }
                            placeholder="Enter base price"
                            required
                        />
                        {errors.base_price && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.base_price}
                            </p>
                        )}
                    </div>

                    {/* Add to Slider Switch */}
                    <div>
                        <div className="flex items-center space-x-2">
                            <Switch
                                id="add_to_slider"
                                checked={data.add_to_slider}
                                onCheckedChange={(checked) =>
                                    setData('add_to_slider', checked)
                                }
                            />
                            <Label htmlFor="add_to_slider">Add to Slider</Label>
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            Display this package in the homepage slider
                        </p>
                        {errors.add_to_slider && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.add_to_slider}
                            </p>
                        )}
                    </div>

                    {/* Is Featured Switch */}
                    <div>
                        <div className="flex items-center space-x-2">
                            <Switch
                                id="is_featured"
                                checked={data.is_featured}
                                onCheckedChange={(checked) =>
                                    setData('is_featured', checked)
                                }
                            />
                            <Label htmlFor="is_featured">
                                Featured Package
                            </Label>
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            Mark this package as featured to highlight it
                        </p>
                        {errors.is_featured && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.is_featured}
                            </p>
                        )}
                    </div>

                    {/* Image Upload */}
                    <div className="md:col-span-2">
                        <Label htmlFor="image">Package Image</Label>
                        <div className="mt-1">
                            <input
                                id="image"
                                type="file"
                                accept="image/*"
                                onChange={handleImageChange}
                                className="hidden"
                            />
                            <label
                                htmlFor="image"
                                className="inline-flex cursor-pointer items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                            >
                                <Upload className="mr-2 h-4 w-4" />
                                Choose Image
                            </label>
                            {previewImage && (
                                <div className="mt-4">
                                    <img
                                        src={previewImage}
                                        alt="Preview"
                                        className="h-32 w-48 rounded-lg border object-cover"
                                    />
                                </div>
                            )}
                        </div>
                        {errors.image && (
                            <p className="mt-1 text-sm text-red-600">
                                {errors.image}
                            </p>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
