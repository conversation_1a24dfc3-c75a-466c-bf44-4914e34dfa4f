import { Button } from '@admin/components/ui/button.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { BasicInformationSection } from './components';

export default function Create({
    destinations,
    activities,
    title,
    description,
    ...props
}) {
    const [selectedActivities, setSelectedActivities] = useState([]);
    const [previewImage, setPreviewImage] = useState(null);

    const { data, setData, post, processing, errors } = useForm({
        name: '',
        slug: '',
        description: '',
        destination_id: '',
        base_price: '',
        location: '',
        duration: '',
        image: null,
        add_to_slider: false,
        is_featured: false,
        activities: [],
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        // Create FormData for file uploads
        const formData = new FormData();

        // Add all basic form data
        Object.keys(data).forEach((key) => {
            if (data[key] !== null && data[key] !== undefined) {
                formData.append(key, data[key]);
            }
        });

        // Add activities
        selectedActivities.forEach((activityId) => {
            formData.append('activities[]', activityId);
        });

        router.post(route('admin.packages.store'), formData, {
            onSuccess: () => {
                toast.success(
                    'Package created successfully! Continue editing to add more details.',
                );
                // The Laravel controller should handle the redirect to edit page
            },
            onError: () => {
                toast.error('Failed to create package');
            },
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.packages.index')}>
                    <Button variant="outline">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Packages
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="container mx-auto pb-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Information */}
                    <BasicInformationSection
                        data={data}
                        setData={setData}
                        destinations={destinations}
                        activities={activities}
                        selectedActivities={selectedActivities}
                        setSelectedActivities={setSelectedActivities}
                        errors={errors}
                        previewImage={previewImage}
                        setPreviewImage={setPreviewImage}
                    />

                    {/* Info Message */}
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg
                                    className="h-5 w-5 text-blue-400"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">
                                    Step 1: Create Basic Package
                                </h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    <p>
                                        After creating the package with basic
                                        information, you'll be redirected to the
                                        edit page where you can add:
                                    </p>
                                    <ul className="mt-1 list-inside list-disc">
                                        <li>
                                            Package attributes (altitude, best
                                            time, etc.)
                                        </li>
                                        <li>Price tiers and packages</li>
                                        <li>Day-by-day itinerary plans</li>
                                        <li>Route map</li>
                                        <li>
                                            Cost details (included/excluded
                                            items)
                                        </li>
                                        <li>Frequently asked questions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-end space-x-4">
                        <Link href={route('admin.packages.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing
                                ? 'Creating...'
                                : 'Create Package & Continue'}
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
