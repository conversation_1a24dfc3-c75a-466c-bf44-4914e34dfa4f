import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Separator } from '@admin/components/ui/separator.jsx';
import {
    Ta<PERSON>,
    TabsContent,
    TabsList,
    TabsTrigger,
} from '@admin/components/ui/tabs.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link } from '@inertiajs/react';
import {
    ArrowLeft,
    Calendar,
    Clock,
    DollarSign,
    Edit,
    ExternalLink,
    FileText,
    HelpCircle,
    Image as ImageIcon,
    MapPin,
    Package as PackageIcon,
    Star,
} from 'lucide-react';

export default function PackageShow({
    package: pkg,
    title,
    description,
    ...props
}) {
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getDurationText = (duration) => {
        if (duration === 1) return '1 day';
        return `${duration} days`;
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.packages.index')}>
                    <Button variant="outline">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Packages
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="container mx-auto pb-8">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                    {pkg.name}
                                </h1>
                                <p className="mt-2 text-gray-600 dark:text-gray-300">
                                    {pkg.destination.name}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Link href={route('admin.packages.edit', pkg.id)}>
                                <Button>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Package
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="lg:col-span-2">
                        <Tabs defaultValue="overview" className="space-y-6">
                            <TabsList className="grid w-full grid-cols-6">
                                <TabsTrigger value="overview">
                                    Overview
                                </TabsTrigger>
                                <TabsTrigger value="plans">Plans</TabsTrigger>
                                <TabsTrigger value="pricing">
                                    Pricing
                                </TabsTrigger>
                                <TabsTrigger value="costs">Costs</TabsTrigger>
                                <TabsTrigger value="faqs">FAQs</TabsTrigger>
                                <TabsTrigger value="reviews">
                                    Reviews
                                </TabsTrigger>
                            </TabsList>

                            {/* Overview Tab */}
                            <TabsContent value="overview" className="space-y-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <FileText className="mr-2 h-5 w-5" />
                                            Package Information
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <h4 className="mb-2 font-medium text-gray-900 dark:text-white">
                                                Description
                                            </h4>
                                            <p className="whitespace-pre-wrap text-gray-600 dark:text-gray-300">
                                                {pkg.description}
                                            </p>
                                        </div>

                                        {pkg.route_map_url && (
                                            <div>
                                                <h4 className="mb-2 font-medium text-gray-900 dark:text-white">
                                                    Route Map
                                                </h4>
                                                <a
                                                    href={pkg.route_map_url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-primary hover:text-text-primary/75 inline-flex items-center"
                                                >
                                                    View Route Map
                                                    <ExternalLink className="ml-1 h-4 w-4" />
                                                </a>
                                            </div>
                                        )}

                                        {pkg.activities &&
                                            pkg.activities.length > 0 && (
                                                <div>
                                                    <h4 className="mb-2 font-medium text-gray-900 dark:text-white">
                                                        Activities
                                                    </h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {pkg.activities.map(
                                                            (activity) => (
                                                                <Badge
                                                                    key={
                                                                        activity.id
                                                                    }
                                                                    variant="secondary"
                                                                >
                                                                    {
                                                                        activity.name
                                                                    }
                                                                </Badge>
                                                            ),
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                        {pkg.attributes &&
                                            pkg.attributes.length > 0 && (
                                                <div>
                                                    <h4 className="mb-2 font-medium text-gray-900 dark:text-white">
                                                        Attributes
                                                    </h4>
                                                    <div className="grid grid-cols-1 gap-2">
                                                        {pkg.attributes.map(
                                                            (attribute) => (
                                                                <div
                                                                    key={
                                                                        attribute.id
                                                                    }
                                                                    className="flex justify-between rounded bg-gray-50 p-2 dark:bg-gray-800"
                                                                >
                                                                    <span className="font-medium">
                                                                        {
                                                                            attribute.attribute_name
                                                                        }
                                                                    </span>
                                                                    <span className="text-gray-600 dark:text-gray-300">
                                                                        {
                                                                            attribute.attribute_value
                                                                        }
                                                                    </span>
                                                                </div>
                                                            ),
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                    </CardContent>
                                </Card>

                                {pkg.media && pkg.media.length > 0 && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center">
                                                <ImageIcon className="mr-2 h-5 w-5" />
                                                Media Gallery
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                                {pkg.media.map((media) => (
                                                    <div
                                                        key={media.id}
                                                        className="relative"
                                                    >
                                                        {media.type ===
                                                        'image' ? (
                                                            <img
                                                                src={`/storage/${media.file_path}`}
                                                                alt={
                                                                    media.caption ||
                                                                    'Package media'
                                                                }
                                                                className="h-32 w-full rounded-lg object-cover"
                                                            />
                                                        ) : (
                                                            <video
                                                                src={`/storage/${media.file_path}`}
                                                                className="h-32 w-full rounded-lg object-cover"
                                                                controls
                                                            />
                                                        )}
                                                        {media.caption && (
                                                            <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                                                {media.caption}
                                                            </p>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </TabsContent>

                            {/* Plans Tab */}
                            <TabsContent value="plans">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <Calendar className="mr-2 h-5 w-5" />
                                            Itinerary Plans
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {pkg.plans && pkg.plans.length > 0 ? (
                                            <div className="space-y-6">
                                                {pkg.plans
                                                    .sort(
                                                        (a, b) =>
                                                            a.day_number -
                                                            b.day_number,
                                                    )
                                                    .map((plan) => (
                                                        <div
                                                            key={plan.id}
                                                            className="border-primary border-l-4 pl-4"
                                                        >
                                                            <div className="mb-2 flex items-center space-x-2">
                                                                <Badge variant="outline">
                                                                    Day{' '}
                                                                    {
                                                                        plan.day_number
                                                                    }
                                                                </Badge>
                                                                <h4 className="font-medium text-gray-900 dark:text-white">
                                                                    {
                                                                        plan.plan_name
                                                                    }
                                                                </h4>
                                                            </div>
                                                            <p className="mb-2 text-gray-600 dark:text-gray-300">
                                                                {
                                                                    plan.description
                                                                }
                                                            </p>
                                                        </div>
                                                    ))}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-500">
                                                No itinerary plans available
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Pricing Tab */}
                            <TabsContent value="pricing">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <DollarSign className="mr-2 h-5 w-5" />
                                            Price Tiers
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {pkg.prices && pkg.prices.length > 0 ? (
                                            <div className="space-y-4">
                                                {pkg.prices.map((price) => (
                                                    <div
                                                        key={price.id}
                                                        className="rounded-lg border p-4"
                                                    >
                                                        <div className="mb-2 flex items-center justify-between">
                                                            <h4 className="font-medium text-gray-900 dark:text-white">
                                                                Price Option
                                                            </h4>
                                                            <span className="text-primary text-xl font-bold">
                                                                {formatPrice(
                                                                    price.price,
                                                                )}
                                                            </span>
                                                        </div>
                                                        {price.condition && (
                                                            <p className="text-gray-600 dark:text-gray-300">
                                                                {
                                                                    price.condition
                                                                }
                                                            </p>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-500">
                                                No pricing tiers available
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Cost Details Tab */}
                            <TabsContent value="costs">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <FileText className="mr-2 h-5 w-5" />
                                            Cost Breakdown
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {pkg.costDetails &&
                                        pkg.costDetails.length > 0 ? (
                                            <div className="space-y-6">
                                                {['included', 'excluded'].map(
                                                    (type) => {
                                                        const items =
                                                            pkg.costDetails.filter(
                                                                (detail) =>
                                                                    detail.cost_type ===
                                                                    type,
                                                            );
                                                        if (items.length === 0)
                                                            return null;

                                                        return (
                                                            <div key={type}>
                                                                <h4 className="mb-3 font-medium capitalize text-gray-900 dark:text-white">
                                                                    {type} Items
                                                                </h4>
                                                                <div className="space-y-2">
                                                                    {items.map(
                                                                        (
                                                                            detail,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    detail.id
                                                                                }
                                                                                className="rounded bg-gray-50 p-2 dark:bg-gray-800"
                                                                            >
                                                                                <span>
                                                                                    {
                                                                                        detail.description
                                                                                    }
                                                                                </span>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                                </div>
                                                            </div>
                                                        );
                                                    },
                                                )}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-500">
                                                No cost details available
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* FAQs Tab */}
                            <TabsContent value="faqs">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <HelpCircle className="mr-2 h-5 w-5" />
                                            Frequently Asked Questions
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {pkg.faqs && pkg.faqs.length > 0 ? (
                                            <div className="space-y-4">
                                                {pkg.faqs.map((faq) => (
                                                    <div
                                                        key={faq.id}
                                                        className="rounded-lg border p-4"
                                                    >
                                                        <h4 className="mb-2 font-medium text-gray-900 dark:text-white">
                                                            {faq.question}
                                                        </h4>
                                                        <p className="text-gray-600 dark:text-gray-300">
                                                            {faq.answer}
                                                        </p>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-500">
                                                No FAQs available
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Reviews Tab */}
                            <TabsContent value="reviews">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <Star className="mr-2 h-5 w-5" />
                                            Customer Reviews
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {pkg.reviews &&
                                        pkg.reviews.length > 0 ? (
                                            <div className="space-y-4">
                                                {pkg.reviews.map((review) => (
                                                    <div
                                                        key={review.id}
                                                        className="rounded-lg border p-4"
                                                    >
                                                        <div className="mb-2 flex items-center justify-between">
                                                            <h4 className="font-medium text-gray-900 dark:text-white">
                                                                {
                                                                    review.reviewer_name
                                                                }
                                                            </h4>
                                                            <div className="flex items-center">
                                                                {[
                                                                    ...Array(5),
                                                                ].map(
                                                                    (_, i) => (
                                                                        <Star
                                                                            key={
                                                                                i
                                                                            }
                                                                            className={`h-4 w-4 ${
                                                                                i <
                                                                                review.rating
                                                                                    ? 'fill-current text-yellow-400'
                                                                                    : 'text-gray-300'
                                                                            }`}
                                                                        />
                                                                    ),
                                                                )}
                                                                <span className="ml-2 text-sm text-gray-600">
                                                                    {
                                                                        review.rating
                                                                    }
                                                                    /5
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <p className="text-gray-600 dark:text-gray-300">
                                                            {review.comment}
                                                        </p>
                                                        <p className="mt-2 text-sm text-gray-500">
                                                            {formatDate(
                                                                review.created_at,
                                                            )}
                                                        </p>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-500">
                                                No reviews available
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Quick Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <PackageIcon className="mr-2 h-5 w-5" />
                                    Quick Info
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {pkg.image && (
                                    <div className="h-38 w-full overflow-hidden rounded-lg shadow">
                                        <img
                                            src={`/storage/${pkg.image}`}
                                            alt={pkg.name}
                                            className="h-full w-full object-cover"
                                        />
                                    </div>
                                )}

                                <div className="grid grid-cols-1 gap-3">
                                    <div className="flex items-center">
                                        <MapPin className="mr-2 h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Destination
                                            </p>
                                            <p className="font-medium">
                                                {pkg.destination?.name || 'N/A'}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center">
                                        <MapPin className="mr-2 h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Location
                                            </p>
                                            <p className="font-medium">
                                                {pkg.location}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center">
                                        <Clock className="mr-2 h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Duration
                                            </p>
                                            <p className="font-medium">
                                                {getDurationText(pkg.duration)}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center">
                                        <DollarSign className="mr-2 h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Base Price
                                            </p>
                                            <p className="text-primary text-lg font-medium">
                                                {formatPrice(pkg.base_price)}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <Separator />

                                <div className="text-sm text-gray-500">
                                    <p>Created: {formatDate(pkg.created_at)}</p>
                                    <p>Updated: {formatDate(pkg.updated_at)}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Statistics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">
                                        Activities
                                    </span>
                                    <span className="font-medium">
                                        {pkg.activities?.length || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">
                                        Price Tiers
                                    </span>
                                    <span className="font-medium">
                                        {pkg.prices?.length || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">
                                        Itinerary Days
                                    </span>
                                    <span className="font-medium">
                                        {pkg.plans?.length || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">
                                        Reviews
                                    </span>
                                    <span className="font-medium">
                                        {pkg.reviews?.length || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">
                                        Media Items
                                    </span>
                                    <span className="font-medium">
                                        {pkg.media?.length || 0}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
