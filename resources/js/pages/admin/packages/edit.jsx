import { Button } from '@admin/components/ui/button.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import {
    BasicInformationSection,
    CostDetailsSection,
    FaqsSection,
    ItineraryPlansSection,
    PackageAttributesSection,
    PriceTiersSection,
    RouteMapSection,
} from './components';

export default function PackageEdit({
    package: pkg,
    destinations,
    activities,
    title,
    description,
    ...props
}) {
    // Helper function to ensure default attributes are included
    const initializeAttributes = (existingAttributes) => {
        const defaultAttributes = [
            {
                name: 'Maximum Altitude',
                value: '',
                icon: null,
                isDefault: true,
            },
            {
                name: 'Best Time',
                value: '',
                icon: null,
                isDefault: true,
            },
        ];

        if (!existingAttributes || existingAttributes.length === 0) {
            return [...defaultAttributes];
        }

        // Merge existing attributes with defaults, ensuring defaults exist
        const mergedAttributes = [...defaultAttributes];

        // Add existing non-default attributes
        existingAttributes.forEach((attr) => {
            const isDefaultAttr = defaultAttributes.some(
                (def) => def.name === attr.name,
            );
            if (isDefaultAttr) {
                // Update the default attribute with existing value
                const defaultIndex = mergedAttributes.findIndex(
                    (def) => def.name === attr.name,
                );
                if (defaultIndex !== -1) {
                    mergedAttributes[defaultIndex].value = attr.value;
                    mergedAttributes[defaultIndex].icon = attr.icon || null;
                }
            } else {
                // Add custom attribute
                mergedAttributes.push({
                    ...attr,
                    icon: attr.icon || null,
                    isDefault: false,
                });
            }
        });

        // Add empty attribute for new additions
        if (!mergedAttributes.find((attr) => !attr.name && !attr.value)) {
            mergedAttributes.push({
                name: '',
                value: '',
                icon: null,
                isDefault: false,
            });
        }

        return mergedAttributes;
    };

    const [selectedActivities, setSelectedActivities] = useState(
        pkg.activities?.map((activity) => activity.id) || [],
    );
    const [attributes, setAttributes] = useState(
        initializeAttributes(pkg.attributes),
    );
    const [prices, setPrices] = useState(
        pkg.prices?.length > 0 ? pkg.prices : [{ price: '', condition: '' }],
    );
    const [plans, setPlans] = useState(
        pkg.plans?.length > 0
            ? pkg.plans.sort((a, b) => a.day_number - b.day_number)
            : [{ day_number: 1, plan_name: '', description: '' }],
    );
    const [costDetails, setCostDetails] = useState(
        pkg.costDetails?.length > 0
            ? pkg.costDetails
            : [{ description: '', cost_type: 'included' }],
    );
    const [faqs, setFaqs] = useState(
        pkg.faqs?.length > 0 ? pkg.faqs : [{ question: '', answer: '' }],
    );
    const [previewImage, setPreviewImage] = useState(
        pkg.image ? `/storage/${pkg.image}` : null,
    );
    const [previewRouteMap, setPreviewRouteMap] = useState(
        pkg.route_map ? `/storage/${pkg.route_map}` : null,
    );

    const { data, setData, put, processing, errors } = useForm({
        name: pkg.name || '',
        slug: pkg.slug || '',
        description: pkg.description || '',
        destination_id: pkg.destination_id ? pkg.destination_id.toString() : '',
        base_price: pkg.base_price || '',
        location: pkg.location || '',
        duration: pkg.duration || '',
        image: null,
        route_map: null,
        add_to_slider: pkg.add_to_slider || false,
        is_featured: pkg.is_featured || false,
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        // Create FormData for file uploads
        const formData = new FormData();

        // Add all basic form data
        Object.keys(data).forEach((key) => {
            if (data[key] !== null && data[key] !== undefined) {
                formData.append(key, data[key]);
            }
        });

        // Add activities
        selectedActivities.forEach((activityId) => {
            formData.append('activities[]', activityId);
        });

        // Add attributes with icons
        const filteredAttributes = attributes.filter(
            (attr) => attr.name && attr.value,
        );

        filteredAttributes.forEach((attr, index) => {
            formData.append(`attributes[${index}][name]`, attr.name);
            formData.append(`attributes[${index}][value]`, attr.value);

            if (attr.icon && typeof attr.icon !== 'string') {
                // New file upload
                formData.append(`attributes[${index}][icon]`, attr.icon);
            } else if (typeof attr.icon === 'string' && attr.icon) {
                // Existing icon path
                formData.append(
                    `attributes[${index}][existing_icon]`,
                    attr.icon,
                );
            }
        });

        // Add other arrays
        prices
            .filter((price) => price.condition && price.price)
            .forEach((price, index) => {
                formData.append(`prices[${index}][price]`, price.price);
                formData.append(`prices[${index}][condition]`, price.condition);
            });

        plans
            .filter(
                (plan) => plan.day_number && plan.plan_name && plan.description,
            )
            .forEach((plan, index) => {
                formData.append(`plans[${index}][day_number]`, plan.day_number);
                formData.append(`plans[${index}][plan_name]`, plan.plan_name);
                formData.append(
                    `plans[${index}][description]`,
                    plan.description,
                );
            });

        costDetails
            .filter((detail) => detail.description)
            .forEach((detail, index) => {
                formData.append(
                    `cost_details[${index}][description]`,
                    detail.description,
                );
                formData.append(
                    `cost_details[${index}][cost_type]`,
                    detail.cost_type || 'included',
                );
            });

        faqs.filter((faq) => faq.question && faq.answer).forEach(
            (faq, index) => {
                formData.append(`faqs[${index}][question]`, faq.question);
                formData.append(`faqs[${index}][answer]`, faq.answer);
            },
        );

        router.put(route('admin.packages.update', pkg.id), formData, {
            onSuccess: () => {
                toast.success('Package updated successfully');
            },
            onError: () => {
                toast.error('Failed to update package');
            },
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.packages.show', pkg.id)}>
                    <Button variant="outline">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Package
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="container mx-auto pb-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Information */}
                    <BasicInformationSection
                        data={data}
                        setData={setData}
                        destinations={destinations}
                        activities={activities}
                        selectedActivities={selectedActivities}
                        setSelectedActivities={setSelectedActivities}
                        errors={errors}
                        previewImage={previewImage}
                        setPreviewImage={setPreviewImage}
                    />

                    {/* Package Attributes */}
                    <PackageAttributesSection
                        attributes={attributes}
                        setAttributes={setAttributes}
                        errors={errors}
                    />

                    {/* Package Prices */}
                    <PriceTiersSection
                        prices={prices}
                        setPrices={setPrices}
                        errors={errors}
                    />

                    {/* Itinerary Plans */}
                    <ItineraryPlansSection
                        plans={plans}
                        setPlans={setPlans}
                        errors={errors}
                    />

                    {/* Route Map Upload */}
                    <RouteMapSection
                        data={data}
                        setData={setData}
                        previewRouteMap={previewRouteMap}
                        setPreviewRouteMap={setPreviewRouteMap}
                        errors={errors}
                    />

                    {/* Cost Details */}
                    <CostDetailsSection
                        costDetails={costDetails}
                        setCostDetails={setCostDetails}
                        errors={errors}
                    />

                    {/* FAQs */}
                    <FaqsSection
                        faqs={faqs}
                        setFaqs={setFaqs}
                        errors={errors}
                    />

                    {/* Action Buttons */}
                    <div className="flex items-center justify-end space-x-4">
                        <Link href={route('admin.packages.show', pkg.id)}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Updating...' : 'Update Package'}
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
