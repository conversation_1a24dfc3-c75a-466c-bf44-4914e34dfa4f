import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@admin/components/ui/dialog.jsx';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@admin/components/ui/dropdown-menu.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router } from '@inertiajs/react';
import {
    Calendar,
    ChevronDown,
    ChevronLeft,
    ChevronRight,
    ClipboardCheck,
    DollarSign,
    Eye,
    Trash2,
    Users,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function CustomizedTripsIndex({
    customizedTrips,
    filters,
    title,
    description,
    ...props
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [budgetMin, setBudgetMin] = useState(filters.budget_min || '');
    const [budgetMax, setBudgetMax] = useState(filters.budget_max || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');
    const [viewingTrip, setViewingTrip] = useState(null);
    const [isViewOpen, setIsViewOpen] = useState(false);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.customized-trips.index'),
            {
                search: searchTerm,
                budget_min: budgetMin,
                budget_max: budgetMax,
                date_from: dateFrom,
                date_to: dateTo,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearFilters = () => {
        setSearchTerm('');
        setBudgetMin('');
        setBudgetMax('');
        setDateFrom('');
        setDateTo('');
        router.get(route('admin.customized-trips.index'));
    };

    const viewTrip = async (trip) => {
        try {
            const response = await fetch(
                route('admin.customized-trips.show', trip.id),
            );
            const data = await response.json();
            setViewingTrip(data.customizedTrip);
            setIsViewOpen(true);
        } catch (error) {
            toast.error('Failed to load trip details');
        }
    };

    const handleDelete = (id) => {
        router.delete(route('admin.customized-trips.destroy', id), {
            onSuccess: () => {
                toast.success('Customized trip deleted successfully!');
            },
            onError: () => {
                toast.error('Failed to delete customized trip.');
            },
        });
    };

    const handleStatusUpdate = (id, newStatus) => {
        router.put(route('admin.customized-trips.update-status', id), {
            status: newStatus
        }, {
            onSuccess: () => {
                toast.success('Status updated successfully!');
            },
            onError: () => {
                toast.error('Failed to update status.');
            },
        });
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AdminLayout
            title={title || 'Customized Trips'}
            description={description || 'Manage all customized trip requests'}
        >
            <Head title={title || 'Customized Trips'} />

            <div className="space-y-6">
                {/* Filters */}
                <Card>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-6">
                                <div>
                                    <Label htmlFor="search">Search</Label>
                                    <Input
                                        id="search"
                                        placeholder="Search by name, email..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="budget_min">
                                        Min Budget
                                    </Label>
                                    <Input
                                        id="budget_min"
                                        type="number"
                                        placeholder="Min budget"
                                        value={budgetMin}
                                        onChange={(e) =>
                                            setBudgetMin(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="budget_max">
                                        Max Budget
                                    </Label>
                                    <Input
                                        id="budget_max"
                                        type="number"
                                        placeholder="Max budget"
                                        value={budgetMax}
                                        onChange={(e) =>
                                            setBudgetMax(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="date_from">
                                        Travel From
                                    </Label>
                                    <Input
                                        id="date_from"
                                        type="date"
                                        value={dateFrom}
                                        onChange={(e) =>
                                            setDateFrom(e.target.value)
                                        }
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="date_to">Travel To</Label>
                                    <Input
                                        id="date_to"
                                        type="date"
                                        value={dateTo}
                                        onChange={(e) =>
                                            setDateTo(e.target.value)
                                        }
                                    />
                                </div>
                                <div className="flex items-end gap-2">
                                    <Button type="submit" className="flex-1">
                                        Search
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={clearFilters}
                                    >
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Customized Trips Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <ClipboardCheck className="h-5 w-5" />
                            Customized Trips ({customizedTrips.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {customizedTrips.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <ClipboardCheck className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                                <p className="text-muted-foreground">
                                    No customized trips found
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Customer</TableHead>
                                                <TableHead>Package</TableHead>
                                                <TableHead>
                                                    Travel Date
                                                </TableHead>
                                                <TableHead>Duration</TableHead>
                                                <TableHead>Travelers</TableHead>
                                                <TableHead>Budget</TableHead>
                                                <TableHead>Country</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead className="text-right">
                                                    Actions
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {customizedTrips.data.map(
                                                (trip) => (
                                                    <TableRow key={trip.id}>
                                                        <TableCell>
                                                            <div>
                                                                <p className="font-medium">
                                                                    {
                                                                        trip.full_name
                                                                    }
                                                                </p>
                                                                <p className="text-muted-foreground text-sm">
                                                                    {trip.email}
                                                                </p>
                                                                <p className="text-muted-foreground text-sm">
                                                                    {trip.phone}
                                                                </p>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <p className="font-medium">
                                                                {trip.package
                                                                    ?.name ||
                                                                    'Custom Package'}
                                                            </p>
                                                        </TableCell>
                                                        <TableCell>
                                                            {formatDate(
                                                                trip.travel_date,
                                                            )}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-1">
                                                                <Calendar className="text-muted-foreground h-4 w-4" />
                                                                {
                                                                    trip.trip_duration
                                                                }{' '}
                                                                days
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-1">
                                                                <Users className="text-muted-foreground h-4 w-4" />
                                                                <span>
                                                                    {
                                                                        trip.number_of_adults
                                                                    }
                                                                    {trip.number_of_children >
                                                                        0 &&
                                                                        ` + ${trip.number_of_children} children`}
                                                                </span>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-1">
                                                                <DollarSign className="text-muted-foreground h-4 w-4" />
                                                                {formatCurrency(
                                                                    trip.estimated_budget,
                                                                )}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            {trip.country
                                                                ?.name || 'N/A'}
                                                        </TableCell>
                                                        <TableCell>
                                                             <DropdownMenu>
                                                                 <DropdownMenuTrigger asChild>
                                                                     <Button
                                                                         variant="outline"
                                                                         size="sm"
                                                                         className={`h-8 w-auto capitalize ${
                                                                             trip.status === 'confirmed'
                                                                                 ? 'text-green-700 border-green-300 bg-green-50'
                                                                                 : trip.status === 'cancelled'
                                                                                 ? 'text-red-700 border-red-300 bg-red-50'
                                                                                 : 'text-yellow-700 border-yellow-300 bg-yellow-50'
                                                                         }`}
                                                                     >
                                                                         {trip.status || 'pending'}
                                                                         <ChevronDown className="ml-1 h-3 w-3" />
                                                                     </Button>
                                                                 </DropdownMenuTrigger>
                                                                <DropdownMenuContent>
                                                                     <DropdownMenuItem
                                                                         onClick={() =>
                                                                             handleStatusUpdate(
                                                                                 trip.id,
                                                                                 'pending'
                                                                             )
                                                                         }
                                                                     >
                                                                         Pending
                                                                     </DropdownMenuItem>
                                                                     <DropdownMenuItem
                                                                         onClick={() =>
                                                                             handleStatusUpdate(
                                                                                 trip.id,
                                                                                 'confirmed'
                                                                             )
                                                                         }
                                                                     >
                                                                         Confirmed
                                                                     </DropdownMenuItem>
                                                                     <DropdownMenuItem
                                                                         onClick={() =>
                                                                             handleStatusUpdate(
                                                                                 trip.id,
                                                                                 'cancelled'
                                                                             )
                                                                         }
                                                                     >
                                                                         Cancelled
                                                                     </DropdownMenuItem>
                                                                 </DropdownMenuContent>
                                                            </DropdownMenu>
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            <div className="flex items-center justify-end gap-2">
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    onClick={() =>
                                                                        viewTrip(
                                                                            trip,
                                                                        )
                                                                    }
                                                                >
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                                <AlertDialog>
                                                                    <AlertDialogTrigger
                                                                        asChild
                                                                    >
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            className="text-red-600 hover:text-red-700"
                                                                        >
                                                                            <Trash2 className="h-4 w-4" />
                                                                        </Button>
                                                                    </AlertDialogTrigger>
                                                                    <AlertDialogContent>
                                                                        <AlertDialogHeader>
                                                                            <AlertDialogTitle>
                                                                                Delete
                                                                                Customized
                                                                                Trip
                                                                            </AlertDialogTitle>
                                                                            <AlertDialogDescription>
                                                                                Are
                                                                                you
                                                                                sure
                                                                                you
                                                                                want
                                                                                to
                                                                                delete
                                                                                this
                                                                                customized
                                                                                trip
                                                                                request
                                                                                from{' '}
                                                                                <strong>
                                                                                    {
                                                                                        trip.full_name
                                                                                    }
                                                                                </strong>

                                                                                ?
                                                                                This
                                                                                action
                                                                                cannot
                                                                                be
                                                                                undone.
                                                                            </AlertDialogDescription>
                                                                        </AlertDialogHeader>
                                                                        <AlertDialogFooter>
                                                                            <AlertDialogCancel>
                                                                                Cancel
                                                                            </AlertDialogCancel>
                                                                            <AlertDialogAction
                                                                                onClick={() =>
                                                                                    handleDelete(
                                                                                        trip.id,
                                                                                    )
                                                                                }
                                                                                className="bg-red-600 hover:bg-red-700"
                                                                            >
                                                                                Delete
                                                                            </AlertDialogAction>
                                                                        </AlertDialogFooter>
                                                                    </AlertDialogContent>
                                                                </AlertDialog>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                ),
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {customizedTrips.last_page > 1 && (
                                    <div className="mt-4 flex items-center justify-between">
                                        <p className="text-muted-foreground text-sm">
                                            Showing {customizedTrips.from}-
                                            {customizedTrips.to} of{' '}
                                            {customizedTrips.total} results
                                        </p>
                                        <div className="flex items-center space-x-2">
                                            {customizedTrips.prev_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            customizedTrips.prev_page_url,
                                                        )
                                                    }
                                                >
                                                    <ChevronLeft className="h-4 w-4" />
                                                    Previous
                                                </Button>
                                            )}
                                            {customizedTrips.next_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            customizedTrips.next_page_url,
                                                        )
                                                    }
                                                >
                                                    Next
                                                    <ChevronRight className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* View Trip Modal */}
            <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Customized Trip Details</DialogTitle>
                        <DialogDescription>
                            Complete information about this customized trip
                            request
                        </DialogDescription>
                    </DialogHeader>
                    {viewingTrip && (
                        <div className="space-y-6">
                            {/* Customer Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Customer Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Full Name</Label>
                                        <p className="text-sm">
                                            {viewingTrip.full_name}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Email</Label>
                                        <p className="text-sm">
                                            {viewingTrip.email}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Phone</Label>
                                        <p className="text-sm">
                                            {viewingTrip.phone}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Country</Label>
                                        <p className="text-sm">
                                            {viewingTrip.country?.name || 'N/A'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Trip Information */}
                            <div>
                                <h3 className="mb-3 text-lg font-semibold">
                                    Trip Information
                                </h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Base Package</Label>
                                        <p className="text-sm">
                                            {viewingTrip.package?.name ||
                                                'Custom Package'}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Travel Date</Label>
                                        <p className="text-sm">
                                            {formatDate(
                                                viewingTrip.travel_date,
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Trip Duration</Label>
                                        <p className="text-sm">
                                            {viewingTrip.trip_duration} days
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Estimated Budget</Label>
                                        <p className="text-sm font-medium">
                                            {formatCurrency(
                                                viewingTrip.estimated_budget,
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Number of Adults</Label>
                                        <p className="text-sm">
                                            {viewingTrip.number_of_adults}{' '}
                                            adults
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Number of Children</Label>
                                        <p className="text-sm">
                                            {viewingTrip.number_of_children}{' '}
                                            children
                                        </p>
                                    </div>
                                    <div>
                                        <Label>Request Date</Label>
                                        <p className="text-sm">
                                            {formatDate(viewingTrip.created_at)}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Special Notes */}
                            {viewingTrip.notes && (
                                <div>
                                    <h3 className="mb-3 text-lg font-semibold">
                                        Special Notes & Requirements
                                    </h3>
                                    <div className="rounded-md bg-gray-50 p-4">
                                        <p className="whitespace-pre-wrap text-sm">
                                            {viewingTrip.notes}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Summary */}
                            <div className="rounded-md bg-blue-50 p-4">
                                <h3 className="mb-3 text-lg font-semibold text-blue-900">
                                    Trip Summary
                                </h3>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="font-medium">
                                            Total Travelers:
                                        </span>
                                        <span className="ml-2">
                                            {parseInt(
                                                viewingTrip.number_of_adults,
                                            ) +
                                                parseInt(
                                                    viewingTrip.number_of_children,
                                                )}
                                        </span>
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Budget Per Person:
                                        </span>
                                        <span className="ml-2">
                                            {formatCurrency(
                                                viewingTrip.estimated_budget /
                                                    (parseInt(
                                                        viewingTrip.number_of_adults,
                                                    ) +
                                                        parseInt(
                                                            viewingTrip.number_of_children,
                                                        )),
                                            )}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
