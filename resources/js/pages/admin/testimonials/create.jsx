import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function CreateTestimonial({ title, description }) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        location: '',
        rating: '5',
        content: '',
        avatar: null,
        status: 'draft',
    });

    const [avatarPreview, setAvatarPreview] = useState(null);

    const handleAvatarChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('avatar', file);
            const reader = new FileReader();
            reader.onload = (e) => setAvatarPreview(e.target.result);
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.testimonials.store'), {
            onSuccess: () => {
                toast.success('Testimonial created successfully');
            },
            onError: () => {
                toast.error('Failed to create testimonial');
            },
        });
    };

    return (
        <AdminLayout
            actions={
                <Link href={route('admin.testimonials.index')}>
                    <Button variant="ghost" size="sm">
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                </Link>
            }
            title={title}
            description={description}
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Testimonial Details</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) =>
                                                setData('name', e.target.value)
                                            }
                                            placeholder="Enter customer name"
                                            className={
                                                errors.name
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.name && (
                                            <p className="text-destructive text-sm">
                                                {errors.name}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="location">
                                            Location
                                        </Label>
                                        <Input
                                            id="location"
                                            value={data.location}
                                            onChange={(e) =>
                                                setData(
                                                    'location',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter location (optional)"
                                            className={
                                                errors.location
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.location && (
                                            <p className="text-destructive text-sm">
                                                {errors.location}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="rating">Rating *</Label>
                                        <Input
                                            id="rating"
                                            type="number"
                                            min="1"
                                            max="5"
                                            value={data.rating}
                                            onChange={(e) =>
                                                setData('rating', e.target.value)
                                            }
                                            placeholder="Enter rating (1-5)"
                                            className={
                                                errors.rating
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.rating && (
                                            <p className="text-destructive text-sm">
                                                {errors.rating}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">
                                            Content *
                                        </Label>
                                        <Textarea
                                            id="content"
                                            value={data.content}
                                            onChange={(e) =>
                                                setData(
                                                    'content',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter testimonial content"
                                            rows={10}
                                            className={
                                                errors.content
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.content && (
                                            <p className="text-destructive text-sm">
                                                {errors.content}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Avatar Upload */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Avatar</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="avatar">Avatar</Label>
                                        <Input
                                            id="avatar"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleAvatarChange}
                                            className={
                                                errors.avatar
                                                    ? 'border-destructive'
                                                    : ''
                                            }
                                        />
                                        {errors.avatar && (
                                            <p className="text-destructive text-sm">
                                                {errors.avatar}
                                            </p>
                                        )}
                                    </div>

                                    {avatarPreview && (
                                        <div className="mt-4">
                                            <img
                                                src={avatarPreview}
                                                alt="Preview"
                                                className="h-32 w-32 rounded-full border object-cover"
                                            />
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Publish Settings</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status *</Label>
                                        <select
                                            id="status"
                                            value={data.status}
                                            onChange={(e) =>
                                                setData('status', e.target.value)
                                            }
                                            className={`w-full rounded-md border bg-background px-3 py-2 text-sm ${
                                                errors.status
                                                    ? 'border-destructive'
                                                    : 'border-input'
                                            }`}
                                        >
                                            <option value="draft">Draft</option>
                                            <option value="published">Published</option>
                                            <option value="archived">Archived</option>
                                        </select>
                                        {errors.status && (
                                            <p className="text-destructive text-sm">
                                                {errors.status}
                                            </p>
                                        )}
                                    </div>

                                    <Button type="submit" disabled={processing}>
                                        <Save className="mr-2 h-4 w-4" />
                                        {processing ? 'Saving...' : 'Save Testimonial'}
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}