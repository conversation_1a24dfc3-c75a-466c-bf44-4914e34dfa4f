import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Star, Trash2 } from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';

export default function ShowTestimonial({ testimonial, title, description }) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = () => {
        setIsDeleting(true);
        router.delete(route('admin.testimonials.destroy', testimonial.id), {
            onSuccess: () => {
                toast.success('Testimonial deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete testimonial');
                setIsDeleting(false);
            },
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const initials = useMemo(() => {
        if (!testimonial?.name) return '?';
        const parts = testimonial.name.trim().split(/\s+/);
        return parts.slice(0, 2).map((p) => p[0]?.toUpperCase()).join('');
    }, [testimonial?.name]);

    return (
        <AdminLayout
            actions={
                <Link href={route('admin.testimonials.index')}>
                    <Button variant="ghost" size="sm">
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                </Link>
            }
            title={title}
            description={description}
        >
            <Head title={title} />

            <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        {testimonial.avatar ? (
                                            <img
                                                src={`/storage/${testimonial.avatar}`}
                                                alt={testimonial.name}
                                                className="h-16 w-16 rounded-full border object-cover"
                                            />
                                        ) : (
                                            <div className="flex h-16 w-16 items-center justify-center rounded-full border bg-gray-100 text-lg font-semibold text-gray-600">
                                                {initials}
                                            </div>
                                        )}
                                        <div>
                                            <CardTitle className="text-2xl">
                                                {testimonial.name}
                                            </CardTitle>
                                            <div className="text-muted-foreground text-sm">
                                                {testimonial.location || '—'}
                                            </div>
                                        </div>
                                    </div>
                                    <Badge className={getStatusColor(testimonial.status)}>
                                        {testimonial.status.charAt(0).toUpperCase() +
                                            testimonial.status.slice(1)}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-1">
                                    {Array.from({ length: 5 }).map((_, i) => (
                                        <Star
                                            key={i}
                                            className={
                                                i < (testimonial.rating || 0)
                                                    ? 'text-yellow-400'
                                                    : 'text-gray-300'
                                            }
                                            fill={
                                                i < (testimonial.rating || 0)
                                                    ? 'currentColor'
                                                    : 'none'
                                            }
                                        />
                                    ))}
                                </div>

                                <div className="prose max-w-none">
                                    <div className="whitespace-pre-wrap leading-relaxed text-gray-700">
                                        {testimonial.content}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Testimonial Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Status
                                    </h4>
                                    <Badge className={getStatusColor(testimonial.status)}>
                                        {testimonial.status.charAt(0).toUpperCase() +
                                            testimonial.status.slice(1)}
                                    </Badge>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Rating
                                    </h4>
                                    <div className="flex items-center gap-1">
                                        {Array.from({ length: 5 }).map((_, i) => (
                                            <Star
                                                key={i}
                                                className={
                                                    i < (testimonial.rating || 0)
                                                        ? 'text-yellow-400'
                                                        : 'text-gray-300'
                                                }
                                                fill={
                                                    i < (testimonial.rating || 0)
                                                        ? 'currentColor'
                                                        : 'none'
                                                }
                                            />
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Created
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(testimonial.created_at)}
                                    </p>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground mb-1 text-sm font-medium">
                                        Last Updated
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(testimonial.updated_at)}
                                    </p>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Link href={route('admin.testimonials.edit', testimonial.id)}>
                                        <Button>
                                            <Edit className="mr-2 h-4 w-4" />
                                            Edit
                                        </Button>
                                    </Link>
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="ghost" className={'text-red-500'}>
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>
                                                    Are you absolutely sure?
                                                </AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    This action cannot be undone. This will permanently delete the testimonial and remove all associated data.
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>
                                                    Cancel
                                                </AlertDialogCancel>
                                                <AlertDialogAction
                                                    onClick={handleDelete}
                                                    disabled={isDeleting}
                                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                >
                                                    {isDeleting ? 'Deleting...' : 'Delete'}
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </div>

                                <div className="space-y-2 pt-4">
                                    <Link href={route('admin.testimonials.create')} className="block">
                                        <Button variant="outline" className="w-full justify-start">
                                            Create New Testimonial
                                        </Button>
                                    </Link>
                                    <Link href={route('admin.testimonials.index')} className="block">
                                        <Button variant="ghost" className="w-full justify-start">
                                            Back to Testimonials
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}