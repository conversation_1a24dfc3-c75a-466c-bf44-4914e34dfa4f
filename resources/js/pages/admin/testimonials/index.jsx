import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Star, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TestimonialsIndex({
    testimonials,
    filters,
    title,
    description,
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedRating, setSelectedRating] = useState(filters.rating || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [deletingId, setDeletingId] = useState(null);

    const handleSearch = () => {
        router.get(
            route('admin.testimonials.index'),
            {
                search: searchTerm,
                rating: selectedRating,
                status: selectedStatus,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedRating('');
        setSelectedStatus('');
        router.get(
            route('admin.testimonials.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (id) => {
        setDeletingId(id);
        router.delete(route('admin.testimonials.destroy', id), {
            onSuccess: () => {
                toast.success('Testimonial deleted successfully');
                setDeletingId(null);
            },
            onError: () => {
                toast.error('Failed to delete testimonial');
                setDeletingId(null);
            },
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const renderStars = (rating) => {
        return Array.from({ length: 5 }, (_, index) => (
            <Star
                key={index}
                className={`h-4 w-4 ${
                    index < rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                }`}
            />
        ));
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.testimonials.create')}>
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Testimonial
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Filters */}
                <Card>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <Label htmlFor="search">Search</Label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <Input
                                        id="search"
                                        placeholder="Search testimonials..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                        onKeyPress={(e) =>
                                            e.key === 'Enter' && handleSearch()
                                        }
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="rating">Rating</Label>
                                <Select
                                    value={selectedRating}
                                    onValueChange={setSelectedRating}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All ratings" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            All ratings
                                        </SelectItem>
                                        <SelectItem value="5">5 Stars</SelectItem>
                                        <SelectItem value="4">4 Stars</SelectItem>
                                        <SelectItem value="3">3 Stars</SelectItem>
                                        <SelectItem value="2">2 Stars</SelectItem>
                                        <SelectItem value="1">1 Star</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="status">Status</Label>
                                <Select
                                    value={selectedStatus}
                                    onValueChange={setSelectedStatus}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            All statuses
                                        </SelectItem>
                                        <SelectItem value="draft">
                                            Draft
                                        </SelectItem>
                                        <SelectItem value="published">
                                            Published
                                        </SelectItem>
                                        <SelectItem value="archived">
                                            Archived
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex items-end space-x-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    <Search className="mr-2 h-4 w-4" />
                                    Search
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={handleClearFilters}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Testimonials ({testimonials.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Avatar</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Location</TableHead>
                                    <TableHead>Rating</TableHead>
                                    <TableHead>Content</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead className="w-[120px]">
                                        Actions
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {testimonials.data.map((testimonial) => (
                                    <TableRow key={testimonial.id}>
                                        <TableCell>
                                            {testimonial.avatar ? (
                                                <img
                                                    src={`/storage/${testimonial.avatar}`}
                                                    alt={testimonial.name}
                                                    className="h-10 w-10 rounded-full object-cover"
                                                />
                                            ) : (
                                                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                    <span className="text-gray-500 text-sm font-medium">
                                                        {testimonial.name
                                                            .charAt(0)
                                                            .toUpperCase()}
                                                    </span>
                                                </div>
                                            )}
                                        </TableCell>
                                        <TableCell className="font-medium">
                                            {testimonial.name}
                                        </TableCell>
                                        <TableCell>
                                            {testimonial.location || 'N/A'}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center space-x-1">
                                                {renderStars(testimonial.rating)}
                                                <span className="ml-2 text-sm text-gray-600">
                                                    ({testimonial.rating})
                                                </span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="max-w-xs">
                                                <p className="truncate text-sm text-gray-600">
                                                    {testimonial.content.length >
                                                    50
                                                        ? `${testimonial.content.substring(0, 50)}...`
                                                        : testimonial.content}
                                                </p>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge
                                                className={getStatusColor(
                                                    testimonial.status,
                                                )}
                                            >
                                                {testimonial.status
                                                    .charAt(0)
                                                    .toUpperCase() +
                                                    testimonial.status.slice(1)}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            {formatDate(testimonial.created_at)}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center space-x-2">
                                                <Link
                                                    href={route(
                                                        'admin.testimonials.show',
                                                        testimonial.id,
                                                    )}
                                                >
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                    >
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <Link
                                                    href={route(
                                                        'admin.testimonials.edit',
                                                        testimonial.id,
                                                    )}
                                                >
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                    >
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-destructive hover:text-destructive"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>
                                                                Delete
                                                                Testimonial
                                                            </AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Are you sure you
                                                                want to delete
                                                                this testimonial?
                                                                This action
                                                                cannot be undone.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>
                                                                Cancel
                                                            </AlertDialogCancel>
                                                            <AlertDialogAction
                                                                onClick={() =>
                                                                    handleDelete(
                                                                        testimonial.id,
                                                                    )
                                                                }
                                                                disabled={
                                                                    deletingId ===
                                                                    testimonial.id
                                                                }
                                                            >
                                                                {deletingId ===
                                                                testimonial.id
                                                                    ? 'Deleting...'
                                                                    : 'Delete'}
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {testimonials.data.length === 0 && (
                            <div className="py-12 text-center">
                                <p className="text-gray-500">
                                    No testimonials found.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {testimonials.links && testimonials.links.length > 3 && (
                    <div className="flex justify-center">
                        <nav className="flex items-center space-x-2">
                            {testimonials.links.map((link, index) => {
                                if (!link.url) {
                                    return (
                                        <span
                                            key={index}
                                            className="px-3 py-2 text-sm text-gray-500"
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    );
                                }

                                return (
                                    <Link
                                        key={index}
                                        href={link.url}
                                        className={`px-3 py-2 text-sm rounded-md border ${
                                            link.active
                                                ? 'bg-primary text-primary-foreground border-primary'
                                                : 'bg-background hover:bg-muted border-border'
                                        }`}
                                        dangerouslySetInnerHTML={{
                                            __html: link.label,
                                        }}
                                    />
                                );
                            })}
                        </nav>
                    </div>
                )}
            </div>
        </AdminLayout>
    );
}