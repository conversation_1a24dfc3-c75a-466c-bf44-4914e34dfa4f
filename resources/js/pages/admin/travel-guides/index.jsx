import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TravelGuidesIndex({
    travelGuides,
    destinations,
    filters,
    title,
    description,
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedDestination, setSelectedDestination] = useState(
        filters.destination || '',
    );
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [deletingId, setDeletingId] = useState(null);

    const handleSearch = () => {
        router.get(
            route('admin.travel-guides.index'),
            {
                search: searchTerm,
                destination: selectedDestination,
                status: selectedStatus,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedDestination('');
        setSelectedStatus('');
        router.get(
            route('admin.travel-guides.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (id) => {
        setDeletingId(id);
        router.delete(route('admin.travel-guides.destroy', id), {
            onSuccess: () => {
                toast.success('Travel guide deleted successfully');
                setDeletingId(null);
            },
            onError: () => {
                toast.error('Failed to delete travel guide');
                setDeletingId(null);
            },
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.travel-guides.create')}>
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Travel Guide
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <Label htmlFor="search">Search</Label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <Input
                                        id="search"
                                        placeholder="Search travel guides..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                        onKeyPress={(e) =>
                                            e.key === 'Enter' && handleSearch()
                                        }
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="destination">Destination</Label>
                                <Select
                                    value={selectedDestination}
                                    onValueChange={setSelectedDestination}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All destinations" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            All destinations
                                        </SelectItem>
                                        {destinations.map((destination) => (
                                            <SelectItem
                                                key={destination.id}
                                                value={destination.id.toString()}
                                            >
                                                {destination.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="status">Status</Label>
                                <Select
                                    value={selectedStatus}
                                    onValueChange={setSelectedStatus}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            All statuses
                                        </SelectItem>
                                        <SelectItem value="draft">
                                            Draft
                                        </SelectItem>
                                        <SelectItem value="published">
                                            Published
                                        </SelectItem>
                                        <SelectItem value="archived">
                                            Archived
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label>&nbsp;</Label>
                                <div className="flex space-x-2">
                                    <Button
                                        onClick={handleSearch}
                                        className="flex-1"
                                    >
                                        <Search className="mr-2 h-4 w-4" />
                                        Search
                                    </Button>
                                    <Button
                                        onClick={handleClearFilters}
                                        variant="outline"
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Travel Guides Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Travel Guides ({travelGuides.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Title</TableHead>
                                        <TableHead>Destination</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {travelGuides.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={6}
                                                className="py-8 text-center"
                                            >
                                                <div className="text-muted-foreground">
                                                    No travel guides found.
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        travelGuides.data.map((guide) => (
                                            <TableRow key={guide.id}>
                                                <TableCell>
                                                    {guide.image ? (
                                                        <img
                                                            src={`/storage/${guide.image}`}
                                                            alt={guide.title}
                                                            className="h-12 w-12 rounded object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex h-12 w-12 items-center justify-center rounded bg-gray-200">
                                                            <span className="text-xs text-gray-400">
                                                                No image
                                                            </span>
                                                        </div>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="font-medium">
                                                        {guide.title}
                                                    </div>
                                                    <div className="text-muted-foreground max-w-xs truncate text-sm">
                                                        {guide.content?.substring(
                                                            0,
                                                            100,
                                                        )}
                                                        ...
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {guide.destination ? (
                                                        <span className="text-sm">
                                                            {
                                                                guide
                                                                    .destination
                                                                    .name
                                                            }
                                                        </span>
                                                    ) : (
                                                        <span className="text-muted-foreground text-sm">
                                                            No destination
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={getStatusColor(
                                                            guide.status,
                                                        )}
                                                    >
                                                        {guide.status
                                                            .charAt(0)
                                                            .toUpperCase() +
                                                            guide.status.slice(
                                                                1,
                                                            )}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground text-sm">
                                                    {formatDate(
                                                        guide.created_at,
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <Link
                                                            href={route(
                                                                'admin.travel-guides.show',
                                                                guide.id,
                                                            )}
                                                        >
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                            >
                                                                <Eye className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <Link
                                                            href={route(
                                                                'admin.travel-guides.edit',
                                                                guide.id,
                                                            )}
                                                        >
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Are you
                                                                        sure?
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                        This
                                                                        will
                                                                        permanently
                                                                        delete
                                                                        the
                                                                        travel
                                                                        guide.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDelete(
                                                                                guide.id,
                                                                            )
                                                                        }
                                                                        disabled={
                                                                            deletingId ===
                                                                            guide.id
                                                                        }
                                                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                                    >
                                                                        {deletingId ===
                                                                        guide.id
                                                                            ? 'Deleting...'
                                                                            : 'Delete'}
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {travelGuides.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-between">
                                <div className="text-muted-foreground text-sm">
                                    Showing {travelGuides.from} to{' '}
                                    {travelGuides.to} of {travelGuides.total}{' '}
                                    results
                                </div>
                                <div className="flex items-center space-x-2">
                                    {travelGuides.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`rounded px-3 py-1 text-sm ${
                                                link.active
                                                    ? 'bg-primary text-primary-foreground'
                                                    : link.url
                                                      ? 'bg-background hover:bg-accent border'
                                                      : 'text-muted-foreground cursor-not-allowed'
                                            }`}
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
