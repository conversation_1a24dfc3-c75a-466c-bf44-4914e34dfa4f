import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@admin/components/ui/card.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

export default function ShowTravelGuide({ travelGuide, title, description }) {
    const handleDelete = () => {
        router.delete(route('admin.travel-guides.destroy', travelGuide.id), {
            onSuccess: () => {
                toast.success('Travel guide deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete travel guide');
            },
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AdminLayout toolbar={false}>
            <Head title={title} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('admin.travel-guides.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h3 className="text-xl font-bold tracking-tight">
                                {title}
                            </h3>
                            <p className="text-muted-foreground">
                                {description}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Link
                            href={route(
                                'admin.travel-guides.edit',
                                travelGuide.id,
                            )}
                        >
                            <Button>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </Button>
                        </Link>
                        <AlertDialog>
                            <AlertDialogTrigger asChild>
                                <Button variant="destructive">
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle>
                                        Are you absolutely sure?
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                        This action cannot be undone. This will
                                        permanently delete the travel guide and
                                        remove all associated data.
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>
                                        Cancel
                                    </AlertDialogCancel>
                                    <AlertDialogAction onClick={handleDelete}>
                                        Delete
                                    </AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Travel Guide Details */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-start justify-between">
                                    <CardTitle className="text-2xl">
                                        {travelGuide.title}
                                    </CardTitle>
                                    <Badge
                                        className={getStatusColor(
                                            travelGuide.status,
                                        )}
                                    >
                                        {travelGuide.status
                                            .charAt(0)
                                            .toUpperCase() +
                                            travelGuide.status.slice(1)}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {travelGuide.image && (
                                    <div>
                                        <img
                                            src={`/storage/${travelGuide.image}`}
                                            alt={travelGuide.title}
                                            className="h-64 w-full rounded-lg border object-cover"
                                        />
                                    </div>
                                )}
                                <div>
                                    <h3 className="mb-2 text-lg font-semibold">
                                        Content
                                    </h3>
                                    <div className="prose max-w-none">
                                        <p className="text-muted-foreground whitespace-pre-wrap">
                                            {travelGuide.content}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Travel Guide Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Travel Guide Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h4 className="text-muted-foreground text-sm font-medium">
                                        Status
                                    </h4>
                                    <Badge
                                        className={getStatusColor(
                                            travelGuide.status,
                                        )}
                                    >
                                        {travelGuide.status
                                            .charAt(0)
                                            .toUpperCase() +
                                            travelGuide.status.slice(1)}
                                    </Badge>
                                </div>

                                {travelGuide.destination && (
                                    <div>
                                        <h4 className="text-muted-foreground text-sm font-medium">
                                            Destination
                                        </h4>
                                        <p className="text-sm">
                                            {travelGuide.destination.name}
                                        </p>
                                    </div>
                                )}

                                <div>
                                    <h4 className="text-muted-foreground text-sm font-medium">
                                        Created
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(travelGuide.created_at)}
                                    </p>
                                </div>

                                <div>
                                    <h4 className="text-muted-foreground text-sm font-medium">
                                        Last Updated
                                    </h4>
                                    <p className="text-sm">
                                        {formatDate(travelGuide.updated_at)}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="flex flex-col space-y-2">
                                <Link
                                    href={route(
                                        'admin.travel-guides.edit',
                                        travelGuide.id,
                                    )}
                                >
                                    <Button className="w-full">
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Travel Guide
                                    </Button>
                                </Link>
                                <Link href={route('admin.travel-guides.index')}>
                                    <Button
                                        variant="outline"
                                        className="w-full"
                                    >
                                        Back to Travel Guides
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
