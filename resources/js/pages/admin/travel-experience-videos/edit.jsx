import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle } from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function EditTravelExperienceVideo({ videoItem, title, description }) {
    const { data, setData, post, processing, errors } = useForm({
        title: videoItem.title || '',
        traveler: videoItem.traveler || '',
        country: videoItem.country || '',
        thumbnail: null,
        video: null,
        _method: 'PUT',
    });

    const [thumbPreview, setThumbPreview] = useState(videoItem.thumbnail ? `/storage/${videoItem.thumbnail}` : null);

    const handleThumbChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('thumbnail', file);
            const reader = new FileReader();
            reader.onload = (ev) => setThumbPreview(ev.target.result);
            reader.readAsDataURL(file);
        }
    };

    const handleVideoChange = (e) => {
        const file = e.target.files[0];
        if (file) setData('video', file);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.travel-experience-videos.update', videoItem.id), {
            onSuccess: () => toast.success('Updated successfully'),
            onError: () => toast.error('Failed to update'),
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.travel-experience-videos.index')}>
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="mr-2 h-4 w-4" /> Back
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <div className="space-y-6 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Video Details</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="title">Title *</Label>
                                    <Input id="title" value={data.title} onChange={(e) => setData('title', e.target.value)} className={errors.title ? 'border-destructive' : ''} />
                                    {errors.title && <p className="text-destructive text-sm">{errors.title}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="traveler">Traveler *</Label>
                                    <Input id="traveler" value={data.traveler} onChange={(e) => setData('traveler', e.target.value)} className={errors.traveler ? 'border-destructive' : ''} />
                                    {errors.traveler && <p className="text-destructive text-sm">{errors.traveler}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="country">Country *</Label>
                                    <Input id="country" value={data.country} onChange={(e) => setData('country', e.target.value)} className={errors.country ? 'border-destructive' : ''} />
                                    {errors.country && <p className="text-destructive text-sm">{errors.country}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Media</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="thumbnail">Thumbnail</Label>
                                    <Input id="thumbnail" type="file" accept="image/*" onChange={handleThumbChange} className={errors.thumbnail ? 'border-destructive' : ''} />
                                    {errors.thumbnail && <p className="text-destructive text-sm">{errors.thumbnail}</p>}
                                </div>
                                {thumbPreview && (
                                    <div className="mt-2">
                                        <img src={thumbPreview} alt="Preview" className="h-40 max-w-full rounded border object-cover" />
                                    </div>
                                )}
                                <div className="space-y-2">
                                    <Label htmlFor="video">Video file</Label>
                                    <Input id="video" type="file" accept="video/*" onChange={handleVideoChange} className={errors.video ? 'border-destructive' : ''} />
                                    {errors.video && <p className="text-destructive text-sm">{errors.video}</p>}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <div className="space-y-6">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex flex-col space-y-2">
                                    <Button type="submit" disabled={processing}>
                                        <Save className="mr-2 h-4 w-4" /> {processing ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                    <Link href={route('admin.travel-experience-videos.index')}>
                                        <Button variant="ghost" className="w-full">Cancel</Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </form>
        </AdminLayout>
    );
}
