import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle } from '@admin/components/ui/card.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { Eye, Edit, Plus, Search, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function TravelExperienceVideosIndex({ videos, filters, title, description }) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [deletingId, setDeletingId] = useState(null);

    const handleSearch = () => {
        router.get(route('admin.travel-experience-videos.index'), { search: searchTerm }, { preserveState: true, replace: true });
    };

    const handleClear = () => {
        setSearchTerm('');
        router.get(route('admin.travel-experience-videos.index'), {}, { preserveState: true, replace: true });
    };

    const handleDelete = (id) => {
        setDeletingId(id);
        router.delete(route('admin.travel-experience-videos.destroy', id), {
            onSuccess: () => {
                toast.success('Deleted successfully');
                setDeletingId(null);
            },
            onError: () => {
                toast.error('Failed to delete');
                setDeletingId(null);
            },
        });
    };

    const formatDate = (dateString) => new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Link href={route('admin.travel-experience-videos.create')}>
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Video
                    </Button>
                </Link>
            }
        >
            <Head title={title} />

            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <Label htmlFor="search">Search</Label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <Input id="search" placeholder="Search by title, traveler, country..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}
                                           onKeyPress={(e) => e.key === 'Enter' && handleSearch()} className="pl-10" />
                                </div>
                            </div>
                            <div className="flex items-end space-x-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    <Search className="mr-2 h-4 w-4" />Search
                                </Button>
                                <Button variant="outline" onClick={handleClear}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Travel Experience Videos ({videos.total})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Thumbnail</TableHead>
                                        <TableHead>Title</TableHead>
                                        <TableHead>Traveler</TableHead>
                                        <TableHead>Country</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {videos.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} className="py-8 text-center">
                                                <div className="text-muted-foreground">No videos found.</div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        videos.data.map((item) => (
                                            <TableRow key={item.id}>
                                                <TableCell>
                                                    {item.thumbnail ? (
                                                        <img src={`/storage/${item.thumbnail}`} alt={item.title} className="h-12 w-12 rounded object-cover" />
                                                    ) : (
                                                        <div className="flex h-12 w-12 items-center justify-center rounded bg-gray-200 text-xs text-gray-500">No image</div>
                                                    )}
                                                </TableCell>
                                                <TableCell className="font-medium">{item.title}</TableCell>
                                                <TableCell>{item.traveler}</TableCell>
                                                <TableCell>{item.country}</TableCell>
                                                <TableCell className="text-muted-foreground text-sm">{formatDate(item.created_at)}</TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <Link href={route('admin.travel-experience-videos.show', item.id)}>
                                                            <Button variant="ghost" size="sm"><Eye className="h-4 w-4" /></Button>
                                                        </Link>
                                                        <Link href={route('admin.travel-experience-videos.edit', item.id)}>
                                                            <Button variant="ghost" size="sm"><Edit className="h-4 w-4" /></Button>
                                                        </Link>
                                                        <Button variant="ghost" size="sm" onClick={() => handleDelete(item.id)} disabled={deletingId === item.id}>
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {videos.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-between">
                                <div className="text-muted-foreground text-sm">
                                    Showing {videos.from} to {videos.to} of {videos.total} results
                                </div>
                                <div className="flex items-center space-x-2">
                                    {videos.links.map((link, index) => (
                                        <Link key={index} href={link.url || '#'}
                                              className={`rounded px-3 py-1 text-sm ${link.active ? 'bg-primary text-primary-foreground' : link.url ? 'bg-background hover:bg-accent border' : 'text-muted-foreground cursor-not-allowed'}`}
                                              dangerouslySetInnerHTML={{ __html: link.label }} />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
