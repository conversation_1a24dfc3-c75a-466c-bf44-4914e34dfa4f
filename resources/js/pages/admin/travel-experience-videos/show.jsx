import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle } from '@admin/components/ui/card.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

export default function ShowTravelExperienceVideo({ videoItem, title, description }) {
    const handleDelete = () => {
        router.delete(route('admin.travel-experience-videos.destroy', videoItem.id), {
            onSuccess: () => toast.success('Deleted successfully'),
            onError: () => toast.error('Failed to delete'),
        });
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <div className="flex gap-2">
                    <Link href={route('admin.travel-experience-videos.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" /> Back
                        </Button>
                    </Link>
                    <Link href={route('admin.travel-experience-videos.edit', videoItem.id)}>
                        <Button size="sm"><Edit className="mr-2 h-4 w-4" /> Edit</Button>
                    </Link>
                    <Button size="sm" variant="destructive" onClick={handleDelete}><Trash2 className="mr-2 h-4 w-4" /> Delete</Button>
                </div>
            }
        >
            <Head title={title} />

            <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                <div className="space-y-6 lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Details</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <div className="text-sm text-muted-foreground">Title</div>
                                    <div className="text-lg font-medium">{videoItem.title}</div>
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <div className="text-sm text-muted-foreground">Traveler</div>
                                        <div>{videoItem.traveler}</div>
                                    </div>
                                    <div>
                                        <div className="text-sm text-muted-foreground">Country</div>
                                        <div>{videoItem.country}</div>
                                    </div>
                                </div>
                                <div>
                                    <div className="text-sm text-muted-foreground">Video</div>
                                    {videoItem.video ? (
                                        <video controls className="mt-2 w-full rounded border">
                                            <source src={`/storage/${videoItem.video}`} />
                                            Your browser does not support the video tag.
                                        </video>
                                    ) : (
                                        <div className="text-muted-foreground">No video uploaded.</div>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Thumbnail</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {videoItem.thumbnail ? (
                                <img src={`/storage/${videoItem.thumbnail}`} alt={videoItem.title} className="rounded border object-cover" />
                            ) : (
                                <div className="text-muted-foreground">No thumbnail.</div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AdminLayout>
    );
}
