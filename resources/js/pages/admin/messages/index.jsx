import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from '@admin/components/ui/tabs.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Eye,
    Mail,
    MessageCircle,
    Search,
    Trash2,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function MessagesIndex({
    contactMessages,
    askedQuestions,
    activeTab,
    filters,
    title,
    description,
    ...props
}) {
    const [contactSearchTerm, setContactSearchTerm] = useState(
        filters.contact_search || '',
    );
    const [questionsSearchTerm, setQuestionsSearchTerm] = useState(
        filters.questions_search || '',
    );
    const [viewingContact, setViewingContact] = useState(null);
    const [viewingQuestion, setViewingQuestion] = useState(null);
    const [isContactViewOpen, setIsContactViewOpen] = useState(false);
    const [isQuestionViewOpen, setIsQuestionViewOpen] = useState(false);

    const handleTabChange = (tab) => {
        router.get(
            route('admin.messages.index'),
            { tab },
            { preserveState: false },
        );
    };

    const handleContactSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.messages.index'),
            {
                tab: 'contact',
                contact_search: contactSearchTerm,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleQuestionsSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.messages.index'),
            {
                tab: 'questions',
                questions_search: questionsSearchTerm,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearContactSearch = () => {
        setContactSearchTerm('');
        router.get(
            route('admin.messages.index'),
            { tab: 'contact' },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearQuestionsSearch = () => {
        setQuestionsSearchTerm('');
        router.get(
            route('admin.messages.index'),
            { tab: 'questions' },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleViewContact = (contact) => {
        setViewingContact(contact);
        setIsContactViewOpen(true);
    };

    const handleViewQuestion = (question) => {
        setViewingQuestion(question);
        setIsQuestionViewOpen(true);
    };

    const handleDeleteContact = (contact) => {
        router.delete(route('admin.messages.destroyContact', contact.id), {
            onSuccess: () => {
                toast.success('Contact message deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete contact message errors:', errors);
                toast.error('Failed to delete contact message');
            },
        });
    };

    const handleDeleteQuestion = (question) => {
        router.delete(route('admin.messages.destroyQuestion', question.id), {
            onSuccess: () => {
                toast.success('Asked question deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete asked question errors:', errors);
                toast.error('Failed to delete asked question');
            },
        });
    };

    return (
        <AdminLayout title={title} description={description}>
            <Head title={title} />

            <div className="pb-8">
                <Card>
                    <CardContent className="p-6">
                        <Tabs value={activeTab} onValueChange={handleTabChange}>
                            <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger
                                    value="contact"
                                    className="flex items-center gap-2"
                                >
                                    <Mail className="h-4 w-4" />
                                    Contact Messages
                                    {contactMessages.total > 0 && (
                                        <Badge variant="secondary">
                                            {contactMessages.total}
                                        </Badge>
                                    )}
                                </TabsTrigger>
                                <TabsTrigger
                                    value="questions"
                                    className="flex items-center gap-2"
                                >
                                    <MessageCircle className="h-4 w-4" />
                                    Asked Questions
                                    {askedQuestions.total > 0 && (
                                        <Badge variant="secondary">
                                            {askedQuestions.total}
                                        </Badge>
                                    )}
                                </TabsTrigger>
                            </TabsList>

                            {/* Contact Messages Tab */}
                            <TabsContent value="contact" className="mt-6">
                                {/* Search */}
                                <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                    <form
                                        onSubmit={handleContactSearch}
                                        className="flex gap-2"
                                    >
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                            <Input
                                                type="text"
                                                placeholder="Search contact messages..."
                                                value={contactSearchTerm}
                                                onChange={(e) =>
                                                    setContactSearchTerm(
                                                        e.target.value,
                                                    )
                                                }
                                                className="w-64 pl-10"
                                            />
                                        </div>
                                        <Button type="submit" variant="outline">
                                            Search
                                        </Button>
                                        {filters.contact_search && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={
                                                    handleClearContactSearch
                                                }
                                            >
                                                Clear
                                            </Button>
                                        )}
                                    </form>
                                </div>

                                {/* Contact Messages Table */}
                                <div className="overflow-hidden rounded-lg border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Name</TableHead>
                                                <TableHead>Email</TableHead>
                                                <TableHead>Phone</TableHead>
                                                <TableHead>Subject</TableHead>
                                                <TableHead>Date</TableHead>
                                                <TableHead className="text-right">
                                                    Actions
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {contactMessages.data.length ===
                                            0 ? (
                                                <TableRow>
                                                    <TableCell
                                                        colSpan={6}
                                                        className="text-muted-foreground py-8 text-center"
                                                    >
                                                        No contact messages
                                                        found.
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                contactMessages.data.map(
                                                    (contact) => (
                                                        <TableRow
                                                            key={contact.id}
                                                        >
                                                            <TableCell className="font-medium">
                                                                {
                                                                    contact.first_name
                                                                }{' '}
                                                                {
                                                                    contact.last_name
                                                                }
                                                            </TableCell>
                                                            <TableCell>
                                                                {contact.email}
                                                            </TableCell>
                                                            <TableCell>
                                                                {contact.phone ||
                                                                    '-'}
                                                            </TableCell>
                                                            <TableCell className="max-w-xs">
                                                                <div
                                                                    className="truncate"
                                                                    title={
                                                                        contact.subject
                                                                    }
                                                                >
                                                                    {
                                                                        contact.subject
                                                                    }
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                {new Date(
                                                                    contact.created_at,
                                                                ).toLocaleDateString()}
                                                            </TableCell>
                                                            <TableCell className="text-right">
                                                                <div className="flex items-center justify-end gap-2">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() =>
                                                                            handleViewContact(
                                                                                contact,
                                                                            )
                                                                        }
                                                                    >
                                                                        <Eye className="h-4 w-4" />
                                                                    </Button>
                                                                    <AlertDialog>
                                                                        <AlertDialogTrigger
                                                                            asChild
                                                                        >
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                className="text-red-600 hover:text-red-700"
                                                                            >
                                                                                <Trash2 className="h-4 w-4" />
                                                                            </Button>
                                                                        </AlertDialogTrigger>
                                                                        <AlertDialogContent>
                                                                            <AlertDialogHeader>
                                                                                <AlertDialogTitle>
                                                                                    Delete
                                                                                    Contact
                                                                                    Message
                                                                                </AlertDialogTitle>
                                                                                <AlertDialogDescription>
                                                                                    Are
                                                                                    you
                                                                                    sure
                                                                                    you
                                                                                    want
                                                                                    to
                                                                                    delete
                                                                                    this
                                                                                    contact
                                                                                    message?
                                                                                    This
                                                                                    action
                                                                                    cannot
                                                                                    be
                                                                                    undone.
                                                                                </AlertDialogDescription>
                                                                            </AlertDialogHeader>
                                                                            <AlertDialogFooter>
                                                                                <AlertDialogCancel>
                                                                                    Cancel
                                                                                </AlertDialogCancel>
                                                                                <AlertDialogAction
                                                                                    onClick={() =>
                                                                                        handleDeleteContact(
                                                                                            contact,
                                                                                        )
                                                                                    }
                                                                                    className="bg-red-600 hover:bg-red-700"
                                                                                >
                                                                                    Delete
                                                                                </AlertDialogAction>
                                                                            </AlertDialogFooter>
                                                                        </AlertDialogContent>
                                                                    </AlertDialog>
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    ),
                                                )
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Contact Messages Pagination */}
                                {contactMessages.total >
                                    contactMessages.per_page && (
                                    <div className="mt-6 flex items-center justify-between">
                                        <div className="text-sm text-gray-700">
                                            Showing {contactMessages.from} to{' '}
                                            {contactMessages.to} of{' '}
                                            {contactMessages.total} results
                                        </div>
                                        <div className="flex gap-2">
                                            {contactMessages.prev_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            contactMessages.prev_page_url,
                                                        )
                                                    }
                                                >
                                                    <ChevronLeft className="mr-1 h-4 w-4" />
                                                    Previous
                                                </Button>
                                            )}
                                            {contactMessages.next_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            contactMessages.next_page_url,
                                                        )
                                                    }
                                                >
                                                    Next
                                                    <ChevronRight className="ml-1 h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </TabsContent>

                            {/* Asked Questions Tab */}
                            <TabsContent value="questions" className="mt-6">
                                {/* Search */}
                                <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                    <form
                                        onSubmit={handleQuestionsSearch}
                                        className="flex gap-2"
                                    >
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                            <Input
                                                type="text"
                                                placeholder="Search asked questions..."
                                                value={questionsSearchTerm}
                                                onChange={(e) =>
                                                    setQuestionsSearchTerm(
                                                        e.target.value,
                                                    )
                                                }
                                                className="w-64 pl-10"
                                            />
                                        </div>
                                        <Button type="submit" variant="outline">
                                            Search
                                        </Button>
                                        {filters.questions_search && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={
                                                    handleClearQuestionsSearch
                                                }
                                            >
                                                Clear
                                            </Button>
                                        )}
                                    </form>
                                </div>

                                {/* Asked Questions Table */}
                                <div className="overflow-hidden rounded-lg border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Name</TableHead>
                                                <TableHead>Email</TableHead>
                                                <TableHead>Phone</TableHead>
                                                <TableHead>Message</TableHead>
                                                <TableHead>Date</TableHead>
                                                <TableHead className="text-right">
                                                    Actions
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {askedQuestions.data.length ===
                                            0 ? (
                                                <TableRow>
                                                    <TableCell
                                                        colSpan={6}
                                                        className="text-muted-foreground py-8 text-center"
                                                    >
                                                        No asked questions
                                                        found.
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                askedQuestions.data.map(
                                                    (question) => (
                                                        <TableRow
                                                            key={question.id}
                                                        >
                                                            <TableCell className="font-medium">
                                                                {question.name}
                                                            </TableCell>
                                                            <TableCell>
                                                                {question.email}
                                                            </TableCell>
                                                            <TableCell>
                                                                {question.phone ||
                                                                    '-'}
                                                            </TableCell>
                                                            <TableCell className="max-w-md">
                                                                <div
                                                                    className="truncate"
                                                                    title={
                                                                        question.message
                                                                    }
                                                                >
                                                                    {
                                                                        question.message
                                                                    }
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                {new Date(
                                                                    question.created_at,
                                                                ).toLocaleDateString()}
                                                            </TableCell>
                                                            <TableCell className="text-right">
                                                                <div className="flex items-center justify-end gap-2">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() =>
                                                                            handleViewQuestion(
                                                                                question,
                                                                            )
                                                                        }
                                                                    >
                                                                        <Eye className="h-4 w-4" />
                                                                    </Button>
                                                                    <AlertDialog>
                                                                        <AlertDialogTrigger
                                                                            asChild
                                                                        >
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                className="text-red-600 hover:text-red-700"
                                                                            >
                                                                                <Trash2 className="h-4 w-4" />
                                                                            </Button>
                                                                        </AlertDialogTrigger>
                                                                        <AlertDialogContent>
                                                                            <AlertDialogHeader>
                                                                                <AlertDialogTitle>
                                                                                    Delete
                                                                                    Asked
                                                                                    Question
                                                                                </AlertDialogTitle>
                                                                                <AlertDialogDescription>
                                                                                    Are
                                                                                    you
                                                                                    sure
                                                                                    you
                                                                                    want
                                                                                    to
                                                                                    delete
                                                                                    this
                                                                                    asked
                                                                                    question?
                                                                                    This
                                                                                    action
                                                                                    cannot
                                                                                    be
                                                                                    undone.
                                                                                </AlertDialogDescription>
                                                                            </AlertDialogHeader>
                                                                            <AlertDialogFooter>
                                                                                <AlertDialogCancel>
                                                                                    Cancel
                                                                                </AlertDialogCancel>
                                                                                <AlertDialogAction
                                                                                    onClick={() =>
                                                                                        handleDeleteQuestion(
                                                                                            question,
                                                                                        )
                                                                                    }
                                                                                    className="bg-red-600 hover:bg-red-700"
                                                                                >
                                                                                    Delete
                                                                                </AlertDialogAction>
                                                                            </AlertDialogFooter>
                                                                        </AlertDialogContent>
                                                                    </AlertDialog>
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    ),
                                                )
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Asked Questions Pagination */}
                                {askedQuestions.total >
                                    askedQuestions.per_page && (
                                    <div className="mt-6 flex items-center justify-between">
                                        <div className="text-sm text-gray-700">
                                            Showing {askedQuestions.from} to{' '}
                                            {askedQuestions.to} of{' '}
                                            {askedQuestions.total} results
                                        </div>
                                        <div className="flex gap-2">
                                            {askedQuestions.prev_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            askedQuestions.prev_page_url,
                                                        )
                                                    }
                                                >
                                                    <ChevronLeft className="mr-1 h-4 w-4" />
                                                    Previous
                                                </Button>
                                            )}
                                            {askedQuestions.next_page_url && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() =>
                                                        router.get(
                                                            askedQuestions.next_page_url,
                                                        )
                                                    }
                                                >
                                                    Next
                                                    <ChevronRight className="ml-1 h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>
            </div>

            {/* Contact Message View Modal */}
            <Dialog
                open={isContactViewOpen}
                onOpenChange={setIsContactViewOpen}
            >
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <Mail className="h-5 w-5" />
                            Contact Message Details
                        </DialogTitle>
                        <DialogDescription>
                            View the complete contact message information.
                        </DialogDescription>
                    </DialogHeader>
                    {viewingContact && (
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        First Name
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingContact.first_name}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        Last Name
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingContact.last_name}
                                    </p>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        Email
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingContact.email}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        Phone
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingContact.phone || 'Not provided'}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Subject
                                </Label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {viewingContact.subject}
                                </p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Message
                                </Label>
                                <div className="mt-1 rounded-md bg-gray-50 p-3">
                                    <p className="whitespace-pre-wrap text-sm text-gray-900">
                                        {viewingContact.message}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Date Received
                                </Label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(
                                        viewingContact.created_at,
                                    ).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>

            {/* Asked Question View Modal */}
            <Dialog
                open={isQuestionViewOpen}
                onOpenChange={setIsQuestionViewOpen}
            >
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <MessageCircle className="h-5 w-5" />
                            Asked Question Details
                        </DialogTitle>
                        <DialogDescription>
                            View the complete asked question information.
                        </DialogDescription>
                    </DialogHeader>
                    {viewingQuestion && (
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        Name
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingQuestion.name}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                        Email
                                    </Label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        {viewingQuestion.email}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Phone
                                </Label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {viewingQuestion.phone || 'Not provided'}
                                </p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Question/Message
                                </Label>
                                <div className="mt-1 rounded-md bg-gray-50 p-3">
                                    <p className="whitespace-pre-wrap text-sm text-gray-900">
                                        {viewingQuestion.message}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700">
                                    Date Asked
                                </Label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(
                                        viewingQuestion.created_at,
                                    ).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
