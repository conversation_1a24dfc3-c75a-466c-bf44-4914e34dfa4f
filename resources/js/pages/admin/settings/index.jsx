import { Button } from '@admin/components/ui/button.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from '@admin/components/ui/tabs.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, useForm } from '@inertiajs/react';
import { useMemo } from 'react';

export default function SettingsIndex({ title, description, settings }) {
    const groups = useMemo(
        () => ({
            general: {
                label: 'General',
                fields: [
                    { key: 'business_name', label: 'Business Name' },
                    { key: 'office_time', label: 'Office Time' },
                    { key: 'address_short', label: 'Short Address' },
                    {
                        key: 'address_full',
                        label: 'Full Address',
                        textarea: true,
                    },
                    { key: 'email', label: 'Email' },
                    { key: 'phone', label: 'Phone' },
                    { key: 'whatsapp', label: 'WhatsApp' },
                ],
            },
            social: {
                label: 'Social',
                fields: [
                    { key: 'facebook_link', label: 'Facebook URL' },
                    { key: 'twitter_link', label: 'Twitter URL' },
                    { key: 'instagram_link', label: 'Instagram URL' },
                ],
            },
            contact: {
                label: 'Contact',
                fields: [
                    { key: 'email_address_1', label: 'Email Address 1' },
                    { key: 'email_address_2', label: 'Email Address 2' },
                    { key: 'phone_number_1', label: 'Phone Number 1' },
                    { key: 'phone_number_2', label: 'Phone Number 2' },
                    {
                        key: 'head_office_address',
                        label: 'Head Office Address',
                        textarea: true,
                    },
                    {
                        key: 'map_embed_link',
                        label: 'Map Embed Link',
                        textarea: true,
                    },
                    { key: 'receiving_email', label: 'Receiving Email' },
                ],
            },
            email: {
                label: 'Email (SMTP)',
                fields: [
                    { key: 'smtp_host', label: 'SMTP Host' },
                    { key: 'smtp_port', label: 'SMTP Port', type: 'number' },
                    { key: 'smtp_username', label: 'SMTP Username' },
                    {
                        key: 'smtp_password',
                        label: 'SMTP Password',
                        type: 'password',
                    },
                    { key: 'smtp_encryption', label: 'Encryption (tls/ssl)' },
                ],
            },
            payment: {
                label: 'Payment',
                fields: [
                    {
                        key: 'enable_cash_on_arrival',
                        label: 'Enable Cash on Arrival',
                        type: 'checkbox',
                    },
                    {
                        key: 'enable_bank_transfer',
                        label: 'Enable Bank Transfer',
                        type: 'checkbox',
                    },
                    { key: 'bank_name', label: 'Bank Name' },
                    { key: 'bank_account_name', label: 'Bank Account Name' },
                    { key: 'bank_account_number', label: 'Bank Account Number' },
                    { key: 'bank_swift_code', label: 'Bank SWIFT Code' },
                    {
                        key: 'payment_instructions',
                        label: 'Payment Instructions',
                        textarea: true,
                    },
                ],
            },
            footer: {
                label: 'Footer Links',
                fields: [
                    {
                        key: 'footer_useful_links',
                        label: 'Useful Links (Label|URL per line)',
                        textarea: true,
                        isLinks: true,
                    },
                    {
                        key: 'footer_company_links',
                        label: 'Company Links (Label|URL per line)',
                        textarea: true,
                        isLinks: true,
                    },
                ],
            },
        }),
        [],
    );

    return (
        <AdminLayout title={title} description={description}>
            <Head title={title} />
            <div className="space-y-6">
                <Tabs defaultValue="general">
                    <TabsList>
                        {Object.entries(groups).map(([key, cfg]) => (
                            <TabsTrigger key={key} value={key}>
                                {cfg.label}
                            </TabsTrigger>
                        ))}
                    </TabsList>

                    {Object.entries(groups).map(([groupKey, cfg]) => (
                        <TabsContent key={groupKey} value={groupKey}>
                            <GroupForm
                                groupKey={groupKey}
                                config={cfg}
                                initial={settings?.[groupKey] || {}}
                            />
                        </TabsContent>
                    ))}
                </Tabs>
            </div>
        </AdminLayout>
    );
}

function GroupForm({ groupKey, config, initial }) {
    const initialData = useMemo(() => {
        const data = {};
        (config.fields || []).forEach((f) => {
            let v = initial?.[f.key] ?? '';
            if (f.isLinks && Array.isArray(v)) {
                v = v.map((it) => `${it.label}|${it.url}`).join('\n');
            }
            data[f.key] = v;
        });
        return data;
    }, [config.fields, initial]);

    const { data, setData, put, processing, errors, reset } =
        useForm(initialData);

    const onSubmit = (e) => {
        e.preventDefault();
        put(route('admin.settings.updateGroup', groupKey), {
            preserveScroll: true,
        });
    };

    return (
        <form onSubmit={onSubmit} className="max-w-3xl space-y-4">
            {(config.fields || []).map((field) => (
                <div key={field.key} className="space-y-2">
                    {field.type === 'checkbox' ? (
                        <div className="flex items-center space-x-2">
                            <input
                                id={`${groupKey}-${field.key}`}
                                type="checkbox"
                                checked={data[field.key] === '1' || data[field.key] === true}
                                onChange={(e) => setData(field.key, e.target.checked ? '1' : '0')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <Label htmlFor={`${groupKey}-${field.key}`} className="text-sm font-medium text-gray-700">
                                {field.label}
                            </Label>
                        </div>
                    ) : (
                        <>
                            <Label htmlFor={`${groupKey}-${field.key}`}>
                                {field.label}
                            </Label>
                            {field.textarea ? (
                                <Textarea
                                    id={`${groupKey}-${field.key}`}
                                    value={data[field.key] ?? ''}
                                    onChange={(e) => setData(field.key, e.target.value)}
                                    rows={field.isLinks ? 6 : 4}
                                />
                            ) : (
                                <Input
                                    id={`${groupKey}-${field.key}`}
                                    type={field.type || 'text'}
                                    value={data[field.key] ?? ''}
                                    onChange={(e) => setData(field.key, e.target.value)}
                                />
                            )}
                        </>
                     )}
                    {errors?.[field.key] && (
                        <p className="text-destructive text-sm">
                            {errors[field.key]}
                        </p>
                    )}
                </div>
            ))}
            <div>
                <Button type="submit" disabled={processing}>
                    Save {config.label}
                </Button>
            </div>
        </form>
    );
}
