import { Button } from '@admin/components/ui/button.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
} from '@admin/components/ui/collapsible.jsx';
import { Card, CardContent, CardHeader, CardTitle } from '@admin/components/ui/card.jsx';
import { ChevronDown, ChevronRight } from 'lucide-react';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, useForm } from '@inertiajs/react';
import { useMemo, useState } from 'react';

// GroupForm component for handling individual setting groups
function GroupForm({ groupKey, config, initial }) {
    const initialData = useMemo(() => {
        const data = {};
        config.fields.forEach(field => {
            data[field.key] = initial[field.key] || '';
        });
        return data;
    }, [config.fields, initial]);

    const { data, setData, put, processing, errors } = useForm(initialData);

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('admin.settings.updateGroup', groupKey), {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            },
        });
    };

    const renderField = (field) => {
        const value = data[field.key] || '';
        const error = errors[field.key];

        return (
            <div key={field.key} className="space-y-2">
                <Label htmlFor={field.key}>{field.label}</Label>
                {field.textarea ? (
                    <Textarea
                        id={field.key}
                        value={value}
                        onChange={(e) => setData(field.key, e.target.value)}
                        className={error ? 'border-red-500' : ''}
                        rows={3}
                    />
                ) : (
                    <Input
                        id={field.key}
                        type={field.type || 'text'}
                        value={value}
                        onChange={(e) => setData(field.key, e.target.value)}
                        className={error ? 'border-red-500' : ''}
                    />
                )}
                {error && <p className="text-sm text-red-500">{error}</p>}
            </div>
        );
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {config.fields.map(renderField)}
            </div>
            <div className="flex justify-end pt-4 border-t">
                <Button type="submit" disabled={processing}>
                    {processing ? 'Saving...' : `Save ${config.label}`}
                </Button>
            </div>
        </form>
    );
}

export default function HomePageSettings({ settings = {} }) {
    const title = 'Home Page Settings';
    const description = 'Manage home page content and settings';

    const [openSections, setOpenSections] = useState({
        hero: true, // Open first section by default
    });

    const toggleSection = (section) => {
        setOpenSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    const homePageGroups = useMemo(
        () => ({
            hero: {
                label: 'Hero Section',
                fields: [
                    { key: 'hero_pre_heading', label: 'Pre Heading' },
                    { key: 'hero_heading', label: 'Main Heading' },
                    { key: 'hero_sub_heading', label: 'Sub Heading', textarea: true },
                    { key: 'hero_button_text', label: 'Button Text' },
                    { key: 'hero_button_url', label: 'Button URL', type: 'url' },
                    { key: 'hero_image', label: 'Hero Image Path' },
                ],
            },
            top_destination: {
                label: 'Top Destination',
                fields: [
                    { key: 'top_dest_pre_heading', label: 'Pre Heading' },
                    { key: 'top_dest_heading', label: 'Main Heading' },
                    { key: 'top_dest_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
            plan_trip: {
                label: 'Plan Your Trip',
                fields: [
                    { key: 'plan_trip_pre_heading', label: 'Pre Heading' },
                    { key: 'plan_trip_heading', label: 'Main Heading' },
                    { key: 'plan_trip_short_desc', label: 'Short Description', textarea: true },
                    { key: 'plan_trip_image1', label: 'Image 1 Path' },
                    { key: 'plan_trip_image2', label: 'Image 2 Path' },
                    { key: 'plan_trip_image3', label: 'Image 3 Path' },
                    { key: 'plan_trip_button_text', label: 'Button Text' },
                    { key: 'plan_trip_button_url', label: 'Button URL', type: 'url' },
                ],
            },
            best_offers: {
                label: 'Best Offers',
                fields: [
                    { key: 'best_offers_pre_heading', label: 'Pre Heading' },
                    { key: 'best_offers_heading', label: 'Main Heading' },
                    { key: 'best_offers_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
            popular_packages: {
                label: 'Popular Packages',
                fields: [
                    { key: 'popular_packages_pre_heading', label: 'Pre Heading' },
                    { key: 'popular_packages_heading', label: 'Main Heading' },
                    { key: 'popular_packages_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
            travel_experience: {
                label: 'Travel Experience',
                fields: [
                    { key: 'travel_exp_pre_heading', label: 'Pre Heading' },
                    { key: 'travel_exp_heading', label: 'Main Heading' },
                    { key: 'travel_exp_short_desc', label: 'Short Description', textarea: true },
                    { key: 'travel_exp_image', label: 'Background Image Path' },
                    { key: 'travel_exp_button_text', label: 'Button Text' },
                    { key: 'travel_exp_button_url', label: 'Button URL', type: 'url' },
                ],
            },
            solabans_village: {
                label: 'Solabans Village',
                fields: [
                    { key: 'solabans_pre_heading', label: 'Pre Heading' },
                    { key: 'solabans_heading', label: 'Main Heading' },
                    { key: 'solabans_short_desc', label: 'Short Description', textarea: true },
                    { key: 'solabans_image', label: 'Background Image Path' },
                    { key: 'solabans_button_text', label: 'Button Text' },
                    { key: 'solabans_button_url', label: 'Button URL', type: 'url' },
                ],
            },
            testimonials: {
                label: 'Testimonials',
                fields: [
                    { key: 'testimonials_pre_heading', label: 'Pre Heading' },
                    { key: 'testimonials_heading', label: 'Main Heading' },
                    { key: 'testimonials_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
            review_brands: {
                label: 'Review and Brands',
                fields: [
                    { key: 'review_brands_pre_heading', label: 'Pre Heading' },
                    { key: 'review_brands_heading', label: 'Main Heading' },
                    { key: 'review_brands_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
            news: {
                label: 'News',
                fields: [
                    { key: 'news_pre_heading', label: 'Pre Heading' },
                    { key: 'news_heading', label: 'Main Heading' },
                    { key: 'news_sub_heading', label: 'Sub Heading', textarea: true },
                ],
            },
        }),
        [],
    );

    return (
        <AdminLayout title={title} description={description}>
            <Head title={title} />
            <div className="space-y-6">
                {Object.entries(homePageGroups).map(([groupKey, group]) => (
                    <Card key={groupKey}>
                        <Collapsible
                            open={openSections[groupKey]}
                            onOpenChange={() => toggleSection(groupKey)}
                        >
                            <CollapsibleTrigger asChild>
                                <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">{group.label}</CardTitle>
                                        {openSections[groupKey] ? (
                                            <ChevronDown className="h-4 w-4" />
                                        ) : (
                                            <ChevronRight className="h-4 w-4" />
                                        )}
                                    </div>
                                </CardHeader>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <CardContent>
                                    <GroupForm
                                        groupKey={groupKey}
                                        config={group}
                                        initial={settings[groupKey] || {}}
                                    />
                                </CardContent>
                            </CollapsibleContent>
                        </Collapsible>
                    </Card>
                ))}
            </div>
        </AdminLayout>
    );
}