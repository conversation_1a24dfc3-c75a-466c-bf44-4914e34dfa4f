import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, Edit, Trash2, Zap } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddActivityDialog from './AddActivityDialog.jsx';
import EditActivityDialog from './EditActivityDialog.jsx';
import SearchAndFilter from './SearchAndFilter.jsx';

export default function ActivitiesIndex({
    activities,
    destinations,
    parentActivities,
    filters,
    title,
    description,
    ...props
}) {
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingActivity, setEditingActivity] = useState(null);

    const handleEditActivity = (activity) => {
        setEditingActivity(activity);
        setIsEditDialogOpen(true);
    };

    const handleDeleteActivity = (activity) => {
        router.delete(route('admin.activities.destroy', activity.id), {
            onSuccess: () => {
                toast.success('Activity deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete activity errors:', errors);
                if (errors?.error) {
                    toast.error(errors.error);
                } else {
                    toast.error('Failed to delete activity');
                }
            },
        });
    };

    const closeEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingActivity(null);
        resetEdit();
        if (editFileInputRef.current) {
            editFileInputRef.current.value = '';
        }
    };

    // Convert destinations to options for MultiSelect
    const destinationOptions = destinations.map((dest) => ({
        value: dest.id.toString(),
        label: dest.name,
    }));

    // Handle multiple destination selection for edit form
    const handleDestinationSelectChange = (selectedOptions, isEdit = false) => {
        const selectedIds = selectedOptions.map((option) => option.value);
        if (isEdit) {
            setEditData('destination_ids', selectedIds);
        }
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <AddActivityDialog
                    destinations={destinations}
                    parentActivities={parentActivities}
                    onActivityAdded={() => {
                        // optionally refresh or show toast, but creation already toasts
                    }}
                />
            }
        >
            <Head title={title} />

            <div className="pb-8">
                <Card>
                    <CardContent className="p-6">
                        {/* Search and Filters */}
                        <SearchAndFilter
                            filters={filters}
                            destinations={destinations}
                            activities={activities}
                        />

                        {/* Activities Table */}
                        <div className="overflow-hidden rounded-lg border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Activity Type</TableHead>
                                        <TableHead>Destination</TableHead>
                                        <TableHead>Parent Activity</TableHead>
                                        <TableHead>Sub-Activities</TableHead>
                                        <TableHead>Packages</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {activities.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={9}
                                                className="text-muted-foreground py-8 text-center"
                                            >
                                                No activities found.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        activities.data.map((activity) => (
                                            <TableRow key={activity.id}>
                                                <TableCell>
                                                    {activity.image ? (
                                                        <img
                                                            src={`/storage/${activity.image}`}
                                                            alt={activity.name}
                                                            className="h-10 w-10 rounded-lg object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100">
                                                            <Zap className="h-5 w-5 text-gray-400" />
                                                        </div>
                                                    )}
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    {activity.name}
                                                </TableCell>
                                                <TableCell className="truncate">
                                                    {activity.activity_id ? (
                                                        <span className="text-sm">
                                                            Sub-Activity
                                                        </span>
                                                    ) : (
                                                        <span className="text-sm">
                                                            Main Activity
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="space-y-1">
                                                        {activity.destinations &&
                                                            activity
                                                                .destinations
                                                                .length > 0 && (
                                                                <div className="flex flex-wrap gap-1">
                                                                    {activity.destinations.map(
                                                                        (
                                                                            dest,
                                                                        ) => (
                                                                            <span
                                                                                className="text-sm font-medium text-gray-900"
                                                                                key={
                                                                                    dest.id
                                                                                }
                                                                            >
                                                                                {
                                                                                    dest.name
                                                                                }
                                                                            </span>
                                                                        ),
                                                                    )}
                                                                </div>
                                                            )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {activity.parent_activity ? (
                                                        <span className="text-sm text-gray-600">
                                                            {
                                                                activity
                                                                    .parent_activity
                                                                    .name
                                                            }
                                                        </span>
                                                    ) : (
                                                        <span className="text-sm text-gray-400">
                                                            -
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="secondary">
                                                        {activity.sub_activities_count ||
                                                            0}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {activity.packages_count ||
                                                            0}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(
                                                        activity.created_at,
                                                    ).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                handleEditActivity(
                                                                    activity,
                                                                )
                                                            }
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        Activity
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete "
                                                                        {
                                                                            activity.name
                                                                        }
                                                                        "? This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeleteActivity(
                                                                                activity,
                                                                            )
                                                                        }
                                                                        className="bg-red-600 hover:bg-red-700"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {activities.total > activities.per_page && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {activities.from} to {activities.to}{' '}
                                    of {activities.total} results
                                </div>
                                <div className="flex gap-2">
                                    {activities.prev_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    activities.prev_page_url,
                                                )
                                            }
                                        >
                                            <ChevronLeft className="mr-1 h-4 w-4" />
                                            Previous
                                        </Button>
                                    )}
                                    {activities.next_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    activities.next_page_url,
                                                )
                                            }
                                        >
                                            Next
                                            <ChevronRight className="ml-1 h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Edit Dialog */}
            <EditActivityDialog
                destinations={destinations}
                parentActivities={parentActivities}
                activity={editingActivity}
                isOpen={isEditDialogOpen}
                onOpenChange={setIsEditDialogOpen}
            />
        </AdminLayout>
    );
}
