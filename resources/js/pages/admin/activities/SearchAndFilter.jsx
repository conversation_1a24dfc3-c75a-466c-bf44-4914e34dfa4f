import { Button } from '@/admin/components/ui/button';
import { Input } from '@admin/components/ui/input.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { router } from '@inertiajs/react';
import { Filter, Search } from 'lucide-react';
import { useState } from 'react';

export default function SearchAndFilter({
    destinations,
    activityTypes,
    filters,
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [destinationFilter, setDestinationFilter] = useState(
        filters.destination_id || 'all',
    );
    const [activityTypeFilter, setActivityTypeFilter] = useState(
        filters.activity_type || 'all',
    );

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.activities.index'),
            {
                search: searchTerm,
                destination_id:
                    destinationFilter !== 'all' ? destinationFilter : '',
                activity_type:
                    activityTypeFilter !== 'all' ? activityTypeFilter : '',
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setDestinationFilter('all');
        setActivityTypeFilter('all');
        router.get(
            route('admin.activities.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    return (
        <div className="mb-6 flex flex-col gap-4">
            <form onSubmit={handleSearch} className="flex flex-wrap gap-2">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                    <Input
                        type="text"
                        placeholder="Search activities..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-64 pl-10"
                    />
                </div>
                <Select
                    value={destinationFilter}
                    onValueChange={setDestinationFilter}
                >
                    <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by destination" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Destinations</SelectItem>
                        {destinations.map((destination) => (
                            <SelectItem
                                key={destination.id}
                                value={destination.id.toString()}
                            >
                                {destination.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <Select
                    value={activityTypeFilter}
                    onValueChange={setActivityTypeFilter}
                >
                    <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Activities</SelectItem>
                        <SelectItem value="main">
                            Main Activities Only
                        </SelectItem>
                        <SelectItem value="sub">Sub-Activities Only</SelectItem>
                    </SelectContent>
                </Select>
                <Button type="submit" variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Apply Filters
                </Button>
                {(filters.search ||
                    filters.destination_id ||
                    filters.activity_type) && (
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleClearFilters}
                    >
                        Clear
                    </Button>
                )}
            </form>
        </div>
    );
}
