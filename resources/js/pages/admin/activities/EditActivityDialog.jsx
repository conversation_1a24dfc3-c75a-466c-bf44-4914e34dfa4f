import { Button } from '@/admin/components/ui/button';
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { MultiSelect } from '@admin/components/ui/multi-select.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { useForm } from '@inertiajs/react';
import { Upload } from 'lucide-react';
import { useEffect, useRef } from 'react';

export default function EditActivityDialog({
    isOpen,
    onOpenChange,
    activity: editingActivity,
    destinations,
    parentActivities,
}) {
    const editFileInputRef = useRef(null);

    const {
        data: editData,
        setData: setEditData,
        post: putWithFiles,
        processing: editProcessing,
        errors: editErrors,
        reset: resetEdit,
    } = useForm({
        name: '',
        slug: '',
        description: '',
        destination_ids: [],
        activity_id: '',
        image: null,
        _method: 'PUT',
    });

    const closeEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingActivity(null);
        resetEdit();
        if (editFileInputRef.current) {
            editFileInputRef.current.value = '';
        }
    };

    // Convert destinations to options for MultiSelect
    const destinationOptions = destinations.map((dest) => ({
        value: dest.id.toString(),
        label: dest.name,
    }));

    // Filter parent activities based on selected destination for the edit form
    const getFilteredParentActivities = (destinationId) => {
        if (!destinationId) return [];
        return parentActivities.filter(
            (activity) =>
                activity.destinations &&
                activity.destinations.some(
                    (dest) => dest.id.toString() === destinationId.toString()
                ),
        );
    };

    const handleUpdateActivity = (e) => {
        e.preventDefault();
        putWithFiles(route('admin.activities.update', editingActivity.id), {
            onSuccess: () => {
                toast.success('Activity updated successfully');
                setIsEditDialogOpen(false);
                setEditingActivity(null);
                resetEdit();
                if (editFileInputRef.current) {
                    editFileInputRef.current.value = '';
                }
            },
            onError: (errors) => {
                console.error('Update activity errors:', errors);
                if (errors?.error) {
                    toast.error(errors.error);
                } else {
                    toast.error('Failed to update activity');
                }
            },
        });
    };

    useEffect(() => {
        if (editingActivity) {
            setEditData({
                name: editingActivity.name,
                slug: editingActivity.slug || '',
                description: editingActivity.description || '',
                destination_ids: editingActivity.destinations
                    ? editingActivity.destinations.map((dest) =>
                          dest.id.toString(),
                      )
                    : [],
                activity_id: editingActivity.activity_id
                    ? editingActivity.activity_id.toString()
                    : '',
                image: null,
                _method: 'PUT',
            });
        }
    }, [editingActivity]);

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-md">
                <form onSubmit={handleUpdateActivity}>
                    <DialogHeader>
                        <DialogTitle>Edit Activity</DialogTitle>
                        <DialogDescription>
                            Update the activity information.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="edit-name">Name</Label>
                            <Input
                                id="edit-name"
                                value={editData.name}
                                onChange={(e) =>
                                    setEditData('name', e.target.value)
                                }
                                placeholder="Enter activity name"
                                className={
                                    editErrors.name ? 'border-red-500' : ''
                                }
                            />
                            {editErrors.name && (
                                <p className="text-sm text-red-500">
                                    {editErrors.name}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-slug">Slug</Label>
                            <Input
                                id="edit-slug"
                                value={editData.slug}
                                onChange={(e) =>
                                    setEditData('slug', e.target.value)
                                }
                                placeholder="Enter activity slug (e.g., trekking-in-himalayas)"
                                className={
                                    editErrors.slug ? 'border-red-500' : ''
                                }
                            />
                            <p className="text-sm text-gray-500">
                                URL-friendly version of the name.
                            </p>
                            {editErrors.slug && (
                                <p className="text-sm text-red-500">
                                    {editErrors.slug}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-description">
                                Description
                            </Label>
                            <Textarea
                                id="edit-description"
                                value={editData.description}
                                onChange={(e) =>
                                    setEditData('description', e.target.value)
                                }
                                placeholder="Enter activity description"
                                className={
                                    editErrors.description
                                        ? 'border-red-500'
                                        : ''
                                }
                                rows={3}
                            />
                            {editErrors.description && (
                                <p className="text-sm text-red-500">
                                    {editErrors.description}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-destination">
                                Primary Destination
                            </Label>
                            <Select
                                value={editData.destination_id}
                                onValueChange={(value) =>
                                    setEditData('destination_id', value)
                                }
                            >
                                <SelectTrigger
                                    className={
                                        editErrors.destination_id
                                            ? 'border-red-500'
                                            : ''
                                    }
                                >
                                    <SelectValue placeholder="Select primary destination" />
                                </SelectTrigger>
                                <SelectContent>
                                    {destinations.map((destination) => (
                                        <SelectItem
                                            key={destination.id}
                                            value={destination.id.toString()}
                                        >
                                            {destination.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {editErrors.destination_id && (
                                <p className="text-sm text-red-500">
                                    {editErrors.destination_id}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-destinations">
                                Additional Destinations (Optional)
                            </Label>
                            <MultiSelect
                                options={destinationOptions}
                                value={destinationOptions.filter((option) =>
                                    editData.destination_ids.includes(
                                        option.value,
                                    ),
                                )}
                                onChange={(selectedOptions) =>
                                    handleDestinationSelectChange(
                                        selectedOptions,
                                        true,
                                    )
                                }
                                placeholder="Select additional destinations..."
                                className={
                                    editErrors.destination_ids
                                        ? 'border-red-500'
                                        : ''
                                }
                            />
                            {editErrors.destination_ids && (
                                <p className="text-sm text-red-500">
                                    {editErrors.destination_ids}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-parent-activity">
                                Parent Activity (Optional)
                            </Label>
                            <Select
                                value={editData.activity_id || 'none'}
                                onValueChange={(value) =>
                                    setEditData(
                                        'activity_id',
                                        value === 'none' ? '' : value,
                                    )
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select parent activity (for sub-activity)" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">
                                        None (Main Activity)
                                    </SelectItem>
                                    {getFilteredParentActivities(
                                        editData.destination_id,
                                    )
                                        .filter(
                                            (activity) =>
                                                activity.id !==
                                                editingActivity?.id,
                                        )
                                        .map((activity) => (
                                            <SelectItem
                                                key={activity.id}
                                                value={activity.id.toString()}
                                            >
                                                {activity.name}
                                            </SelectItem>
                                        ))}
                                </SelectContent>
                            </Select>
                            {editErrors.activity_id && (
                                <p className="text-sm text-red-500">
                                    {editErrors.activity_id}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="edit-image">Image</Label>
                            {editingActivity?.image && (
                                <div className="mb-2">
                                    <img
                                        src={`/storage/${editingActivity.image}`}
                                        alt={editingActivity.name}
                                        className="h-20 w-20 rounded-lg object-cover"
                                    />
                                    <p className="mt-1 text-xs text-gray-500">
                                        Current image
                                    </p>
                                </div>
                            )}
                            <div className="flex items-center gap-2">
                                <Input
                                    id="edit-image"
                                    type="file"
                                    ref={editFileInputRef}
                                    accept="image/*"
                                    onChange={(e) =>
                                        setEditData('image', e.target.files[0])
                                    }
                                    className={
                                        editErrors.image ? 'border-red-500' : ''
                                    }
                                />
                                <Upload className="h-4 w-4 text-gray-400" />
                            </div>
                            {editErrors.image && (
                                <p className="text-sm text-red-500">
                                    {editErrors.image}
                                </p>
                            )}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={closeEditDialog}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={editProcessing}>
                            {editProcessing ? 'Updating...' : 'Update Activity'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
