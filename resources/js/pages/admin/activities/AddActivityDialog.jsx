import { Button } from '@admin/components/ui/button.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import { MultiSelect } from '@admin/components/ui/multi-select.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import { useForm } from '@inertiajs/react';
import { Plus, Upload } from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

export default function AddActivityDialog({
    destinations,
    parentActivities,
    onActivityAdded,
}) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const fileInputRef = useRef(null);

    const {
        data: addData,
        setData: setAddData,
        post,
        processing: addProcessing,
        errors: addErrors,
        reset: resetAdd,
    } = useForm({
        name: '',
        slug: '',
        description: '',
        destination_id: '',
        destination_ids: [],
        activity_id: '',
        image: null,
    });

    // Convert destinations to options for MultiSelect
    const destinationOptions = destinations.map((dest) => ({
        value: dest.id.toString(),
        label: dest.name,
    }));

    // Filter parent activities based on selected destination for the add form
    const getFilteredParentActivities = (destinationId) => {
        if (!destinationId) return [];
        return parentActivities.filter(
            (activity) =>
                activity.destinations &&
                activity.destinations.some(
                    (dest) => dest.id.toString() === destinationId.toString()
                ),
        );
    };

    // Handle multiple destination selection for add form
    const handleDestinationSelectChange = (selectedOptions) => {
        const selectedIds = selectedOptions.map((option) => option.value);
        setAddData('destination_ids', selectedIds);
    };

    const handleAddActivity = (e) => {
        e.preventDefault();
        post(route('admin.activities.store'), {
            onSuccess: () => {
                toast.success('Activity created successfully');
                closeAddDialog();
                if (onActivityAdded) {
                    onActivityAdded();
                }
            },
            onError: (errors) => {
                console.error('Add activity errors:', errors);
                toast.error('Failed to create activity');
            },
        });
    };

    const closeAddDialog = () => {
        setIsAddDialogOpen(false);
        resetAdd();
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
                <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Activity
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <form onSubmit={handleAddActivity}>
                    <DialogHeader>
                        <DialogTitle>Add New Activity</DialogTitle>
                        <DialogDescription>
                            Create a new activity or sub-activity.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="add-name">Name</Label>
                            <Input
                                id="add-name"
                                value={addData.name}
                                onChange={(e) =>
                                    setAddData('name', e.target.value)
                                }
                                placeholder="Enter activity name"
                                className={
                                    addErrors.name ? 'border-red-500' : ''
                                }
                            />
                            {addErrors.name && (
                                <p className="text-sm text-red-500">
                                    {addErrors.name}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="add-slug">Slug</Label>
                            <Input
                                id="add-slug"
                                value={addData.slug}
                                onChange={(e) =>
                                    setAddData('slug', e.target.value)
                                }
                                placeholder="Enter activity slug (e.g., trekking-in-himalayas)"
                                className={
                                    addErrors.slug ? 'border-red-500' : ''
                                }
                            />
                            <p className="text-sm text-gray-500">
                                URL-friendly version of the name.
                            </p>
                            {addErrors.slug && (
                                <p className="text-sm text-red-500">
                                    {addErrors.slug}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="add-description">Description</Label>
                            <Textarea
                                id="add-description"
                                value={addData.description}
                                onChange={(e) =>
                                    setAddData('description', e.target.value)
                                }
                                placeholder="Enter activity description"
                                className={
                                    addErrors.description
                                        ? 'border-red-500'
                                        : ''
                                }
                                rows={3}
                            />
                            {addErrors.description && (
                                <p className="text-sm text-red-500">
                                    {addErrors.description}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="add-destinations">
                                Destination(s)
                            </Label>
                            <MultiSelect
                                options={destinationOptions}
                                value={destinationOptions.filter((option) =>
                                    addData.destination_ids.includes(
                                        option.value,
                                    ),
                                )}
                                onChange={handleDestinationSelectChange}
                                placeholder="Select additional destinations..."
                                className={
                                    addErrors.destination_ids
                                        ? 'border-red-500'
                                        : ''
                                }
                            />
                            {addErrors.destination_ids && (
                                <p className="text-sm text-red-500">
                                    {addErrors.destination_ids}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="add-parent-activity">
                                Parent Activity (Optional)
                            </Label>
                            <Select
                                value={addData.activity_id || 'none'}
                                onValueChange={(value) =>
                                    setAddData(
                                        'activity_id',
                                        value === 'none' ? '' : value,
                                    )
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select parent activity (for sub-activity)" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">
                                        None (Main Activity)
                                    </SelectItem>
                                    {getFilteredParentActivities(
                                        addData.destination_id,
                                    ).map((activity) => (
                                        <SelectItem
                                            key={activity.id}
                                            value={activity.id.toString()}
                                        >
                                            {activity.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {addErrors.activity_id && (
                                <p className="text-sm text-red-500">
                                    {addErrors.activity_id}
                                </p>
                            )}
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="add-image">Image</Label>
                            <div className="flex items-center gap-2">
                                <Input
                                    id="add-image"
                                    type="file"
                                    ref={fileInputRef}
                                    accept="image/*"
                                    onChange={(e) =>
                                        setAddData('image', e.target.files[0])
                                    }
                                    className={
                                        addErrors.image ? 'border-red-500' : ''
                                    }
                                />
                                <Upload className="h-4 w-4 text-gray-400" />
                            </div>
                            {addErrors.image && (
                                <p className="text-sm text-red-500">
                                    {addErrors.image}
                                </p>
                            )}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={closeAddDialog}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={addProcessing}>
                            {addProcessing ? 'Creating...' : 'Create Activity'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
