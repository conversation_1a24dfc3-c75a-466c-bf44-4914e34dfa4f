import {
    Toolbar,
    ToolbarActions,
    ToolbarHeading,
} from '@/admin/layouts/components/toolbar';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router, useForm } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Edit,
    MapPin,
    Plus,
    Search,
    Trash2,
    Upload,
} from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

export default function DestinationsIndex({ destinations, filters, ...props }) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingDestination, setEditingDestination] = useState(null);
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const fileInputRef = useRef(null);
    const editFileInputRef = useRef(null);

    const {
        data: addData,
        setData: setAddData,
        post,
        processing: addProcessing,
        errors: addErrors,
        reset: resetAdd,
    } = useForm({
        name: '',
        slug: '',
        description: '',
        image: null,
    });

    const {
        data: editData,
        setData: setEditData,
        post: putWithFiles,
        processing: editProcessing,
        errors: editErrors,
        reset: resetEdit,
    } = useForm({
        name: '',
        slug: '',
        description: '',
        image: null,
        _method: 'PUT',
    });

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.destinations.index'),
            { search: searchTerm },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearSearch = () => {
        setSearchTerm('');
        router.get(
            route('admin.destinations.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleAddDestination = (e) => {
        e.preventDefault();
        post(route('admin.destinations.store'), {
            onSuccess: () => {
                toast.success('Destination created successfully');
                setIsAddDialogOpen(false);
                resetAdd();
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            },
            onError: (errors) => {
                console.error('Add destination errors:', errors);
                toast.error('Failed to create destination');
            },
        });
    };

    const handleEditDestination = (destination) => {
        setEditingDestination(destination);
        setEditData({
            name: destination.name,
            slug: destination.slug || '',
            description: destination.description || '',
            image: null,
            _method: 'PUT',
        });
        setIsEditDialogOpen(true);
    };

    const handleUpdateDestination = (e) => {
        e.preventDefault();
        putWithFiles(
            route('admin.destinations.update', editingDestination.id),
            {
                onSuccess: () => {
                    toast.success('Destination updated successfully');
                    setIsEditDialogOpen(false);
                    setEditingDestination(null);
                    resetEdit();
                    if (editFileInputRef.current) {
                        editFileInputRef.current.value = '';
                    }
                },
                onError: (errors) => {
                    console.error('Update destination errors:', errors);
                    toast.error('Failed to update destination');
                },
            },
        );
    };

    const handleDeleteDestination = (destination) => {
        router.delete(route('admin.destinations.destroy', destination.id), {
            onSuccess: () => {
                toast.success('Destination deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete destination errors:', errors);
                if (errors?.error) {
                    toast.error(errors.error);
                } else {
                    toast.error('Failed to delete destination');
                }
            },
        });
    };

    const closeAddDialog = () => {
        setIsAddDialogOpen(false);
        resetAdd();
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const closeEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingDestination(null);
        resetEdit();
        if (editFileInputRef.current) {
            editFileInputRef.current.value = '';
        }
    };

    return (
        <AdminLayout toolbar={false}>
            <Head title={props.title} />

            <Toolbar>
                <ToolbarHeading
                    title={props.title}
                    description={props.description}
                />
                <ToolbarActions>
                    <Dialog
                        open={isAddDialogOpen}
                        onOpenChange={setIsAddDialogOpen}
                    >
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Destination
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                            <form onSubmit={handleAddDestination}>
                                <DialogHeader>
                                    <DialogTitle>
                                        Add New Destination
                                    </DialogTitle>
                                    <DialogDescription>
                                        Create a new destination for travel
                                        packages.
                                    </DialogDescription>
                                </DialogHeader>
                                <div className="grid gap-4 py-4">
                                    <div className="grid gap-2">
                                        <Label htmlFor="add-name">Name</Label>
                                        <Input
                                            id="add-name"
                                            value={addData.name}
                                            onChange={(e) =>
                                                setAddData(
                                                    'name',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter destination name"
                                            className={
                                                addErrors.name
                                                    ? 'border-red-500'
                                                    : ''
                                            }
                                        />
                                        {addErrors.name && (
                                            <p className="text-sm text-red-500">
                                                {addErrors.name}
                                            </p>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="add-slug">Slug</Label>
                                        <Input
                                            id="add-slug"
                                            value={addData.slug}
                                            onChange={(e) =>
                                                setAddData(
                                                    'slug',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter destination slug (e.g., everest-region)"
                                            className={
                                                addErrors.slug
                                                    ? 'border-red-500'
                                                    : ''
                                            }
                                        />
                                        <p className="text-sm text-gray-500">
                                            URL-friendly version of the name.
                                        </p>
                                        {addErrors.slug && (
                                            <p className="text-sm text-red-500">
                                                {addErrors.slug}
                                            </p>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="add-description">
                                            Description
                                        </Label>
                                        <Textarea
                                            id="add-description"
                                            value={addData.description}
                                            onChange={(e) =>
                                                setAddData(
                                                    'description',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Enter destination description"
                                            className={
                                                addErrors.description
                                                    ? 'border-red-500'
                                                    : ''
                                            }
                                            rows={3}
                                        />
                                        {addErrors.description && (
                                            <p className="text-sm text-red-500">
                                                {addErrors.description}
                                            </p>
                                        )}
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="add-image">Image</Label>
                                        <div className="flex items-center gap-2">
                                            <Input
                                                id="add-image"
                                                type="file"
                                                ref={fileInputRef}
                                                accept="image/*"
                                                onChange={(e) =>
                                                    setAddData(
                                                        'image',
                                                        e.target.files[0],
                                                    )
                                                }
                                                className={
                                                    addErrors.image
                                                        ? 'border-red-500'
                                                        : ''
                                                }
                                            />
                                            <Upload className="h-4 w-4 text-gray-400" />
                                        </div>
                                        {addErrors.image && (
                                            <p className="text-sm text-red-500">
                                                {addErrors.image}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <DialogFooter>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={closeAddDialog}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={addProcessing}
                                    >
                                        {addProcessing
                                            ? 'Creating...'
                                            : 'Create Destination'}
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </ToolbarActions>
            </Toolbar>

            <div className="pb-8">
                <Card>
                    <CardContent className="p-6">
                        {/* Search and Filters */}
                        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <form
                                onSubmit={handleSearch}
                                className="flex gap-2"
                            >
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search destinations..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                        className="w-64 pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                                {filters.search && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleClearSearch}
                                    >
                                        Clear
                                    </Button>
                                )}
                            </form>
                        </div>

                        {/* Destinations Table */}
                        <div className="overflow-hidden rounded-lg border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Packages</TableHead>
                                        <TableHead>Activities</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {destinations.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={7}
                                                className="text-muted-foreground py-8 text-center"
                                            >
                                                No destinations found.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        destinations.data.map((destination) => (
                                            <TableRow key={destination.id}>
                                                <TableCell>
                                                    {destination.image ? (
                                                        <img
                                                            src={`/storage/${destination.image}`}
                                                            alt={
                                                                destination.name
                                                            }
                                                            className="h-10 w-10 rounded-lg object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100">
                                                            <MapPin className="h-5 w-5 text-gray-400" />
                                                        </div>
                                                    )}
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    {destination.name}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="secondary">
                                                        {destination.packages_count ||
                                                            0}{' '}
                                                        packages
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {destination.activities_count ||
                                                            0}{' '}
                                                        activities
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(
                                                        destination.created_at,
                                                    ).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                handleEditDestination(
                                                                    destination,
                                                                )
                                                            }
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        Destination
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete "
                                                                        {
                                                                            destination.name
                                                                        }
                                                                        "? This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeleteDestination(
                                                                                destination,
                                                                            )
                                                                        }
                                                                        className="bg-red-600 hover:bg-red-700"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {destinations.total > destinations.per_page && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {destinations.from} to{' '}
                                    {destinations.to} of {destinations.total}{' '}
                                    results
                                </div>
                                <div className="flex gap-2">
                                    {destinations.prev_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    destinations.prev_page_url,
                                                )
                                            }
                                        >
                                            <ChevronLeft className="mr-1 h-4 w-4" />
                                            Previous
                                        </Button>
                                    )}
                                    {destinations.next_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(
                                                    destinations.next_page_url,
                                                )
                                            }
                                        >
                                            Next
                                            <ChevronRight className="ml-1 h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="max-w-md">
                    <form onSubmit={handleUpdateDestination}>
                        <DialogHeader>
                            <DialogTitle>Edit Destination</DialogTitle>
                            <DialogDescription>
                                Update the destination information.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="edit-name">Name</Label>
                                <Input
                                    id="edit-name"
                                    value={editData.name}
                                    onChange={(e) =>
                                        setEditData('name', e.target.value)
                                    }
                                    placeholder="Enter destination name"
                                    className={
                                        editErrors.name ? 'border-red-500' : ''
                                    }
                                />
                                {editErrors.name && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.name}
                                    </p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-slug">Slug</Label>
                                <Input
                                    id="edit-slug"
                                    value={editData.slug}
                                    onChange={(e) =>
                                        setEditData('slug', e.target.value)
                                    }
                                    placeholder="Enter destination slug (e.g., everest-region)"
                                    className={
                                        editErrors.slug ? 'border-red-500' : ''
                                    }
                                />
                                <p className="text-sm text-gray-500">
                                    URL-friendly version of the name.
                                </p>
                                {editErrors.slug && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.slug}
                                    </p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-description">
                                    Description
                                </Label>
                                <Textarea
                                    id="edit-description"
                                    value={editData.description}
                                    onChange={(e) =>
                                        setEditData(
                                            'description',
                                            e.target.value,
                                        )
                                    }
                                    placeholder="Enter destination description"
                                    className={
                                        editErrors.description
                                            ? 'border-red-500'
                                            : ''
                                    }
                                    rows={3}
                                />
                                {editErrors.description && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.description}
                                    </p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-image">Image</Label>
                                {editingDestination?.image && (
                                    <div className="mb-2">
                                        <img
                                            src={`/storage/${editingDestination.image}`}
                                            alt={editingDestination.name}
                                            className="h-20 w-20 rounded-lg object-cover"
                                        />
                                        <p className="mt-1 text-xs text-gray-500">
                                            Current image
                                        </p>
                                    </div>
                                )}
                                <div className="flex items-center gap-2">
                                    <Input
                                        id="edit-image"
                                        type="file"
                                        ref={editFileInputRef}
                                        accept="image/*"
                                        onChange={(e) =>
                                            setEditData(
                                                'image',
                                                e.target.files[0],
                                            )
                                        }
                                        className={
                                            editErrors.image
                                                ? 'border-red-500'
                                                : ''
                                        }
                                    />
                                    <Upload className="h-4 w-4 text-gray-400" />
                                </div>
                                {editErrors.image && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.image}
                                    </p>
                                )}
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={closeEditDialog}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={editProcessing}>
                                {editProcessing
                                    ? 'Updating...'
                                    : 'Update Destination'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
