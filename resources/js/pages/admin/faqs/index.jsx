import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@admin/components/ui/alert-dialog.jsx';
import { Badge } from '@admin/components/ui/badge.jsx';
import { Button } from '@admin/components/ui/button.jsx';
import { Card, CardContent } from '@admin/components/ui/card.jsx';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@admin/components/ui/dialog.jsx';
import { Input } from '@admin/components/ui/input.jsx';
import { Label } from '@admin/components/ui/label.jsx';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@admin/components/ui/select.jsx';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@admin/components/ui/table.jsx';
import { Textarea } from '@admin/components/ui/textarea.jsx';
import AdminLayout from '@admin/layouts/AdminLayout.jsx';
import { Head, router, useForm } from '@inertiajs/react';
import {
    ChevronLeft,
    ChevronRight,
    Edit,
    Filter,
    Plus,
    Search,
    Trash2,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function FaqsIndex({
    faqs,
    faqGroups,
    filters,
    title,
    description,
    ...props
}) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingFaq, setEditingFaq] = useState(null);
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [groupFilter, setGroupFilter] = useState(
        filters.faq_group_id || 'all',
    );

    const {
        data: addData,
        setData: setAddData,
        post,
        processing: addProcessing,
        errors: addErrors,
        reset: resetAdd,
    } = useForm({
        question: '',
        answer: '',
        faq_group_id: '',
    });

    const {
        data: editData,
        setData: setEditData,
        put,
        processing: editProcessing,
        errors: editErrors,
        reset: resetEdit,
    } = useForm({
        question: '',
        answer: '',
        faq_group_id: '',
    });

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route('admin.faqs.index'),
            {
                search: searchTerm,
                faq_group_id: groupFilter !== 'all' ? groupFilter : '',
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setGroupFilter('all');
        router.get(
            route('admin.faqs.index'),
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleAddFaq = (e) => {
        e.preventDefault();
        post(route('admin.faqs.store'), {
            onSuccess: () => {
                toast.success('FAQ created successfully');
                setIsAddDialogOpen(false);
                resetAdd();
            },
            onError: (errors) => {
                console.error('Add FAQ errors:', errors);
                toast.error('Failed to create FAQ');
            },
        });
    };

    const handleEditFaq = (faq) => {
        setEditingFaq(faq);
        setEditData({
            question: faq.question,
            answer: faq.answer,
            faq_group_id: faq.faq_group_id.toString(),
        });
        setIsEditDialogOpen(true);
    };

    const handleUpdateFaq = (e) => {
        e.preventDefault();
        put(route('admin.faqs.update', editingFaq.id), {
            onSuccess: () => {
                toast.success('FAQ updated successfully');
                setIsEditDialogOpen(false);
                setEditingFaq(null);
                resetEdit();
            },
            onError: (errors) => {
                console.error('Update FAQ errors:', errors);
                toast.error('Failed to update FAQ');
            },
        });
    };

    const handleDeleteFaq = (faq) => {
        router.delete(route('admin.faqs.destroy', faq.id), {
            onSuccess: () => {
                toast.success('FAQ deleted successfully');
            },
            onError: (errors) => {
                console.error('Delete FAQ errors:', errors);
                toast.error('Failed to delete FAQ');
            },
        });
    };

    const closeAddDialog = () => {
        setIsAddDialogOpen(false);
        resetAdd();
    };

    const closeEditDialog = () => {
        setIsEditDialogOpen(false);
        setEditingFaq(null);
        resetEdit();
    };

    return (
        <AdminLayout
            title={title}
            description={description}
            actions={
                <Dialog
                    open={isAddDialogOpen}
                    onOpenChange={setIsAddDialogOpen}
                >
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add FAQ
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <form onSubmit={handleAddFaq}>
                            <DialogHeader>
                                <DialogTitle>Add New FAQ</DialogTitle>
                                <DialogDescription>
                                    Create a new frequently asked question.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="add-faq-group">
                                        FAQ Group
                                    </Label>
                                    <Select
                                        value={addData.faq_group_id}
                                        onValueChange={(value) =>
                                            setAddData('faq_group_id', value)
                                        }
                                    >
                                        <SelectTrigger
                                            className={
                                                addErrors.faq_group_id
                                                    ? 'border-red-500'
                                                    : ''
                                            }
                                        >
                                            <SelectValue placeholder="Select FAQ group" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {faqGroups.map((group) => (
                                                <SelectItem
                                                    key={group.id}
                                                    value={group.id.toString()}
                                                >
                                                    {group.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {addErrors.faq_group_id && (
                                        <p className="text-sm text-red-500">
                                            {addErrors.faq_group_id}
                                        </p>
                                    )}
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="add-question">
                                        Question
                                    </Label>
                                    <Input
                                        id="add-question"
                                        value={addData.question}
                                        onChange={(e) =>
                                            setAddData(
                                                'question',
                                                e.target.value,
                                            )
                                        }
                                        placeholder="Enter the FAQ question"
                                        className={
                                            addErrors.question
                                                ? 'border-red-500'
                                                : ''
                                        }
                                    />
                                    {addErrors.question && (
                                        <p className="text-sm text-red-500">
                                            {addErrors.question}
                                        </p>
                                    )}
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="add-answer">Answer</Label>
                                    <Textarea
                                        id="add-answer"
                                        value={addData.answer}
                                        onChange={(e) =>
                                            setAddData('answer', e.target.value)
                                        }
                                        placeholder="Enter the FAQ answer"
                                        className={
                                            addErrors.answer
                                                ? 'border-red-500'
                                                : ''
                                        }
                                        rows={5}
                                    />
                                    {addErrors.answer && (
                                        <p className="text-sm text-red-500">
                                            {addErrors.answer}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={closeAddDialog}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={addProcessing}>
                                    {addProcessing
                                        ? 'Creating...'
                                        : 'Create FAQ'}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            }
        >
            <Head title={title} />

            <div className="pb-8">
                <Card>
                    <CardContent className="p-6">
                        {/* Search and Filters */}
                        <div className="mb-6 flex flex-col gap-4">
                            <form
                                onSubmit={handleSearch}
                                className="flex flex-wrap gap-2"
                            >
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search FAQs..."
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                        className="w-64 pl-10"
                                    />
                                </div>
                                <Select
                                    value={groupFilter}
                                    onValueChange={setGroupFilter}
                                >
                                    <SelectTrigger className="w-48">
                                        <SelectValue placeholder="Filter by group" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            All Groups
                                        </SelectItem>
                                        {faqGroups.map((group) => (
                                            <SelectItem
                                                key={group.id}
                                                value={group.id.toString()}
                                            >
                                                {group.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <Button type="submit" variant="outline">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Apply Filters
                                </Button>
                                {(filters.search || filters.faq_group_id) && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleClearFilters}
                                    >
                                        Clear
                                    </Button>
                                )}
                            </form>
                        </div>

                        {/* FAQs Table */}
                        <div className="overflow-hidden rounded-lg border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Question</TableHead>
                                        <TableHead>Answer</TableHead>
                                        <TableHead>Group</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">
                                            Actions
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {faqs.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={5}
                                                className="text-muted-foreground py-8 text-center"
                                            >
                                                No FAQs found.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        faqs.data.map((faq) => (
                                            <TableRow key={faq.id}>
                                                <TableCell className="max-w-xs font-medium">
                                                    <div
                                                        className="truncate"
                                                        title={faq.question}
                                                    >
                                                        {faq.question}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="max-w-md">
                                                    <div
                                                        className="truncate text-sm text-gray-600"
                                                        title={faq.answer}
                                                    >
                                                        {faq.answer}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {faq.faq_group?.name}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(
                                                        faq.created_at,
                                                    ).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() =>
                                                                handleEditFaq(
                                                                    faq,
                                                                )
                                                            }
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>
                                                                        Delete
                                                                        FAQ
                                                                    </AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        Are you
                                                                        sure you
                                                                        want to
                                                                        delete
                                                                        this
                                                                        FAQ?
                                                                        This
                                                                        action
                                                                        cannot
                                                                        be
                                                                        undone.
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>
                                                                        Cancel
                                                                    </AlertDialogCancel>
                                                                    <AlertDialogAction
                                                                        onClick={() =>
                                                                            handleDeleteFaq(
                                                                                faq,
                                                                            )
                                                                        }
                                                                        className="bg-red-600 hover:bg-red-700"
                                                                    >
                                                                        Delete
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {faqs.total > faqs.per_page && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {faqs.from} to {faqs.to} of{' '}
                                    {faqs.total} results
                                </div>
                                <div className="flex gap-2">
                                    {faqs.prev_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(faqs.prev_page_url)
                                            }
                                        >
                                            <ChevronLeft className="mr-1 h-4 w-4" />
                                            Previous
                                        </Button>
                                    )}
                                    {faqs.next_page_url && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                                router.get(faqs.next_page_url)
                                            }
                                        >
                                            Next
                                            <ChevronRight className="ml-1 h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="max-w-2xl">
                    <form onSubmit={handleUpdateFaq}>
                        <DialogHeader>
                            <DialogTitle>Edit FAQ</DialogTitle>
                            <DialogDescription>
                                Update the FAQ information.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="edit-faq-group">
                                    FAQ Group
                                </Label>
                                <Select
                                    value={editData.faq_group_id}
                                    onValueChange={(value) =>
                                        setEditData('faq_group_id', value)
                                    }
                                >
                                    <SelectTrigger
                                        className={
                                            editErrors.faq_group_id
                                                ? 'border-red-500'
                                                : ''
                                        }
                                    >
                                        <SelectValue placeholder="Select FAQ group" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {faqGroups.map((group) => (
                                            <SelectItem
                                                key={group.id}
                                                value={group.id.toString()}
                                            >
                                                {group.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {editErrors.faq_group_id && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.faq_group_id}
                                    </p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-question">Question</Label>
                                <Input
                                    id="edit-question"
                                    value={editData.question}
                                    onChange={(e) =>
                                        setEditData('question', e.target.value)
                                    }
                                    placeholder="Enter the FAQ question"
                                    className={
                                        editErrors.question
                                            ? 'border-red-500'
                                            : ''
                                    }
                                />
                                {editErrors.question && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.question}
                                    </p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-answer">Answer</Label>
                                <Textarea
                                    id="edit-answer"
                                    value={editData.answer}
                                    onChange={(e) =>
                                        setEditData('answer', e.target.value)
                                    }
                                    placeholder="Enter the FAQ answer"
                                    className={
                                        editErrors.answer
                                            ? 'border-red-500'
                                            : ''
                                    }
                                    rows={5}
                                />
                                {editErrors.answer && (
                                    <p className="text-sm text-red-500">
                                        {editErrors.answer}
                                    </p>
                                )}
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={closeEditDialog}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={editProcessing}>
                                {editProcessing ? 'Updating...' : 'Update FAQ'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
