import Breadcrumb from '../components/shared/Breadcrumb.jsx';
import HeroTopSection from '../components/shared/HeroTopSection.jsx';
import AppLayout from '../layouts/AppLayout.jsx';

export default function Page({ title, page, error }) {
    const breadcrumbLinks = [{ title: page?.title || title }];

    if (error) {
        return (
            <AppLayout title={title}>
                <HeroTopSection title="Page Not Found">
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="pb-18 container px-4 pt-8">
                    <div className="lg:w-9/10 mx-auto mt-4 py-6">
                        <div className="py-12 text-center">
                            <div className="mx-auto max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
                                <div className="mb-2 text-lg font-medium text-red-600">
                                    Page Not Found
                                </div>
                                <p className="text-red-500">{error}</p>
                                <button
                                    onClick={() => window.history.back()}
                                    className="mt-4 rounded bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
                                >
                                    Go Back
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </AppLayout>
        );
    }

    if (!page) {
        return (
            <AppLayout title={title}>
                <HeroTopSection title="Loading...">
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="pb-18 container px-4 pt-8">
                    <div className="lg:w-9/10 mx-auto mt-4 py-6">
                        <div className="py-12 text-center">
                            <div className="text-lg text-gray-500">
                                Loading page content...
                            </div>
                        </div>
                    </div>
                </section>
            </AppLayout>
        );
    }

    return (
        <>
            <AppLayout title={page.title}>
                <HeroTopSection title={page.title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="pb-18 container px-4 pt-8">
                    {page.image && (
                        <div className="h-auto w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg">
                            <img
                                src={page.image}
                                alt={page.title}
                                className="h-full w-full object-cover"
                            />
                        </div>
                    )}
                    <div className="lg:w-9/10 mx-auto mt-4 py-6">
                        <div className="blog-content text-md mt-6 md:text-lg [&>h2]:mb-2 [&>h2]:mt-6 [&>h2]:text-2xl [&>h2]:font-[600] [&>h3]:mb-2 [&>h3]:mt-4 [&>h3]:text-xl [&>h3]:font-[600] [&_div]:mb-4 [&_p]:mb-4 [&_p]:font-medium [&_p]:leading-8 [&_p]:text-slate-800">
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: page.content,
                                }}
                            />
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
