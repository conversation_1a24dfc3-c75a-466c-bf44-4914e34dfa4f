import Breadcrumb from '@/components/shared/Breadcrumb.jsx';
import HeroTopSection from '@/components/shared/HeroTopSection.jsx';
import LiveSupportCard from '@/components/shared/LiveSupportCard.jsx';
import Accordion from '@/components/ui/Accordion.jsx';
import AppLayout from '@/layouts/AppLayout.jsx';
import { FiArrowRight } from 'react-icons/fi';
import Button from '../components/ui/Button.jsx';
import FloatingInput from '../components/ui/form-elements/FloatingInput.jsx';
import FloatingTextarea from '../components/ui/form-elements/FloatingTextarea.jsx';

export default function FaqPage({ title, faqs = [] }) {
    const breadcrumbLinks = [{ title }];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container mt-8 px-4 pt-8 pb-18">
                    <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-3">
                        <div className="md:col-span-2">
                            {faqs.length > 0 ? (
                                faqs.map((group, index) => (
                                    <div className="mb-8" key={index}>
                                        <h2 className="mb-4 text-2xl font-semibold md:text-3xl">
                                            {group.title}
                                        </h2>
                                        <Accordion
                                            id={group.id}
                                            items={group.items}
                                        />
                                    </div>
                                ))
                            ) : (
                                <div className="py-8 text-center text-gray-500">
                                    <p>No FAQs available at the moment.</p>
                                </div>
                            )}
                        </div>
                        <div className="md:col-span-1">
                            <div className="w-full">
                                <div className="mb-6">
                                    <h3 className="font-montez mb-1 text-xl font-medium text-green-600">
                                        Need Any Help?
                                    </h3>
                                    <h2 className="mb-4 text-3xl font-medium">
                                        Popular Inquiries
                                    </h2>
                                    <p className="text-sm text-gray-400">
                                        If you need immediate assistance, click
                                        the button below to chat with our
                                        support team.
                                    </p>
                                </div>

                                <div className="mb-6 rounded-xl bg-gray-200 px-4 py-6">
                                    <h3 className="mb-4 text-lg font-medium">
                                        Have any Question?
                                    </h3>
                                    <div className="flex flex-col gap-4">
                                        <FloatingInput
                                            label="Your Name"
                                            type="text"
                                            name="name"
                                        />
                                        <FloatingInput
                                            label="Your Email"
                                            type="email"
                                            name="email"
                                        />
                                        <FloatingTextarea
                                            label="Your Message"
                                            type="text"
                                            name="message"
                                        />
                                        <div>
                                            <Button
                                                size="lg"
                                                className="rounded-lg border-green-600 bg-green-600 py-6 text-[1rem] font-normal text-white"
                                                block
                                            >
                                                Ask Question Now
                                                <FiArrowRight />
                                            </Button>
                                        </div>
                                    </div>
                                </div>

                                <LiveSupportCard />
                            </div>
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
