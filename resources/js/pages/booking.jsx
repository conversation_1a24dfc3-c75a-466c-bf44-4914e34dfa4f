import Breadcrumb from '@/components/shared/Breadcrumb.jsx';
import HeroTopSection from '@/components/shared/HeroTopSection.jsx';
import Alert from '@/components/ui/Alert.jsx';
import Button from '@/components/ui/Button.jsx';
import FloatingInput from '@/components/ui/form-elements/FloatingInput.jsx';
import FloatingSelect from '@/components/ui/form-elements/FloatingSelect.jsx';
import AppLayout from '@/layouts/AppLayout.jsx';
import { useForm } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { FaMoneyBillWave, FaUniversity } from 'react-icons/fa';
import LiveSupportCard from '@/components/shared/LiveSupportCard.jsx';
import { toast } from 'sonner';

export default function Booking({
    title,
    package: packageData,
    countries,
    paymentSettings,
}) {
    const breadcrumbLinks = [
        { name: 'Home', href: '/' },
        { name: 'Packages', href: '/packages' },
        { name: packageData.name, href: '#' },
        { name: 'Book Now', href: '#' },
    ];

    const { data, setData, post, processing, errors, reset } = useForm({
        package_id: packageData.id,
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        whatsapp: '',
        country_id: '',
        booking_date: '',
        extra_notes: '',
        payment_method: 'cash',
        payment_amount: packageData.base_price || 0,
    });

    const [alert, setAlert] = useState(null);

    const paymentMethods = [
        {
            value: 'cash',
            label: 'Cash on Arrival',
            description: 'Pay when you arrive at the destination',
            icon: FaMoneyBillWave,
        },
        {
            value: 'bank_transfer',
            label: 'Bank Transfer',
            description: 'Transfer payment directly to our bank account',
            icon: FaUniversity,
        },
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        setAlert(null);

        post(route('front.booking.store'), {
            onSuccess: () => {
                setAlert({
                    type: 'success',
                    message:
                        'Booking submitted successfully! We will contact you soon to confirm your booking.',
                });
                toast.success(
                    'Booking submitted successfully! We will contact you soon to confirm your booking.',
                );
                reset();
            },
            onError: () => {
                setAlert({
                    type: 'error',
                    message:
                        'Something went wrong. Please check your information and try again.',
                });
            },
        });
    };

    useEffect(() => {
        if (alert) {
            const timer = setTimeout(() => {
                setAlert(null);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [alert]);

    return (
        <AppLayout title={title}>
            <HeroTopSection title={title}>
                <Breadcrumb links={breadcrumbLinks} />
            </HeroTopSection>
            <section className="pb-18 container px-4 pt-8">
                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Booking Form */}
                    <div className="lg:col-span-2">
                        <div className="card bg-base-100 card-md border border-gray-100 shadow-sm">
                            <div className="card-body">
                                <h2 className="text-primary mb-6 text-2xl font-semibold">
                                    Book Your Trip
                                </h2>
                                <form onSubmit={handleSubmit}>
                                    {/* Personal Information */}
                                    <div className="mb-8">
                                        <h3 className="text-primary mb-4 text-lg font-semibold">
                                            Personal Information
                                        </h3>
                                        <div className="grid gap-6 md:grid-cols-2">
                                            <FloatingInput
                                                label="First Name*"
                                                name="first_name"
                                                value={data.first_name}
                                                onChange={(e) =>
                                                    setData(
                                                        'first_name',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.first_name}
                                            />
                                            <FloatingInput
                                                label="Last Name*"
                                                name="last_name"
                                                value={data.last_name}
                                                onChange={(e) =>
                                                    setData(
                                                        'last_name',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.last_name}
                                            />
                                            <FloatingInput
                                                label="Email Address*"
                                                name="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) =>
                                                    setData(
                                                        'email',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.email}
                                            />
                                            <FloatingInput
                                                label="Phone Number*"
                                                name="phone"
                                                value={data.phone}
                                                onChange={(e) =>
                                                    setData(
                                                        'phone',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.phone}
                                            />
                                            <FloatingInput
                                                label="WhatsApp Number"
                                                name="whatsapp"
                                                value={data.whatsapp}
                                                onChange={(e) =>
                                                    setData(
                                                        'whatsapp',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.whatsapp}
                                            />
                                            <FloatingSelect
                                                label="Country*"
                                                name="country_id"
                                                value={data.country_id}
                                                onChange={(e) =>
                                                    setData(
                                                        'country_id',
                                                        e.target.value,
                                                    )
                                                }
                                                error={errors.country_id}
                                            >
                                                <option value="">
                                                    Select Country
                                                </option>
                                                {countries.map((country) => (
                                                    <option
                                                        key={country.id}
                                                        value={country.id}
                                                    >
                                                        {country.name}
                                                    </option>
                                                ))}
                                            </FloatingSelect>
                                        </div>
                                    </div>

                                    {/* Payment Method */}
                                    <div className="mb-8">
                                        <h3 className="text-primary mb-4 text-lg font-semibold">
                                            Payment Method
                                        </h3>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            {paymentMethods
                                                .filter((method) => {
                                                    if (
                                                        method.value === 'cash'
                                                    ) {
                                                        return (
                                                            paymentSettings?.enable_cash_on_arrival ===
                                                            '1'
                                                        );
                                                    }
                                                    if (
                                                        method.value ===
                                                        'bank_transfer'
                                                    ) {
                                                        return (
                                                            paymentSettings?.enable_bank_transfer ===
                                                            '1'
                                                        );
                                                    }
                                                    return true;
                                                })
                                                .map((method) => {
                                                    const IconComponent =
                                                        method.icon;
                                                    return (
                                                        <label
                                                            key={method.value}
                                                            className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${
                                                                data.payment_method ===
                                                                method.value
                                                                    ? 'border-primary bg-primary/5'
                                                                    : 'border-gray-200 hover:border-gray-300'
                                                            }`}
                                                        >
                                                            <input
                                                                type="radio"
                                                                name="payment_method"
                                                                value={
                                                                    method.value
                                                                }
                                                                checked={
                                                                    data.payment_method ===
                                                                    method.value
                                                                }
                                                                onChange={(e) =>
                                                                    setData(
                                                                        'payment_method',
                                                                        e.target
                                                                            .value,
                                                                    )
                                                                }
                                                                className="sr-only"
                                                            />
                                                            <div className="flex items-start gap-3">
                                                                <IconComponent
                                                                    className={`mt-1 text-xl ${
                                                                        data.payment_method ===
                                                                        method.value
                                                                            ? 'text-primary'
                                                                            : 'text-gray-400'
                                                                    }`}
                                                                />
                                                                <div>
                                                                    <h4
                                                                        className={`font-semibold ${
                                                                            data.payment_method ===
                                                                            method.value
                                                                                ? 'text-primary'
                                                                                : 'text-gray-700'
                                                                        }`}
                                                                    >
                                                                        {
                                                                            method.label
                                                                        }
                                                                    </h4>
                                                                    <p className="text-sm text-gray-500">
                                                                        {
                                                                            method.description
                                                                        }
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </label>
                                                    );
                                                })}
                                        </div>

                                        {/* Bank Transfer Details */}
                                        {data.payment_method ===
                                            'bank_transfer' &&
                                            paymentSettings?.enable_bank_transfer ===
                                                '1' && (
                                                <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-6">
                                                    <div className="mb-4 flex items-center">
                                                        <FaUniversity className="mr-2 text-blue-600" />
                                                        <h4 className="text-lg font-semibold text-blue-900">
                                                            Bank Transfer
                                                            Details
                                                        </h4>
                                                    </div>
                                                    <div className="rounded-lg border border-blue-100 bg-white p-4">
                                                        <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                                                            {paymentSettings.bank_name && (
                                                                <div className="flex flex-col">
                                                                    <span className="mb-1 font-medium text-gray-600">
                                                                        Bank
                                                                        Name
                                                                    </span>
                                                                    <span className="font-semibold text-gray-900">
                                                                        {
                                                                            paymentSettings.bank_name
                                                                        }
                                                                    </span>
                                                                </div>
                                                            )}
                                                            {paymentSettings.bank_account_name && (
                                                                <div className="flex flex-col">
                                                                    <span className="mb-1 font-medium text-gray-600">
                                                                        Account
                                                                        Name
                                                                    </span>
                                                                    <span className="font-semibold text-gray-900">
                                                                        {
                                                                            paymentSettings.bank_account_name
                                                                        }
                                                                    </span>
                                                                </div>
                                                            )}
                                                            {paymentSettings.bank_account_number && (
                                                                <div className="flex flex-col">
                                                                    <span className="mb-1 font-medium text-gray-600">
                                                                        Account
                                                                        Number
                                                                    </span>
                                                                    <span className="rounded bg-gray-100 px-2 py-1 font-mono font-semibold text-gray-900">
                                                                        {
                                                                            paymentSettings.bank_account_number
                                                                        }
                                                                    </span>
                                                                </div>
                                                            )}
                                                            {paymentSettings.bank_swift_code && (
                                                                <div className="flex flex-col">
                                                                    <span className="mb-1 font-medium text-gray-600">
                                                                        SWIFT
                                                                        Code
                                                                    </span>
                                                                    <span className="rounded bg-gray-100 px-2 py-1 font-mono font-semibold text-gray-900">
                                                                        {
                                                                            paymentSettings.bank_swift_code
                                                                        }
                                                                    </span>
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Transfer Amount */}
                                                        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
                                                            <div className="flex items-center justify-between">
                                                                <span className="font-medium text-blue-800">
                                                                    Amount to
                                                                    Transfer:
                                                                </span>
                                                                <span className="text-xl font-bold text-blue-900">
                                                                    $
                                                                    {
                                                                        packageData.base_price
                                                                    }
                                                                </span>
                                                            </div>
                                                            <p className="mt-1 text-xs text-blue-600">
                                                                Please transfer
                                                                the exact amount
                                                                shown above
                                                            </p>
                                                        </div>
                                                    </div>

                                                    {paymentSettings.payment_instructions && (
                                                        <div className="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4">
                                                            <h5 className="mb-2 font-medium text-amber-800">
                                                                Important
                                                                Instructions
                                                            </h5>
                                                            <p className="whitespace-pre-line text-sm text-amber-700">
                                                                {
                                                                    paymentSettings.payment_instructions
                                                                }
                                                            </p>
                                                        </div>
                                                    )}

                                                    {/* Contact for Payment Support */}
                                                    <div className="mt-4 rounded-lg border border-green-200 bg-green-50 p-4">
                                                        <h5 className="mb-2 font-medium text-green-800">
                                                            Need Help with
                                                            Payment?
                                                        </h5>
                                                        <p className="text-sm text-green-700">
                                                            Contact us via
                                                            WhatsApp or Email
                                                        </p>
                                                    </div>
                                                </div>
                                            )}

                                        {data.payment_method && (
                                            <div className="mt-4 rounded border border-blue-200 bg-blue-50 p-3">
                                                <p className="text-xs text-blue-700">
                                                    <strong>Note:</strong> By
                                                    proceeding with payment, you
                                                    agree to our
                                                    <a
                                                        href="#"
                                                        className="underline hover:text-blue-900"
                                                    >
                                                        Terms & Conditions
                                                    </a>{' '}
                                                    and
                                                    <a
                                                        href="#"
                                                        className="underline hover:text-blue-900"
                                                    >
                                                        Cancellation Policy
                                                    </a>
                                                    .
                                                </p>
                                            </div>
                                        )}

                                        {errors.payment_method && (
                                            <p className="mt-2 text-sm text-red-600">
                                                {errors.payment_method}
                                            </p>
                                        )}
                                    </div>

                                    {/* Cash on Arrival Details */}
                                    {data.payment_method ===
                                        'cash_on_arrival' &&
                                        paymentSettings?.enable_cash_on_arrival ===
                                            '1' && (
                                            <div className="mb-8 rounded-lg border border-green-200 bg-green-50 p-6">
                                                <div className="mb-4 flex items-center">
                                                    <FaMoneyBillWave className="mr-2 text-green-600" />
                                                    <h4 className="text-lg font-semibold text-green-900">
                                                        Cash on Arrival Payment
                                                    </h4>
                                                </div>

                                                <div className="rounded-lg border border-green-100 bg-white p-4">
                                                    <div className="mb-4">
                                                        <h5 className="mb-2 font-medium text-green-800">
                                                            Payment Details
                                                        </h5>
                                                        <div className="space-y-2 text-sm text-green-700">
                                                            <p>
                                                                💰{' '}
                                                                <strong>
                                                                    Amount to
                                                                    Pay:
                                                                </strong>{' '}
                                                                $
                                                                {
                                                                    packageData.base_price
                                                                }
                                                            </p>
                                                            <p>
                                                                📍{' '}
                                                                <strong>
                                                                    Payment
                                                                    Location:
                                                                </strong>{' '}
                                                                Trip starting
                                                                point
                                                            </p>
                                                            <p>
                                                                ⏰{' '}
                                                                <strong>
                                                                    Payment
                                                                    Time:
                                                                </strong>{' '}
                                                                Upon arrival
                                                                before trip
                                                                begins
                                                            </p>
                                                            <p>
                                                                💵{' '}
                                                                <strong>
                                                                    Accepted
                                                                    Currency:
                                                                </strong>{' '}
                                                                USD, NPR
                                                                (Nepalese
                                                                Rupees)
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <div className="rounded-lg border border-green-200 bg-green-100 p-3">
                                                        <h6 className="mb-1 font-medium text-green-800">
                                                            Important Notes:
                                                        </h6>
                                                        <ul className="space-y-1 text-xs text-green-700">
                                                            <li>
                                                                • Please bring
                                                                exact change or
                                                                be prepared for
                                                                change in local
                                                                currency
                                                            </li>
                                                            <li>
                                                                • Payment must
                                                                be completed
                                                                before the trip
                                                                begins
                                                            </li>
                                                            <li>
                                                                • Receipt will
                                                                be provided upon
                                                                payment
                                                            </li>
                                                            <li>
                                                                • No additional
                                                                fees for cash
                                                                payment
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                {/* Contact Information */}
                                                <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                                                    <h5 className="mb-2 font-medium text-blue-800">
                                                        Questions about Cash
                                                        Payment?
                                                    </h5>
                                                    <div className="text-sm text-blue-700">
                                                        <p>
                                                            📧 Email:
                                                            <EMAIL>
                                                        </p>
                                                        <p>
                                                            📱 Phone:
                                                            +977-9841234567
                                                        </p>
                                                        <p>
                                                            ⏰ Available: 24/7
                                                            for urgent inquiries
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                    {alert && (
                                        <div className="mb-6">
                                            <Alert
                                                type={alert.type}
                                                message={alert.message}
                                            />
                                        </div>
                                    )}

                                    <div className="flex justify-end">
                                        <Button
                                            type="submit"
                                            variant="primary"
                                            size="lg"
                                            className="px-8"
                                            disabled={processing}
                                        >
                                            {processing
                                                ? 'Submitting...'
                                                : 'Submit Booking'}
                                        </Button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    {/* Package Summary */}
                    <div className="lg:col-span-1">
                        <div className="sticky top-8">
                            <div className="card bg-base-100 card-md border border-gray-100 shadow-sm">
                                <div className="card-body">
                                    <h3 className="text-primary mb-4 text-lg font-semibold">
                                        Trip Summary
                                    </h3>
                                    {packageData.image && (
                                        <img
                                            src={packageData.image}
                                            alt={packageData.name}
                                            className="mb-4 h-48 w-full rounded-lg object-cover"
                                        />
                                    )}
                                    <h4 className="mb-2 text-lg font-semibold">
                                        {packageData.name}
                                    </h4>
                                    <p className="mb-4 text-sm text-gray-600">
                                        {packageData.destination?.name} •{' '}
                                        {packageData.duration} days
                                    </p>
                                    <div className="mb-4 text-sm text-gray-600">
                                        <p className="mb-1">
                                            <strong>Location:</strong>{' '}
                                            {packageData.location}
                                        </p>
                                        <p className="mb-1">
                                            <strong>Base Price:</strong> $
                                            {packageData.base_price}
                                        </p>
                                    </div>
                                    <div className="border-t pt-4">
                                        <div className="flex justify-between text-lg font-semibold">
                                            <span>Total Amount:</span>
                                            <span className="text-primary">
                                                ${data.payment_amount}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="mt-6">
                                <LiveSupportCard />
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </AppLayout>
    );
}
