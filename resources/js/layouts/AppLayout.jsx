import '@css/main.css';

import { useLocation } from '@/hooks/navigation.js';
import { Head, usePage } from '@inertiajs/react';
import { twMerge } from 'tailwind-merge';
import Footer from '../components/Footer.jsx';
import Header from '../components/Header.jsx';
import Topbar from '../components/Topbar.jsx';

export default function AppLayout({
    children,
    title,
    meta = null,
    containerClass,
}) {
    const page = usePage();
    const location = useLocation();

    return (
        <>
            <Head
                title={title}
                meta={meta ?? { description: 'Everest Travel' }}
            />
            <div className="absolute top-0 z-10 w-full">
                <Topbar />
                <Header />
            </div>
            <main className={twMerge(containerClass)}>{children}</main>
            <Footer />
        </>
    );
}
