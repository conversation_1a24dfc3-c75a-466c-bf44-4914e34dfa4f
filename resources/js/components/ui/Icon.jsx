export default function Icon({name, width, height, size, ...props}) {

    if (size) {
        height=size;
        width=size;
    }

    if (name=="trekking") {
        return (
            <svg
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 102.16 122.88"
                width={width??48}
                height={height??48}
                {...props}
            >
                <path d="M42.08,68.7a68,68,0,0,0,7.74,4c7.53,3.51,13.72,6.4,15.05,18.23a36.81,36.81,0,0,1,0,6.3v0c0,.32,0,.66-.07,1.46l-.8,16.8c7.24,0,14.23.23,20.39.6,1.39-.32,3.35-1,5.42-1.52L73.63,54.26l-2.39.94h0l-.89.32c-5.19,1.84-8.95,3.17-15.53,1.55a.34.34,0,0,1-.14,0c-4.6-1.78-7-4.26-9.31-6.69L45,50l-3,18.73ZM9.61,29.51l8.36.6c2.12.15,3.49,2.64,3.11,4.93L16.57,62.29c-.36,2.19-10.93,4-13.4,4.62C1.11,67.45-.33,64.27.07,62L5.12,32.92a4.11,4.11,0,0,1,4.49-3.41Zm41,86.17c.13-6.57.35-10.93.72-18l0-1.07a31,31,0,0,0,.07-3.43,8,8,0,0,0-.67-2.88L50.7,90c-.47-1-.9-2-1.54-2.27a54.32,54.32,0,0,0-5.65-2.11c-2.66-.87-5.62-1.76-8.24-2.5C33.35,89.51,30.74,97.2,28,104.52c-2.14,5.6-5,9.15-7.26,13,8.43-1,19.06-1.62,30-1.88ZM5,120.94c2.45-6,6.93-13.38,9.69-21.29,2.84-8.13,5.56-16.42,7.56-23.08a21.28,21.28,0,0,1-3.11-4.89,10.93,10.93,0,0,1-.84-6.45L22,45.44C23.46,34.8,23.74,29.06,35,29.81c10.63.69,15.61,6.29,19.47,10.63,1.71,1.92,3.19,3.58,4.78,4.34.75.36,1.94-.14,3.28-.71a22,22,0,0,1,2.59-1l1.16-.44.67-.25,3.14-1.25-1.29-4.83a3.41,3.41,0,0,1,6.58-1.76l1.09,4.06.15-.06h0a5.06,5.06,0,0,1,4.13.61,7.24,7.24,0,0,1,2.4,2.37,6.58,6.58,0,0,1,1,3.52,8.79,8.79,0,0,1-3,6.1.84.84,0,0,1-.31.19l-.94.37L96.7,114a4.3,4.3,0,0,1,2.31,1.2,14.92,14.92,0,0,1,2.67,4.17l.48,3.52h-99c0-.7.67-1.35,1.85-1.94ZM35.88.69a12.74,12.74,0,1,1-7.33,6.45A12.7,12.7,0,0,1,35.88.69Z"/>
            </svg>
        );
    }

    if (name=="plane") {
        return (
            <svg
                fill="currentColor"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                xmlnsXlink="http://www.w3.org/1999/xlink"
                xmlSpace="preserve"
                width={width??48}
                height={height??48}
                viewBox="0 0 360.031 360.031"
                {...props}
            >
                <g>
                    <path d="M356.354,82.579l-0.774-0.688c-6.684-4.678-16.201-7.14-27.508-7.14c-17.973,0-36.587,6.095-44.982,11.812l-66.16,45.171
                        L87.958,159.128c-1.129,0.246-2.177,0.688-3.134,1.339c-2.504,1.735-3.948,4.687-3.783,7.719
                        c0.258,4.354,3.585,7.815,7.878,8.214l54.542,5.492l-53.164,36.291c-15.829-7.267-30.856-14.316-44.679-20.934
                        c-21.422-10.268-35.407-12.075-41.979-5.5c-6.842,6.846-2.384,18.543,0.057,22.109l51.212,66.396
                        c1.723,2.516,3.702,3.957,6.077,4.39c3.942,0.678,7.365-1.682,10.932-4.21l12.967-8.935l72.037-34.979l-10.542,37.65
                        c-1.156,4.173,0.862,8.503,4.804,10.322c1.12,0.517,2.381,0.786,3.627,0.786c1.778,0,3.48-0.528,4.963-1.519
                        c0.958-0.643,1.726-1.447,2.366-2.42l41.22-62.643l25.214-29.501l111.81-76.333c1.712-1.162,3.381-2.234,5.056-3.315
                        c3.855-2.459,7.5-4.792,10.125-7.44C361.213,96.477,361.537,90.193,356.354,82.579z"/>
                </g>
            </svg>
        );
    }

    if (name=='file-download') {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 113.79 122.88"
                width={width??48}
                height={height??48}
                {...props}
            >
                <path style={{fillRule:'evenodd'}} d="M65.59,67.32h38.82a9.41,9.41,0,0,1,9.38,9.38v36.79a9.41,9.41,0,0,1-9.38,9.39H65.59a9.41,9.41,0,0,1-9.38-9.39V76.7a9.41,9.41,0,0,1,9.38-9.38ZM60,11.56,79.73,30.07H60V11.56ZM20.89,70a2.14,2.14,0,0,0-2,2.23,2.1,2.1,0,0,0,2,2.22H45.67V70Zm0,16a2.14,2.14,0,0,0-2,2.23,2.1,2.1,0,0,0,2,2.23H45.67V85.91Zm0-47.89a2.14,2.14,0,0,0-2,2.23,2.11,2.11,0,0,0,2,2.23H43.81a2.14,2.14,0,0,0,2-2.23,2.11,2.11,0,0,0-2-2.23Zm0-16a2.14,2.14,0,0,0-2,2.23,2.1,2.1,0,0,0,2,2.23h12.6a2.14,2.14,0,0,0,2-2.23,2.11,2.11,0,0,0-2-2.23Zm0,31.93a2.14,2.14,0,0,0-2,2.23,2.12,2.12,0,0,0,2,2.23H59.65a2.14,2.14,0,0,0,2-2.23,2.1,2.1,0,0,0-2-2.23ZM90.72,32.72a3.28,3.28,0,0,0-2.39-3.17L59.23,1.21A3.27,3.27,0,0,0,56.69,0H5.91A5.91,5.91,0,0,0,0,5.91V107.12A5.91,5.91,0,0,0,5.91,113H45.76v-6.6H6.61V6.57H53.37V33.36a3.32,3.32,0,0,0,3.32,3.31H84.12V58.29h6.6V32.72Zm6.45,60.62a2.4,2.4,0,0,1,2.06,1c1.08,1.62-.4,3.22-1.42,4.35-2.91,3.19-9.49,9-10.92,10.66a2.37,2.37,0,0,1-3.72,0c-1.49-1.73-8.43-7.86-11.19-11-1-1.08-2.15-2.56-1.15-4a2.42,2.42,0,0,1,2.07-1h5.17V84.07A2.92,2.92,0,0,1,81,81.15H89.1A2.92,2.92,0,0,1,92,84.07v9.27Z"/>
            </svg>
        );
    }

    return;
}
