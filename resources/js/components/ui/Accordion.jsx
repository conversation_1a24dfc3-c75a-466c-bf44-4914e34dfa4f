import AccordionItem from '@/components/ui/AccordionItem.jsx';
import useHsAccordionInit from '@/hooks/preline/useHsAccordionInit.js';

function Accordion({ items, id }) {
    useHsAccordionInit();

    return (
        <div className="hs-accordion-group" id={id + '-accordion'}>
            {items.map((item, index) => (
                <AccordionItem
                    key={index}
                    title={item.title}
                    id={`${id}-${index}`}
                    content={item.content}
                />
            ))}
        </div>
    );
}

export default Accordion;
