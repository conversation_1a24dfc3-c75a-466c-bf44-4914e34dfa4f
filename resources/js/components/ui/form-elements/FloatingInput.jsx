export default function FloatingInput({ type, id, name, placeholder, label, border = true, value, onChange, error }) {

    const classList = ['peer block w-full rounded-lg bg-white p-4 not-placeholder-shown:pt-6 not-placeholder-shown:pb-2 placeholder:text-transparent autofill:pt-6 autofill:pb-2 focus:border-green-600 focus:pt-6 focus:pb-2 focus:ring-green-600 disabled:pointer-events-none disabled:opacity-50 sm:text-sm'];

    if (border) {
        classList.push('border-gray-200');
    } else {
        classList.push('border-transparent');
    }

    return (
        <div className="relative">
            <input
                type={type}
                id={id ?? name}
                name={name}
                className={classList.join(' ')}
                placeholder={placeholder ?? label}
                value={value || ''}
                onChange={onChange}
            />
            <label
                htmlFor={id ?? name}
                className="pointer-events-none absolute start-0 top-0 h-full origin-[0_0] truncate border border-transparent p-4 transition duration-100 ease-in-out peer-not-placeholder-shown:translate-x-0.5 peer-not-placeholder-shown:-translate-y-1.5 peer-not-placeholder-shown:scale-90 peer-not-placeholder-shown:text-gray-500 text-gray-600 peer-focus:translate-x-0.5 peer-focus:-translate-y-1.5 peer-focus:scale-90 peer-focus:text-gray-500 peer-disabled:pointer-events-none peer-disabled:opacity-50 sm:text-sm"
            >
                {label}
            </label>
            {error && (
                <div className="mt-1 text-sm text-red-600">
                    {error}
                </div>
            )}
        </div>
    );
}
