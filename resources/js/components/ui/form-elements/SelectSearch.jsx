import { useHsSelectAutoInit } from '@/hooks/index.js';
import { twMerge } from 'tailwind-merge';

export default function SelectSearch({
    label,
    id,
    name,
    hasSelect,
    children,
    className,
    leftIcon,
    onSelectChange,
    value
}) {
    const leftIconHtml = leftIcon
        ? `<span class=\"text-gray-400\">${leftIcon}</span>`
        : '';

    const hsSelect = JSON.stringify({
        hasSearch: true,
        searchPlaceholder: 'Search...',
        searchClasses:
            'block w-full sm:text-sm border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 py-1.5 sm:py-2 px-3',
        searchWrapperClasses: 'bg-white p-2 -mx-1 sticky top-0',
        placeholder: `<span class=\"inline-flex items-center text-gray-400\">Select ${label}</span>`,
        toggleTag: `<button type=\"button\" aria-expanded=\"false\">${leftIconHtml}<span class=\"text-gray-500 font-normal\" data-title></span></button>`,
        toggleClasses:
            'hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative p-0 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer border-0 rounded-1 text-start text-sm focus:outline-hidden focus:ring-0',
        dropdownClasses:
            'mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 min-w-[160px]',
        optionClasses:
            'py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100',
        optionTemplate:
            '<div><div class="flex items-center"><div class="me-2" data-icon></div><div class="text-gray-800" data-title></div></div></div>'
    });

    useHsSelectAutoInit();

    return (
        <div className={twMerge('font-roboto w-full', className)}>
            <label htmlFor={id ?? name} className="text-sm font-bold">
                <strong>{label}</strong>
            </label>
            <select
                id={id ?? name}
                name={name}
                data-hs-select={hsSelect}
                className="hidden w-full"
                value={value}
                onChange={onSelectChange}
            >
                {children}
            </select>
        </div>
    );
}
