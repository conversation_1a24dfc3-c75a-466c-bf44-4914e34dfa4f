import { Link } from '@inertiajs/react';
import { FaFacebookF, FaInstagram, FaTwitter } from 'react-icons/fa';

export default function Footer() {
    return (
        <>
            <section className="-z-1 relative w-full">
                <img
                    src="/assets/footer-top.png"
                    alt="Footer"
                    className="min-h-[250px] w-full object-cover object-center"
                />
            </section>

            <footer className="bg-th-green-800 -mt-2 px-4">
                <div className="container pb-12 pt-4">
                    <div className="mb-8 flex flex-col gap-6 lg:flex-row">
                        <div className="lg:w-1/2">
                            <div className="mb-4">
                                <img
                                    src="/assets/everest-sherpa-logo.png"
                                    alt="Logo"
                                    width={120}
                                />
                            </div>
                            <div className="text-white">
                                <h2 className="mb-2 text-lg font-semibold">
                                    T<PERSON><PERSON>, Kathmandu, Nepal
                                </h2>
                                <p className="mb-1 text-sm">
                                    Email:{' '}
                                    <a href="mailto:<EMAIL>">
                                        <EMAIL>
                                    </a>
                                </p>
                                <p className="mb-1 text-sm">
                                    Phone:{' '}
                                    <a href="tel:+9779808762517">
                                        +977 9808762517
                                    </a>
                                </p>
                                <p className="mb-1 text-sm">
                                    WhatsApp:{' '}
                                    <a
                                        href="//wa.me/+9779808762517"
                                        target={'_blank'}
                                    >
                                        +977 9808762517
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div className="flex flex-col gap-4 text-white sm:flex-row lg:w-1/2">
                            <div className="sm:w-2/3 lg:w-auto">
                                <div className="mb-2 text-lg font-bold">
                                    Useful Links
                                </div>
                                <div className="grid gap-1 sm:grid-cols-2 sm:gap-10 md:gap-20">
                                    <div className="flex flex-col gap-1 text-sm">
                                        <Link href="/destinations">
                                            Destinations
                                        </Link>
                                        <Link href="#">Travel Guides</Link>
                                        <Link href="/privacy">
                                            Privacy Policy
                                        </Link>
                                        <Link href="/customized-trip">
                                            Customize Trip
                                        </Link>
                                    </div>
                                    <div className="flex flex-col gap-1 text-sm">
                                        <Link href="#">Write Review</Link>
                                        <Link href="#">Plan Your Trip</Link>
                                        <Link href="/blogs">Blog</Link>
                                        <Link href="/contact">Contact Us</Link>
                                    </div>
                                </div>
                            </div>
                            <div className="w-auto sm:ml-auto">
                                <div className="mb-2 text-lg font-bold">
                                    Company
                                </div>
                                <div className="flex flex-col gap-1 text-sm">
                                    <Link href="/about">About Us</Link>
                                    <Link href="/legal-documents">
                                        Legal Documents
                                    </Link>
                                    <Link href="/booking-and-payments">
                                        Booking And Payments
                                    </Link>
                                    <Link href="/terms">
                                        Terms and Conditions
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr className="my-4 text-gray-100/25" />
                    <div className="flex justify-between text-white max-sm:flex-col max-sm:gap-4 sm:flex-wrap lg:flex-nowrap">
                        <div className="order-1 flex justify-center">
                            <div className="flex items-center justify-center gap-2 max-sm:flex-col">
                                <span>Follow Us:</span>
                                <div className="inline-flex items-center gap-2">
                                    <a
                                        href="#"
                                        className="text-primary rounded-full bg-white p-2"
                                    >
                                        <FaFacebookF />
                                    </a>
                                    <a
                                        href="#"
                                        className="text-primary rounded-full bg-white p-2"
                                    >
                                        <FaTwitter />
                                    </a>
                                    <a
                                        href="#"
                                        className="text-primary rounded-full bg-white p-2"
                                    >
                                        <FaInstagram />
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div className="order-3 flex justify-center">
                            <div className="flex items-center gap-2 max-sm:flex-col">
                                <span className={'max-sm:block'}>
                                    We Accept:
                                </span>
                                <a href="#" className="max-sm:block">
                                    <img
                                        src="/assets/we-accept-credit-cards.png"
                                        alt="Card"
                                    />
                                </a>
                            </div>
                        </div>
                        <div className="order-4 text-center max-lg:w-full lg:order-2">
                            <hr className="my-4 text-gray-100/25 lg:hidden" />
                            <p className="text-sm max-sm:text-xs">
                                &copy;2025 Everest Sherpa Adventure, All Rights
                                Reserved.
                            </p>
                            <p className="mt-2 text-xs">
                                Developed by Perfect Web
                            </p>
                        </div>
                    </div>
                </div>
            </footer>
        </>
    );
}
