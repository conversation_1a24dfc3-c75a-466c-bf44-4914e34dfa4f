import MenuItem from './MenuItem.jsx';

export default function Navigation({ menuItems = [] }) {
    return (
        <>
            {Array.isArray(menuItems) &&
                menuItems.map((item, index) => (
                    <MenuItem title={item.title} link={item.href} key={index}>
                        {item.children &&
                            item.children.length > 0 &&
                            item.children.map((child, childIndex) => (
                                <MenuItem
                                    title={child.title}
                                    link={child.href}
                                    key={childIndex}
                                />
                            ))}
                    </MenuItem>
                ))}
        </>
    );
}
