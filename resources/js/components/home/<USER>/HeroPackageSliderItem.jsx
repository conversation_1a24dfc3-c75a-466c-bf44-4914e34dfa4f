export default function HeroPackageSliderItem({
                                                  imgUrl,
                                                  imgAlt,
                                                  packageName,
                                                  destination,
                                                  scale = false // Optional prop to control scaling
                                              }) {
    /* const scaleClass = scale
        ? 'h-[375px] max-h-[calc(50%+20px)] w-[215px]'
        : 'h-[330px] max-h-1/2 w-[195px]'; */

    return (
        <div className="slide-card-container p-3">
            <div
                className={`slide-card-shadow relative h-full w-full overflow-hidden rounded-lg`}
            >
                <img
                    src={imgUrl}
                    alt={imgAlt ?? packageName}
                    className="h-full w-full object-cover object-center"
                />
                <img
                    src="/assets/abstract-background-sm-1.png"
                    alt={imgAlt ?? packageName}
                    className="absolute bottom-0 left-0 w-full object-cover object-left"
                />
                <div className="absolute bottom-0 left-0 w-full px-3 pb-2">
                    <p className="text-truncate mb-0 text-sm leading-[1]">
                        {packageName}
                    </p>
                    <h2 className="font-myriad text-xl font-bold">
                        {destination}
                    </h2>
                </div>
            </div>
        </div>
    );
}
