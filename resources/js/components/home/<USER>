import { packages } from '@/data/dummy';
import { FiExternalLink } from 'react-icons/fi';
import PackageCard from '../package/PackageCard.jsx';

export default function PopularPackages() {
    const styles = {
        '--bg-graphics1': `url("/assets/bg-graphics-1.png")`,
        '--bg-graphics2': `url("/assets/bg-graphics-3.png")`,
    };

    return (
        <section
            className="popular-package-section md:pb-50 relative pb-40"
            style={styles}
        >
            <div className="container px-4 py-8">
                <div className="py-4">
                    <p className="ff-montez mb-2 text-2xl md:text-3xl">
                        Unique and Exclusive!
                    </p>
                    <div className="flex justify-between max-sm:flex-col">
                        <h2 className="ff-myrd-web mb-3 text-2xl sm:text-3xl md:text-5xl">
                            Popular Packages for 2025
                        </h2>
                        <a
                            href="#popular-packages"
                            className="inline-flex items-center gap-2 max-sm:text-sm"
                        >
                            View More
                            <FiExternalLink size={16} />
                        </a>
                    </div>
                </div>
                {/*<div className="grid gap-1 md:grid-cols-3 lg:-mx-4 xl:grid-cols-4">*/}
                <div className="grid grid-cols-[repeat(auto-fit,_minmax(275px,_1fr))] gap-1 lg:-mx-4">
                    {packages.length > 0 &&
                        packages.map((_package, index) => (
                            <PackageCard
                                key={index}
                                title={_package.title}
                                price={_package.price}
                                oldPrice={_package.oldPrice}
                                duration={_package.duration}
                                rating={_package.rating}
                                ratingCount={_package.ratingCount}
                                imgSrc={_package.imgSrc}
                                destination={_package.destination}
                                packageType={_package.packageType}
                            />
                        ))}
                </div>
            </div>
        </section>
    );
}
