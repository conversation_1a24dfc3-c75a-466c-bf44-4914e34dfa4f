import { RiDoubleQuotesR } from 'react-icons/ri';
import { TiStar } from 'react-icons/ti';

export default function TestimonialCard({ isActive = false }) {
    return (
        <div
            className={`review-container relative ${isActive ? 'mt-0' : 'mt-10'} p-3`}
        >
            <div className="relative pl-6">
                <img
                    src="/assets/review_profile.png"
                    className="size-[100px] rounded-full shadow-sm"
                    alt="Avatar"
                />
            </div>
            <div className="review-body bg-th-greenish-100 -mt-[50px] w-[320px] rounded-3xl p-6 shadow-md md:w-[450px]">
                <div className="-mt-1 pl-[120px]">
                    <div className="flex gap-0">
                        <TiStar className={'size-5'} />
                        <TiStar className={'size-5'} />
                        <TiStar className={'size-5'} />
                        <TiStar className={'size-5'} />
                        <TiStar className={'size-5'} />
                    </div>
                    <div>
                        <p className="text-lg font-bold md:text-xl">
                            <PERSON>
                        </p>
                        <p className="text-sm font-bold text-gray-400">
                            Netherland
                        </p>
                    </div>
                </div>
                <div className="mt-4 mb-8 text-sm font-semibold">
                    <p className="mb-1">
                        I had a great time, Rinje Sherpa was our guide! He
                        assure us the best experience we could have there. Fun,
                        safe and sporty!
                    </p>
                    <p>
                        A great friend nowadays! About the trek, is really
                        beautiful, I totally reccommend the experience! Do not
                        doubt it!
                    </p>
                </div>
            </div>
            <div className="-mt-10 flex justify-center">
                <div className="rounded-full bg-primary p-4 text-white shadow-sm">
                    <RiDoubleQuotesR size={48} />
                </div>
            </div>
        </div>
    );
}
