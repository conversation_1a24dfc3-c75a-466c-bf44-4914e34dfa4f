import { FaCirclePlay } from 'react-icons/fa6';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import HeroPackageSlider from './slider/HeroPackageSlider.jsx';

export default function HeroSection({ children, settings = {} }) {
    const bgImg = '/assets/hero-bg.png';
    const bgAbstract = '/assets/abstract-purple-watercolor-background.png';
    const sectionStyle = {
        '--bg-img': `url(${bgImg})`,
        '--bg-abstract': `url(${bgAbstract})`,
    };

    return (
        <section
            className="landing-hero-section relative mt-8 h-auto w-full overflow-hidden md:h-[80vh]"
            style={sectionStyle}
        >
            <div className="left-0 top-0 m-0 h-full w-full p-0 text-white max-md:mb-[70px] md:absolute md:min-h-[80vh]">
                <div className="max-md:mt-24! container flex h-full items-center px-4 md:mt-0 md:px-6">
                    <div className="md:w-1/2 md:pr-[75px]">
                        <p className="font-montez hero-pretitle mb-6 text-3xl md:text-2xl lg:text-4xl">
                            {settings.pretitle || 'Get unforgettable pleasure with us.'}
                        </p>
                        <h1 className="font-myriad hero-lead mb-6 text-5xl font-bold uppercase leading-[1] md:text-5xl lg:text-[4rem]">
                            {settings.title ? (
                                <span dangerouslySetInnerHTML={{ __html: settings.title }} />
                            ) : (
                                <>Let&apos;s travel <br /> together</>
                            )}
                        </h1>
                        <p className="hero-text text-lg font-normal leading-7 lg:text-xl">
                            {settings.description || 'Exploring new cultures, creating unforgettable memories, and sharing the adventure every step of the way.'}
                        </p>
                        <div className="mt-8">
                            <a 
                                href={settings.button_link || '#'} 
                                className="btn btn-primary btn-lg"
                            >
                                {settings.button_text || 'Explore Trek'}
                                <MdKeyboardDoubleArrowRight className="size-6" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div className="right-0 top-0 flex h-full w-full items-center overflow-hidden max-md:relative md:absolute md:w-1/2 md:pl-[120px]">
                <HeroPackageSlider />
            </div>
            <button className="absolute left-1/2 top-1/2 size-[75px] -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full text-white/50 shadow-2xl hover:text-white/75 hover:shadow-lg max-md:mt-0">
                <FaCirclePlay className="h-full w-full" />
            </button>
            {children}
        </section>
    );
}
