import { SplideSlide } from '@splidejs/react-splide';
import DragButton from '../ui/DragButton.jsx';
import DragSlider from '../ui/DragSlider.jsx';
import TravelExperienceVideo from './TravelExperienceVideo.jsx';

export default function TravelExperience() {
    const videos = Array(4).fill({
        thumbSrc: '/assets/video-thumb.png',
        title: 'EBC - 8 Days Trek',
        name: 'Alex & Maria',
        country: 'Netherland',
    });

    return (
        <section className="bg-th-greenish-500">
            <div className="container px-4 py-8">
                <div className="py-4">
                    <p className="ff-montez mb-2 text-2xl md:text-3xl">
                        Travel's Experience
                    </p>
                    <div className="flex justify-between max-sm:flex-col">
                        <h2 className="ff-myrd-web mb-3 text-2xl sm:text-3xl md:text-5xl">
                            Travel Video Experiences
                        </h2>
                        {/*<a
                            href="#popular-packages"
                            className="inline-flex items-center gap-2 max-sm:text-sm"
                        >
                            View More
                            <FiExternalLink size={16} />
                        </a>*/}
                    </div>
                </div>
                <div className="relative">
                    <div className="flex gap-4">
                        <DragSlider perPage={3.7} label={'Travel Experiences'}>
                            {videos.length &&
                                videos.map((item, index) => (
                                    <SplideSlide key={index}>
                                        <TravelExperienceVideo
                                            key={index}
                                            thumbSrc={item.thumbSrc}
                                            title={item.title}
                                            desc={`${item.name} - ${item.country}`}
                                        />
                                    </SplideSlide>
                                ))}
                        </DragSlider>
                    </div>
                    <DragButton />
                </div>
            </div>
        </section>
    );
}
