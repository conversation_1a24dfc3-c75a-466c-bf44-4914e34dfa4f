import { SplideSlide } from '@splidejs/react-splide';
import DragSlider from '../ui/DragSlider.jsx';

export default function ReviewAndBrands() {
    const positions = [
        'object-[0_0]',
        'object-[-8rem_0]',
        'object-[-16.5rem_0]',
        'object-[-24.75rem_0]',
        'object-[-32.75rem_0]',
        'object-[-41.25rem_0]',
        'object-[-49.5rem_0]',
        'object-[-57.75rem_0]',
    ];

    return (
        <section>
            <div className="container">
                <div className="flex flex-wrap items-center justify-center gap-8 py-10">
                    <img
                        src="/assets/Google-Review-Logo.png"
                        alt="Google Review"
                        className="h-20"
                    />
                    <img
                        src="/assets/trustpilot-logo.png"
                        alt="Trustpilot"
                        className="h-18"
                    />
                </div>
                <div className="flex gap-6 md:px-8">
                    <DragSlider
                        label={'Brands'}
                        autoplay={true}
                        isCentered={true}
                        interval={2000}
                        gap={'4rem'}
                    >
                        {positions.map((positionClass, index) => (
                            <SplideSlide key={index}>
                                <img
                                    src="/assets/brands.png"
                                    alt="brands"
                                    className={`h-20 w-22 object-cover ${positionClass}`}
                                />
                            </SplideSlide>
                        ))}
                    </DragSlider>
                </div>
            </div>
        </section>
    );
}
