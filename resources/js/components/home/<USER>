import { packages } from '@/data/dummy';
import { SplideSlide } from '@splidejs/react-splide';
import PackageCard from '../package/PackageCard.jsx';
import DragButton from '../ui/DragButton.jsx';
import DragSlider from '../ui/DragSlider.jsx';

export default function BestOffers() {
    return (
        <section className="best-offers-section relative pb-6">
            <div className="-z-1 absolute left-0 top-0 h-1/2 min-h-80 w-full overflow-hidden bg-[#0A5333]">
                <img
                    className="-z-1 translate-y-1/5 absolute -bottom-[1px] left-0 w-full min-w-[1600px] object-cover object-center"
                    alt=""
                    src="/assets/abstract-purple-watercolor-background.png"
                ></img>
            </div>
            <div className="container px-4 py-2">
                <div className="py-4 text-center">
                    <p className="ff-montez mb-2 text-2xl text-white md:text-3xl">
                        Unique and Exclusive!
                    </p>
                    <h2 className="ff-myrd-web mb-3 text-4xl text-white md:text-5xl">
                        Discover our Best Offers!
                    </h2>
                </div>
                <div className="relative">
                    <div className="flex gap-6">
                        <DragSlider label="Best Offers">
                            {packages.length > 0 &&
                                packages.map((_package, index) => (
                                    <SplideSlide key={index}>
                                        <PackageCard
                                            key={index}
                                            title={_package.title}
                                            price={_package.price}
                                            oldPrice={_package.oldPrice}
                                            duration={_package.duration}
                                            rating={_package.rating}
                                            ratingCount={_package.ratingCount}
                                            imgSrc={_package.imgSrc}
                                            destination={_package.destination}
                                            packageType={_package.packageType}
                                            forSlider={true}
                                        />
                                    </SplideSlide>
                                ))}
                        </DragSlider>
                    </div>
                    <DragButton />
                </div>
            </div>
        </section>
    );
}
