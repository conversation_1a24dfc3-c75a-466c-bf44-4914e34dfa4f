import { FaPlay } from 'react-icons/fa';

export default function TravelExperienceVideo({ thumbSrc, title, desc }) {
    return (
        <div className="my-2 w-full max-w-[300px] p-2">
            <div className="relative aspect-[1/1.1] w-full overflow-hidden rounded-tl-[8rem] rounded-br-[8rem] bg-white p-0 shadow-lg">
                <div className="mb-3 h-full w-full">
                    <img
                        src={thumbSrc}
                        alt={title}
                        className="h-full w-full object-cover object-center"
                    />
                </div>
                <img
                    src="/assets/abstract-text-bg-2.png"
                    alt="Background"
                    className="absolute bottom-0 left-1/2 min-w-[550px] -translate-x-1/2 translate-y-1/3"
                />
                <div className="absolute bottom-0 left-0 w-full px-4 pb-4">
                    <div className="-mb-1 flex justify-end pr-2">
                        <button className="cursor-pointer rounded-full bg-slate-950 p-2 text-white shadow-sm shadow-white">
                            <FaPlay className="size-5" />
                        </button>
                    </div>
                    <h3 className="text-md truncate font-bold">{title}</h3>
                    <p className="text-xs font-semibold">{desc}</p>
                </div>
            </div>
        </div>
    );
}
