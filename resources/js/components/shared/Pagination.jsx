import { Link } from '@inertiajs/react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export default function Pagination({ data }) {
    if (!data || data.last_page <= 1) {
        return null;
    }

    return (
        <div className="mt-8 flex items-center justify-between">
            <div className="text-sm text-gray-600">
                Showing {data.from} to {data.to} of {data.total} results
            </div>
            <div className="flex items-center space-x-2">
                {data.prev_page_url && (
                    <Link
                        href={data.prev_page_url}
                        className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 transition-colors hover:bg-gray-50 hover:text-gray-700"
                    >
                        <ChevronLeft className="mr-1 h-4 w-4" />
                        Previous
                    </Link>
                )}

                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                    {data.links
                        .filter(
                            (link) =>
                                link.label !== '&laquo; Previous' &&
                                link.label !== 'Next &raquo;',
                        )
                        .map((link, index) => {
                            if (link.url === null) {
                                return (
                                    <span
                                        key={index}
                                        className="px-3 py-2 text-sm text-gray-400"
                                        dangerouslySetInnerHTML={{
                                            __html: link.label,
                                        }}
                                    />
                                );
                            }

                            return (
                                <Link
                                    key={index}
                                    href={link.url}
                                    className={`rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                                        link.active
                                            ? 'bg-primary text-white'
                                            : 'border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700'
                                    }`}
                                    dangerouslySetInnerHTML={{
                                        __html: link.label,
                                    }}
                                />
                            );
                        })}
                </div>

                {data.next_page_url && (
                    <Link
                        href={data.next_page_url}
                        className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 transition-colors hover:bg-gray-50 hover:text-gray-700"
                    >
                        Next
                        <ChevronRight className="ml-1 h-4 w-4" />
                    </Link>
                )}
            </div>
        </div>
    );
}
