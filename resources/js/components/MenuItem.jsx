import React from 'react';
import { Link } from '@inertiajs/react';

export default function MenuItem({ link, title, children, mobile = false }) {
    const hasChildren = React.Children.count(children) > 0;

    if (mobile) {
        return (
            <li>
                {hasChildren > 0 ? (
                    <ul className="p-2">
                        <Link href={link}>{title}</Link>
                        {children}
                    </ul>
                ) : (
                    <Link href={link}>{title}</Link>
                )}
            </li>
        );
    }

    return (
        <li>
            {hasChildren > 0 ? (
                <details>
                    <summary>{title}</summary>
                    <ul className="min-w-max p-2">{children}</ul>
                </details>
            ) : (
                <Link href={link}>{title}</Link>
            )}
        </li>
    );
}
