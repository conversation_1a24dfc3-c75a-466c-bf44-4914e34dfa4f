import { Link } from '@inertiajs/react';
import { FaStar } from 'react-icons/fa6';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import { TbCalendarClock } from 'react-icons/tb';

export default function PackageCard(props) {
    const {
        image,
        name: title,
        destination,
        activities,
        base_price: price,
        oldPrice,
        duration,
        reviews_count,
        average_rating,
        href,
        forSlider = false,
    } = props;

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    console.log(props);

    return (
        <div
            className={`my-2 ${forSlider ? 'max-w-[300px]' : 'max-w-[350px]! min-w-[275px]'} group p-2 transition duration-300`}
        >
            <div className="w-full rounded-2xl border border-gray-50 bg-white p-3 shadow-lg hover:scale-[101%]">
                <div className="mb-3 w-full overflow-hidden rounded-xl">
                    <img
                        src={
                            image
                                ? `/storage/${image}`
                                : '/assets/img-landscape.png'
                        }
                        alt={title}
                        className="aspect-[3/2] w-full rounded-xl object-cover object-center transition duration-300 group-hover:scale-105"
                    />
                </div>
                <div>
                    <span className="bg-th-green-100 text-primary rounded-full px-4 py-1 text-xs font-bold">
                        {destination}{' '}
                        {activities && activities[0] && activities[0].name && (
                                <> &rarr; {activities[0].name} </>
                            )}
                    </span>
                </div>
                <h3
                    className="ff-myrd mt-4 truncate text-xl font-bold text-black"
                    title={title}
                >
                    <Link href={href}>{title}</Link>
                </h3>
                <div className="my-2 flex items-center gap-4">
                    <div className="flex items-center gap-1">
                        <TbCalendarClock className="size-5 text-slate-700" />
                        <span className="text-sm font-semibold text-slate-800">
                            {duration} {duration > 1 ? 'days' : 'day'}
                        </span>
                    </div>
                    <div className="flex items-center gap-1">
                        <FaStar className="size-4 text-slate-700" />
                        <span className="text-sm font-semibold text-slate-800">
                            {average_rating}{' '}
                            <span className="text-slate-500">
                                ({reviews_count})
                            </span>
                        </span>
                    </div>
                </div>
                <div className="mb-2 mt-3 flex items-end justify-between">
                    <div className="mb-0">
                        {oldPrice ? (
                            <div className="text-md">
                                <span className="font-normal text-slate-500 line-through">
                                    ${oldPrice}
                                </span>
                                <span className="font-semibold">/person</span>
                            </div>
                        ) : (
                            <div className="text-md">
                                <span className="font-normal text-transparent line-through">
                                    0
                                </span>
                            </div>
                        )}
                        <span className="font-bold">
                            <span className="text-xl">${price}</span>
                            {!oldPrice && (
                                <span className="!text-md font-semibold">
                                    /person
                                </span>
                            )}
                        </span>
                    </div>
                    <div className="">
                        <Link
                            href={href}
                            className="btn btn-primary rounded-full px-3 py-2 text-sm font-semibold"
                        >
                            Book Now
                            <MdKeyboardDoubleArrowRight className="size-4" />
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}
