import { useState } from 'react';
import { TbStar } from 'react-icons/tb';
import { twMerge } from 'tailwind-merge';

export default function ReviewRatingInput({ onInput, value = 0, className }) {
    const [hovered, setHovered] = useState(0);

    const isActive = (index) => {
        if (hovered > 0) {
            return hovered > index;
        }
        return value > index;
    };

    return (
        <div className={twMerge('group/star flex gap-0', className)}>
            {Array(5)
                .fill(null)
                .map((_, index) => (
                    <button
                        onMouseOver={() => setHovered(index + 1)}
                        onMouseLeave={() => setHovered(0)}
                        className="px-0.5"
                        key={index}
                    >
                        <TbStar
                            className={`size-6 ${isActive(index) ? 'fill-yellow-500 text-yellow-500' : 'text-gray-500'}`}
                            key={index}
                            onClick={() => onInput(index + 1)}
                        />
                    </button>
                ))}
        </div>
    );
}
