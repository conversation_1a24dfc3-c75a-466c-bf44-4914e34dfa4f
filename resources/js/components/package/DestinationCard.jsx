import { Link } from '@inertiajs/react';

export default function DestinationCard({ name, image, slug, packages }) {
    return (
        <div className="relative overflow-hidden rounded-xl border border-gray-100 shadow-md">
            <img
                src={image ? `/storage/${image}` : '/assets/img-landscape.png'}
                alt={name}
                className="-z-1 absolute left-0 top-0 h-full w-full object-cover object-center"
            />
            <Link href={`/activities/${slug}`}>
                <div className="aspect-[5/3] h-full w-full rounded-xl">
                    <div className="absolute bottom-0 left-0 w-full px-4 pb-1 text-center text-lg font-bold">
                        {name}
                    </div>
                    <div className="bg-primary absolute right-0 top-0 rounded-bl-xl p-3 font-medium text-white shadow-sm">
                        {packages ?? 0} packages
                    </div>
                </div>
            </Link>
            <img
                src="/assets/destinations/abstract-bg.png"
                alt={name}
                className="w-min-[calc(100%+10px)] -z-1 w-[700px]! object-bottom-left absolute -bottom-1 left-0 overflow-hidden"
            />
        </div>
    );
}
