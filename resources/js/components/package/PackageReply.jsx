import { FaStar } from 'react-icons/fa';
import { MdReply } from 'react-icons/md';

export default function PackageReply({
    name = '<PERSON><PERSON>',
    rating = 5,
    comment = 'Mondy of January is supposed to be the most depressing day of the year. Whether you believe that or not, the long nights cold weather, and trying.',
    date,
    avatarUrl = 'https://picsum.photos/id/550/300/300',
}) {
    return (
        <div className="flex gap-5">
            <div className="aspect-square size-24 shrink-0 overflow-hidden rounded-lg">
                <img
                    src={avatarUrl}
                    alt="avatar"
                    className="size-full object-cover"
                />
            </div>
            <div>
                <div className="float-right! text-green-600">
                    <span className="inline-flex items-center gap-1">
                        <MdReply className="size-5" />
                        Reply
                    </span>
                </div>
                <div className="flex flex-col">
                    <div className="mb-1 flex gap-0.5 text-yellow-500">
                        {[...Array(5)].map((_, i) => (
                            <FaStar
                                key={i}
                                className={`size-4 ${
                                    i < rating
                                        ? 'text-yellow-500'
                                        : 'text-gray-300'
                                }`}
                            />
                        ))}
                    </div>
                    <h3 className="mb-1 text-xl font-[600]">{name}</h3>
                    {date && (
                        <p className="mb-1 text-xs text-gray-400">{date}</p>
                    )}
                    <p className="text-sm text-gray-500">{comment}</p>
                </div>
            </div>
        </div>
    );
}
