/* Tailwind Core */
@import 'tailwindcss';

/* <PERSON><PERSON> Configs */
@import './config.reui.css';

/** Global Styles **/
@layer base {
  * {
    @apply border-border;
  }

  *:focus-visible {
    @apply outline-ring rounded-xs shadow-none outline-2 outline-offset-3 transition-none!;
  }
}

/* Metronic Components  */
@import './components/image-input.css';
@import './components/apexcharts.css';
@import './components/leaflet.css';
@import './components/rating.css';
@import './components/image-input.css';
@import './components/scrollable.css';

/* Demos */
@import './demos/demo1.css';

/** Dark/Light Theme Variant **/
@custom-variant dark (&:is(.dark *));
@custom-variant light (&:not(.dark *));
