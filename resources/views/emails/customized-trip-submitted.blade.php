<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Customized Trip Request</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .field-value {
            color: #212529;
        }
        .footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Customized Trip Request</h1>
        <p>A new customized trip request has been submitted on your website.</p>
    </div>

    <div class="content">
        <h2>Customer Information</h2>
        
        <div class="field">
            <div class="field-label">Full Name:</div>
            <div class="field-value">{{ $customizedTrip->full_name }}</div>
        </div>

        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">{{ $customizedTrip->email }}</div>
        </div>

        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{ $customizedTrip->phone }}</div>
        </div>

        <div class="field">
            <div class="field-label">Country:</div>
            <div class="field-value">{{ $customizedTrip->country->name ?? 'N/A' }}</div>
        </div>

        <h2>Trip Details</h2>

        @if($customizedTrip->package)
        <div class="field">
            <div class="field-label">Selected Package:</div>
            <div class="field-value">{{ $customizedTrip->package->name }}</div>
        </div>
        @endif

        @if($customizedTrip->travel_date)
        <div class="field">
            <div class="field-label">Travel Date:</div>
            <div class="field-value">{{ \Carbon\Carbon::parse($customizedTrip->travel_date)->format('F j, Y') }}</div>
        </div>
        @endif

        @if($customizedTrip->trip_duration)
        <div class="field">
            <div class="field-label">Trip Duration:</div>
            <div class="field-value">{{ $customizedTrip->trip_duration }} days</div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Number of Adults:</div>
            <div class="field-value">{{ $customizedTrip->number_of_adults }}</div>
        </div>

        <div class="field">
            <div class="field-label">Number of Children:</div>
            <div class="field-value">{{ $customizedTrip->number_of_children }}</div>
        </div>

        @if($customizedTrip->estimated_budget)
        <div class="field">
            <div class="field-label">Estimated Budget:</div>
            <div class="field-value">${{ number_format($customizedTrip->estimated_budget, 2) }}</div>
        </div>
        @endif

        @if($customizedTrip->notes)
        <div class="field">
            <div class="field-label">Additional Notes:</div>
            <div class="field-value">{{ $customizedTrip->notes }}</div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Submitted At:</div>
            <div class="field-value">{{ $customizedTrip->created_at->format('F j, Y \\a\\t g:i A') }}</div>
        </div>
    </div>

    <div class="footer">
        <p>You can manage this request from your admin panel.</p>
        <p>This is an automated email from your website.</p>
    </div>
</body>
</html>